import React, { Suspense, useContext, useState } from 'react'
import { Outlet, useLocation, useNavigate } from 'react-router-dom'
import { Typography, Layout, Nav, Modal, Button, Tooltip, Dropdown } from '@douyinfe/semi-ui';
import { IconMoon, IconSun, IconColorPalette, IconSetting, IconServer, IconSafe, IconArticle, IconHelpCircle, IconGlobeStroke, IconMenu, IconDownload, IconInfoCircle, IconComponent, IconAppCenter, IconUserGroup, IconLanguage } from '@douyinfe/semi-icons';
import { LocaleFormatter, useLocale } from '@/locales';
import Activation from '@/components/activation';
import { UserRole } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb';

import UserIndicator from '@/components/user-indicator';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import styles from './index.module.scss';
import { BASE_PATH } from '@/constants/router';
import { VITE_LOCAL_PAGER_AND_FILTER } from '@/utils/service';
import CustomerService from '@/components/customer-service';
import Renew from '@/components/renew';
const { Text } = Typography;
import moment from 'moment'
import { GlobalConfigContext } from '@/hooks/useGlobalConfig';
import ThemeSwitch from '@/components/theme-switch';
import { useGlobalTheme, GlobalThemeContext } from '@/hooks/useGlobalTheme';
import { useLocaleSwitch } from '@/hooks/useLocale';
const { Header, Content } = Layout;
import { LicenseContext } from '@/hooks/useLicense';

const Index: React.FC = () => {
    // 用户登录信息

    const license = useContext(LicenseContext);
    const flynet = useContext(FlynetGeneralContext);
    const globalConfig = useContext(GlobalConfigContext);
    const { colorMode, theme, setTheme, switchColorMode } = useGlobalTheme();
    const navigate = useNavigate();
    const location = useLocation();

    // 语言切换相关
    const { locale, switchLocale } = useLocaleSwitch();
    const { formatMessage } = useLocale();

    let pathName = location.pathname.replace(BASE_PATH, '');
    // 取第一级路径
    if (pathName.split('/').length > 2) {
        pathName = `/${pathName.split('/')[1]}`;
    }

    // 邀请码对话框是否显示
    const [inviteCodeModalVisible, setInviteCodeModalVisible] = useState(false)
    // 主题切换对话框是否显示
    const [themeSwitchVisible, setThemeSwitchVisible] = useState(false)

    let expireStr = '';

    // 判断是否过期
    if (!license.disabledExpiry && license.expiredAt) {
        const expiry = license.expiredAt;

        const now = new Date()

        if (!license.expired) {
            const expireTimes = expiry.getTime() - now.getTime()
            if (expireTimes < 1000 * 60 * 60 * 24 * 30) {
                var localLocale = moment(expiry);
                // 根据当前语言设置moment的locale
                localLocale.locale(locale === 'zh_CN' ? 'zh-cn' : 'en');
                let timeStr = localLocale.fromNow(true);

                if (locale === 'zh_CN') {
                    expireStr = timeStr
                        .replace('days', '天').replace('a day', '1天')
                        .replace('a year', '1年').replace('years', '年')
                        .replace('a month', '1个月')
                        .replace('months', '个月')
                        .replace('hours', '小时')
                        .replace('an hour', '1小时')
                        .replace('a minute', '1分钟')
                        .replace('minutes', '分钟')
                        .replace('a few', '1')
                        .replace('seconds', '秒').replace(' ', '');
                    expireStr = `${expireStr}后到期`;
                } else {
                    expireStr = `Expires in ${timeStr}`;
                }
            }

        } else {
            expireStr = locale === 'zh_CN' ? '已过期' : 'Expired';
        }
    }


    // 移动端菜单下拉标识
    const [mobileMenuVisible, setMobileMenuVisible] = React.useState(false);

    // 客服图标是否显示
    const [customerSupportVisible, setCustomerSupportVisible] = React.useState(false);

    // Logo点击事件
    const onLogoClick = () => {
        setMobileMenuVisible(false);
        navigate(`${BASE_PATH}`)
    }

    return <><Layout className={styles.layout}>

        <Header className={styles.header}>
            <div className={mobileMenuVisible ? 'mobileMenuVisible' : ''}>
                <Nav mode="horizontal" className={styles.headerNav} selectedKeys={[pathName]}>
                    <Nav.Header>
                        <div className={styles.logo} ><img src={globalConfig.logo} onClick={onLogoClick}></img>
                            <span className={styles.logoText} onClick={onLogoClick}>{globalConfig.name}</span>
                            {(flynet.userRole == UserRole.SUPER_ADMIN.toString() || flynet.userRole == UserRole.FLYNET_ADMIN.toString() || flynet.userRole == UserRole.FLYNET_OWNER.toString()) && (license.activated && !license.expired && expireStr) &&
                                <span className={styles.logoBadge} onClick={() => {
                                    setInviteCodeModalVisible(true)
                                }}>{expireStr}</span>}
                        </div>
                    </Nav.Header>
                    <Nav.Item itemKey="/overview" onClick={() => navigate(`${BASE_PATH}/overview`)} text={<LocaleFormatter id="nav.overview" />} icon={<IconAppCenter />} />
                    {/* <Nav.Item itemKey="/networks" onClick={() => navigate(`${BASE_PATH}/networks`)} text="网络" icon={<IconGlobe />} />*/}
                    {!VITE_LOCAL_PAGER_AND_FILTER && <Nav.Item itemKey="/services" onClick={() => navigate(`${BASE_PATH}/services`)} text={<LocaleFormatter id="nav.services" />} icon={<IconComponent />} />}
                    {false ?
                        <Nav.Item itemKey="/policies" onClick={() => { setMobileMenuVisible(false); navigate(`${BASE_PATH}/policies/acl`) }} text={<LocaleFormatter id="nav.policies" />} icon={<IconSafe />} /> :
                        <Nav.Item itemKey="/policies" onClick={() => { setMobileMenuVisible(false); navigate(`${BASE_PATH}/policies`) }} text={<LocaleFormatter id="nav.policies" />} icon={<IconSafe />} />
                    }

                    <Nav.Item itemKey="/devices" onClick={() => { setMobileMenuVisible(false); navigate(`${BASE_PATH}/devices`) }} text={<LocaleFormatter id="nav.devices" />} icon={<IconServer />} />
                    <Nav.Item itemKey="/users" onClick={() => { setMobileMenuVisible(false); navigate(`${BASE_PATH}/users`) }} text={<LocaleFormatter id="nav.users" />} icon={<IconUserGroup />} />
                    {/* <Popover showArrow={true} content='即将上线'>
                    </Popover> */}
                    {/* <Nav.Item itemKey="/dns" onClick={() => { setMobileMenuVisible(false); navigate(`${BASE_PATH}/dns`) }} text="域名解析" icon={<IconGlobeStroke />} /> */}
                    <Nav.Item itemKey="/resources" onClick={() => { setMobileMenuVisible(false); navigate(`${BASE_PATH}/resources`) }} text={<LocaleFormatter id="nav.resources" />} icon={<IconGlobeStroke />} />
                    <Nav.Item itemKey="/logs" onClick={() => { setMobileMenuVisible(false); navigate(`${BASE_PATH}/logs/audit`) }} text={<LocaleFormatter id="nav.logs" />} icon={<IconArticle />} />
                    <Nav.Item itemKey="/settings" onClick={() => { setMobileMenuVisible(false); navigate(`${BASE_PATH}/settings`) }} text={<LocaleFormatter id="nav.settings" />} icon={<IconSetting />} />
                    <Nav.Footer>
                        {/* <a href={`${BASE_PATH}/download`} className={styles.navLink}>下载</a>    */}

                        {/* {globalConfig.flylayer && <IconCustomerSupport onClick={()=>setCustomerSupportVisible(true)} style={{ fontSize: 20, cursor: 'pointer', marginRight: 20, color: 'var(--semi-color-text-2)' }} />} */}
                        <Tooltip content={<LocaleFormatter id="nav.download" />}>
                            <a className={styles.navIcon} onClick={() => navigate(`${BASE_PATH}/download`)}>
                                <IconDownload size='large' />
                            </a>
                        </Tooltip>
                        {/* <Tooltip content="管理员手册">
                            <a className={styles.navIcon} href='https://fyymagic.feishu.cn/docx/MwhQdOIeRoUb6Ox3B41cQU1qnYf' target='_blank'>
                                <IconInfoCircle size='large' />
                            </a>
                        </Tooltip> */}
                        {(!globalConfig.customized || VITE_LOCAL_PAGER_AND_FILTER) && <Tooltip content={<LocaleFormatter id="tooltip.help" />}><a href={`https://docs.flylayer.com/std/admin-manual/v1?from=50001`} title='文档' target='_blank' className={styles.navIcon}><IconHelpCircle size='large' title='文档' /></a></Tooltip>}

                        {/* 语言切换 */}
                        <Tooltip content={<LocaleFormatter id="tooltip.language" />}>
                                <a className={styles.navIconLanguage} onClick={() => {
                                    switchLocale(locale === 'zh_CN' ? 'en_GB' : 'zh_CN')
                                }}>
                                    <IconLanguage size='large' /> &nbsp;{locale === 'zh_CN' ? '中文' : 'EN'}
                                </a>
                        </Tooltip>
                        <Tooltip content={<LocaleFormatter id="tooltip.theme" />}>
                            <a className={styles.navIcon} onClick={() => setThemeSwitchVisible(true)}>
                                <IconColorPalette size='large' />
                            </a>
                        </Tooltip>
                        <Tooltip content={<LocaleFormatter id={colorMode == 'light' ? 'tooltip.darkMode' : 'tooltip.lightMode'} />}>
                            <a className={styles.navIcon} onClick={() => switchColorMode()}>
                                {colorMode == 'light' ? <IconMoon size='large' /> : <IconSun size='large' />}
                            </a>
                        </Tooltip>


                        <UserIndicator></UserIndicator>

                    </Nav.Footer>
                </Nav>
            </div>
            <div className='mobile-toggle'>
                <Button icon={<IconMenu></IconMenu>} onClick={() => setMobileMenuVisible(!mobileMenuVisible)}></Button>
            </div>
        </Header>
        <Content className={styles.content}>
            <Suspense fallback={<></>}>
                <GlobalThemeContext.Provider value={{ colorMode, theme }}>
                    <Outlet />
                </GlobalThemeContext.Provider>
            </Suspense>
        </Content>
        {/* {globalConfig.flylayer && <Footer className={styles.footer}>
            <div className={styles.logoRevert} onClick={() => { setMobileMenuVisible(false); navigate(`${BASE_PATH}`) }}>
                <img src={globalConfig.logo}></img>
                <span>{globalConfig.name}</span>
            </div>
            <div className={styles.memo}>
                <div className='mb2'><LocaleFormatter id="footer.basedOn" />
                    <a href='https://www.wireguard.com/' target='_blank'>WireGuard</a>
                </div>
                <div>© 2023 Feiyue.Cloud</div>


                <div><a>Privacy</a> & <a>Terms</a></div>

            </div>
        </Footer>
        } */}

    </Layout>
        {/**已过期弹出框 */}
        {license.activated ? license.expired && !license.disabledExpiry && <Modal
            title={<LocaleFormatter id="license.expired.title" />}
            visible={true}
            closable={false}
            footer={null}
            width={400}
        >
            <div className='mb10'><b>{license.customer.name}</b> <LocaleFormatter id="license.expired.license" /> <Text type='danger'><LocaleFormatter id="license.expired.status" /></Text> ，<a onClick={() => {
                setInviteCodeModalVisible(true)
            }} style={{ textDecoration: 'underline', cursor: 'pointer' }}><LocaleFormatter id="license.expired.clickToActivate" /></a></div>
            <div className='mb10'><LocaleFormatter id="license.expired.expireTime" />：{moment(license.expiredAt).format('YYYY-MM-DD HH:mm:ss')}</div>

        </Modal> : <Modal
            title={<LocaleFormatter id="license.notActivated.title" />}
            visible={true}
            closable={false}
            footer={null}
            width={400}
        >
            <div className='mb10'><b>{license.customer.name}</b> <LocaleFormatter id="license.notActivated.software" /> <Text type='danger'><LocaleFormatter id="license.notActivated.status" /></Text> ，<a onClick={() => {
                setInviteCodeModalVisible(true)
            }} style={{ textDecoration: 'underline', cursor: 'pointer' }}><LocaleFormatter id="license.notActivated.clickToActivate" /></a></div>
            {/* <div className='mb10'>过期时间：{moment(license.expiredAt).format('YYYY-MM-DD HH:mm:ss')}</div> */}

        </Modal>}
        { }
        {/**邀请码弹出框 */}
        {/* {inviteCodeModalVisible && <Renew flynetGeneral={flynet} close={() => { setInviteCodeModalVisible(false) }} success={() => {

            setInviteCodeModalVisible(false)
            window.location.reload();


        }} />} */}

        {inviteCodeModalVisible && <Activation
            close={() => { setInviteCodeModalVisible(false) }} success={() => {
                setInviteCodeModalVisible(false)
                window.location.reload();
            }} />}

        {/**客服弹出框 */}
        {customerSupportVisible && <CustomerService close={() => { setCustomerSupportVisible(false) }} />}
        {/**主题选择弹出框 */}
        {themeSwitchVisible && <ThemeSwitch
            visible={themeSwitchVisible}
            theme={theme}
            setTheme={setTheme}
            onClose={() => setThemeSwitchVisible(false)}
        ></ThemeSwitch>}



    </>
}

export default Index;