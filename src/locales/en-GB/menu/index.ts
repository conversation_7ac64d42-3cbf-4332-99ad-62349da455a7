export const enGB_menu = {
	// Main navigation menu
	'nav.overview': 'Overview',
	'nav.services': 'Services',
	'nav.policies': 'Policies',
	'nav.logs': 'Logs',
	'nav.devices': 'Devices',
	'nav.users': 'Users',
	'nav.resources': 'Resources',
	'nav.settings': 'Settings',
	'nav.networks': 'Network',
	'nav.download': 'Download',

	// Tooltips
	'tooltip.help': 'View Help Documentation',
	'tooltip.theme': 'Select Theme',
	'tooltip.darkMode': 'Switch to Dark Mode',
	'tooltip.lightMode': 'Switch to Light Mode',
	'tooltip.language': 'Switch Language',

	// Language options
	'language.chinese': '中文',
	'language.english': 'English',

	// Common text
	'common.expired': 'Expired',
	'common.expiresSoon': 'expires in',
	'common.accountExpired': 'Account Expired',
	'common.clickToRenew': 'Click here to renew',
	'common.expireTime': 'Expiration time',

	// Overview page
	'overview.lastQueryTime': 'Last Query Time',
	'overview.refresh.none': 'None',
	'overview.refresh.5s': '5s',
	'overview.refresh.10s': '10s',
	'overview.refresh.30s': '30s',
	'overview.refresh.1m': '1m',
	'overview.refresh.5m': '5m',
	'overview.refresh.15m': '15m',
	'overview.getOverviewFailed': 'Failed to get overview information, please try again later',

	// Map component
	'overview.map.title': 'Global Device Distribution',
	'overview.map.totalDevices': 'Total Devices',
	'overview.map.onlineDevices': 'Online',
	'overview.map.totalUsers': 'Total Users',
	'overview.map.onlineUsers': 'Online Users',
	'overview.map.totalDevicesCount': 'Total Devices',
	'overview.map.onlineDevicesCount': 'Online Devices',
	'overview.map.city': 'City',

	// Device online trend
	'overview.deviceOnline.title': 'Device Connection Trend',
	'overview.deviceOnline.deviceCount': 'Device Connections',
	'overview.deviceOnline.recent6h': 'Last 6 Hours',
	'overview.deviceOnline.recent12h': 'Last 12 Hours',
	'overview.deviceOnline.recent24h': 'Last 24 Hours',
	'overview.deviceOnline.recent3d': 'Last 3 Days',
	'overview.deviceOnline.recent7d': 'Last 7 Days',

	// Device type distribution
	'overview.deviceType.title': 'Device Type Distribution',
	'overview.deviceType.name': 'Device Type',

	// Network traffic
	'overview.traffic.title': 'Network Traffic',
	'overview.traffic.recent6h': 'Last 6 Hours',
	'overview.traffic.recent12h': 'Last 12 Hours',
	'overview.traffic.recent24h': 'Last 24 Hours',
	'overview.traffic.recent3d': 'Last 3 Days',
	'overview.traffic.recent7d': 'Last 7 Days',
	'overview.traffic.upPeak': 'Upload Peak',
	'overview.traffic.downPeak': 'Download Peak',
	'overview.traffic.upTotal': 'Upload Total',
	'overview.traffic.downTotal': 'Download Total',
	'overview.traffic.receive': 'Receive',
	'overview.traffic.send': 'Send',

	// Client version
	'overview.clientVersion.title': 'Client Version Distribution',
	'overview.clientVersion.name': 'Client Version',
	'overview.clientVersion.deviceType': 'Device Type',

	// Components common text
	'components.common.cancel': 'Cancel',
	'components.common.confirm': 'Confirm',
	'components.common.close': 'Close',
	'components.common.save': 'Save',
	'components.common.delete': 'Delete',
	'components.common.edit': 'Edit',
	'components.common.add': 'Add',
	'components.common.new': 'New',
	'components.common.search': 'Search',
	'components.common.reset': 'Reset',
	'components.common.submit': 'Submit',
	'components.common.loading': 'Loading...',
	'components.common.noData': 'No Data',
	'components.common.all': 'All',
	'components.common.online': 'Online',
	'components.common.offline': 'Offline',
	'components.common.status': 'Status',
	'components.common.operatingSystem': 'Operating System',
	'components.common.pleaseSelect': 'Please select',

	// DND drag component
	'components.dnd.dragToSort': 'Note: You can drag the fields above to sort',

	// Relay map manager component
	'components.relayMapManager.title': 'Relay Server Configuration',
	'components.relayMapManager.description': 'Relay server configuration contains information such as relay server name, address, ID, etc.',
	'components.relayMapManager.saveConfig': 'Save Configuration',

	// Machine selector
	'components.machineSelector.title': 'Select Device',
	'components.machineSelector.searchPlaceholder': 'Search by user, device name, IP, version, etc.',

	// Customer service
	'components.customerService.title': 'Contact Customer Service',
	'components.customerService.description': 'Please scan the QR code with WeChat to contact customer service',

	// Theme switch
	'components.themeSwitch.feiyueCloud': 'Feiyue Cloud',
	'components.themeSwitch.feiyueCloudColor': 'Feiyue Cloud Color Theme',

	// Expandable
	'components.expandable.expand': '+ Expand',
	'components.expandable.collapse': '- Collapse',

	// Activation
	'components.activation.title': 'Enter License Key',
	'components.activation.machineCode': 'Machine Code',
	'components.activation.licenseKey': 'License Key',
	'components.activation.getLicenseKey': 'Get License Key',
	'components.activation.placeholder': 'Please paste or enter your license key here',
	'components.activation.success': 'License activated successfully',
	'components.activation.failed': 'License activation failed',

	// User indicator
	'components.userIndicator.profile': 'Profile',
	'components.userIndicator.appPanel': 'App Panel',
	'components.userIndicator.logout': 'Logout',

	// Error page
	'components.error.goLogin': 'Go to Login',
	'components.error.backHome': 'Back to Home',

	// Loading
	'components.loading.text': 'Loading...',

	// Policies page
	'policies.title': 'Policy List',
	'policies.description': 'Define policies that allow devices and users to connect in the network',
	'policies.lastSyncTime': 'Last Sync Time',
	'policies.policyGroup': 'Policy Group',
	'policies.addPolicy': 'Add Policy',
	'policies.addPolicyNew': 'Add Policy (New)',
	'policies.allPolicies': 'All Policies',
	'policies.totalCount': 'Total Policies',
	'policies.syncRequired': 'Current policies have been modified and need to be synchronized to take effect',
	'policies.syncNow': 'Apply Now',
	'policies.getACLPolicyFailed': 'Failed to get access control policy, please try again later',
	'policies.saveFailed': 'Save failed, please try again later',
	'policies.saveSuccess': 'Save successful',
	'policies.syncSuccess': 'Sync successful',
	'policies.syncFailed': 'Sync failed, please try again later',

	// Policy table columns
	'policies.table.policyName': 'Policy Name',
	'policies.table.priority': 'Priority',
	'policies.table.priorityTooltip': 'Priority range is 1-100, default is 1, which is the highest priority.',
	'policies.table.policyGroup': 'Policy Group',
	'policies.table.source': 'Source',
	'policies.table.action': 'Action',
	'policies.table.actionTooltip': 'When priorities are the same, deny action takes precedence over allow action.',
	'policies.table.target': 'Target',
	'policies.table.status': 'Status',

	// Policy actions
	'policies.action.allow': 'Allow',
	'policies.action.deny': 'Deny',
	'policies.action.enable': 'Enable',
	'policies.action.disable': 'Disable',
	'policies.action.enabled': 'Enabled',
	'policies.action.disabled': 'Disabled',

	// Policy operation menu
	'policies.menu.enablePolicy': 'Enable Policy',
	'policies.menu.disablePolicy': 'Disable Policy',
	'policies.menu.editPolicy': 'Edit Policy',
	'policies.menu.deletePolicy': 'Delete Policy',

	// Policy resource types
	'policies.resource.all': 'All',
	'policies.resource.ip': 'IP',
	'policies.resource.ipGroup': 'Resource Group',
	'policies.resource.user': 'User',
	'policies.resource.userGroup': 'User Group',
	'policies.resource.device': 'Device',
	'policies.resource.deviceGroup': 'Device Group',
	'policies.resource.service': 'Service',
	'policies.resource.serviceGroup': 'Service Group',
	'policies.resource.expression': 'Expression',
	'policies.resource.l7Expression': 'L7 Expression',
	'policies.resource.tag': 'Tag',

	// Built-in user groups
	'policies.autoGroup.self': 'Self',
	'policies.autoGroup.members': 'Members',
	'policies.autoGroup.internet': 'Internet',

	// Policy group types
	'policies.groupType.dynamic': 'Dynamic Policy Group',
	'policies.groupType.static': 'Static Policy Group',

	// Search filter
	'policies.filter.searchPlaceholder': 'Search by policy name, description, source, target, etc.',
	'policies.filter.query': 'Query',
	'policies.filter.user': 'User',
	'policies.filter.status': 'Status',

	// Policy delete
	'policies.delete.title': 'Delete Policy',
	'policies.delete.description': 'Are you sure you want to delete this policy?',
	'policies.delete.policyName': 'Policy Name',
	'policies.delete.deleteSuccess': 'Delete successful',
	'policies.delete.deleteFailed': 'Delete failed',

	// Policy enable/disable
	'policies.suspend.enableTitle': 'Enable Policy',
	'policies.suspend.disableTitle': 'Disable Policy',
	'policies.suspend.enableDescription': 'Are you sure you want to enable this policy?',
	'policies.suspend.disableDescription': 'Are you sure you want to disable this policy?',
	'policies.suspend.enableSuccess': 'Enable successful',
	'policies.suspend.disableSuccess': 'Disable successful',
	'policies.suspend.enableFailed': 'Enable failed',
	'policies.suspend.disableFailed': 'Disable failed',

	// Policy edit
	'policies.edit.title': 'Edit Policy',
	'policies.edit.name': 'Policy Name',
	'policies.edit.description': 'Description',
	'policies.edit.priority': 'Priority',
	'policies.edit.source': 'Source',
	'policies.edit.target': 'Target',
	'policies.edit.action': 'Action',
	'policies.edit.editSuccess': 'Edit successful',
	'policies.edit.editFailed': 'Edit failed',

	// Policy new
	'policies.new.title': 'New Policy',
	'policies.new.createSuccess': 'Create successful',
	'policies.new.createFailed': 'Create failed',
	'policies.new.duplicateName': 'Policy {name} name is duplicated',
	'policies.new.duplicatePolicy': 'Policy is duplicated with existing policy {name}',
	'policies.new.nameRequired': 'Name is required',
	'policies.new.policyName': 'Policy Name',
	'policies.new.priority': 'Priority',
	'policies.new.priorityTooltip': 'Priority range is 1-100, default is 1, which is the highest priority.',
	'policies.new.status': 'Status',
	'policies.new.enabled': 'Enabled',
	'policies.new.disabled': 'Disabled',
	'policies.new.description': 'Policy Description',
	'policies.new.policyGroup': 'Policy Group',
	'policies.new.source': 'Source',
	'policies.new.target': 'Target',
	'policies.new.action': 'Action',

	// Policy group
	'policies.group.title': 'Policy Group',
	'policies.group.name': 'Group Name',
	'policies.group.alias': 'Alias',
	'policies.group.description': 'Description',
	'policies.group.type': 'Type',
	'policies.group.createTime': 'Create Time',
	'policies.group.addGroup': 'Add Policy Group',
	'policies.group.editGroup': 'Edit Policy Group',
	'policies.group.deleteGroup': 'Delete Policy Group',
	'policies.group.createSuccess': 'Create successful',
	'policies.group.editSuccess': 'Edit successful',
	'policies.group.deleteSuccess': 'Delete successful',
	'policies.group.createFailed': 'Create failed',
	'policies.group.editFailed': 'Edit failed',
	'policies.group.deleteFailed': 'Delete failed',
	'policies.group.nameRequired': 'Name is required',
	'policies.group.codeRequired': 'Code is required',
	'policies.group.codeCannotStartWithDash': 'Code cannot start with -',
	'policies.group.codeInvalidFormat': 'Code can only contain letters, numbers and \'-\'',
	'policies.group.code': 'Code',
	'policies.group.codeTooltip': 'Cannot be modified after creation',
	'policies.group.remark': 'Remark',
	'policies.group.parentGroup': 'Parent Group',
	'policies.group.pleaseSelect': 'Please select',
	'policies.group.getFailed': 'Failed to get policy group',
	'policies.group.deleteConfirmation': 'The policy group will be deleted. After deletion, policies cannot be assigned to this group.',
	'policies.group.deleteInputConfirm': 'Type',
	'policies.group.deleteInputConfirmSuffix': 'to confirm deletion',
	'policies.group.policyCount': 'Policy Count',
	'policies.group.viewPolicies': 'View Policies',
	'policies.group.addSubGroup': 'Add Sub Group',

	// IP Group
	'policies.ipGroup.title': 'IP Group',
	'policies.ipGroup.name': 'Group Name',
	'policies.ipGroup.description': 'Description',
	'policies.ipGroup.ipList': 'IP List',
	'policies.ipGroup.addGroup': 'Add IP Group',
	'policies.ipGroup.editGroup': 'Edit IP Group',
	'policies.ipGroup.deleteGroup': 'Delete IP Group',

	// Expression
	'policies.expression.title': 'Expression',
	'policies.expression.name': 'Expression Name',
	'policies.expression.content': 'Expression Content',
	'policies.expression.addExpression': 'Add Expression',
	'policies.expression.editExpression': 'Edit Expression',
	'policies.expression.deleteExpression': 'Delete Expression',
	'policies.expression.description': 'Expressions are a set of conditions connected by logical operators, used to describe whether attributes in the request context contain specified values. Expression values can be strings, numbers, booleans, lists, or dictionaries.',
	'policies.expression.editPropertyDefinition': 'Edit Property Definition',
	'policies.expression.preview': 'Preview',
	'policies.expression.serialNumber': 'Serial Number',
	'policies.expression.property': 'Property',
	'policies.expression.operation': 'Operation',
	'policies.expression.value': 'Value',
	'policies.expression.nameRequired': 'Name is required',
	'policies.expression.nameCannotContainPrefix': 'Name cannot contain prefix',
	'policies.expression.duplicateName': 'Name is duplicated with existing expression',

	// Common operations
	'policies.common.save': 'Save',
	'policies.common.cancel': 'Cancel',
	'policies.common.confirm': 'Confirm',
	'policies.common.delete': 'Delete',
	'policies.common.edit': 'Edit',
	'policies.common.add': 'Add',
	'policies.common.close': 'Close',
	'policies.common.loading': 'Loading...',
	'policies.common.noData': 'No Data',
	'policies.common.required': 'Required',
	'policies.common.optional': 'Optional',
	'policies.common.preview': 'Preview',
	'policies.common.search': 'Search',
	'policies.common.searchByName': 'Search by name',
	'policies.common.searchByIpOrMemo': 'Search by IP or memo',
	'policies.common.notSelected': 'Not selected',
	'policies.common.pleaseSelect': 'Please select',
	'policies.common.apply': 'Apply',

	// Tag management
	'policies.tagowners.title': 'Tags',
	'policies.tagowners.description': 'Define tags that can be applied to devices and the list of users allowed to assign each tag',
	'policies.tagowners.name': 'Name',
	'policies.tagowners.users': 'Users',
	'policies.tagowners.nameRequired': 'Name is required',
	'policies.tagowners.nameCannotContainPrefix': 'Name cannot contain prefix',
	'policies.tagowners.nameExists': 'Name already exists',

	// Host alias management
	'policies.hosts.title': 'Resource Group',
	'policies.hosts.description': 'IP aliases allow you to define human-readable names for IP addresses or IP ranges to improve the readability of access rules',
	'policies.hosts.addAlias': 'Add IP Alias',
	'policies.hosts.alias': 'Alias',
	'policies.hosts.ipOrRange': 'IP or IP Range or Alias',

	// Auto approvers
	'policies.autoapprovers.title': 'Auto Approval Rules',
	'policies.autoapprovers.description': 'Define a list of users who can perform certain operations without approval from the management console, including advertising a specified set of routes as subnet routes or exit nodes',
	'policies.autoapprovers.subnetRoute': 'Subnet Route',
	'policies.autoapprovers.userOrTag': 'User or Tag',
	'policies.autoapprovers.exitNode': 'Exit Node',
	'policies.autoapprovers.user': 'User',
	'policies.autoapprovers.tag': 'Tag',
	'policies.autoapprovers.device': 'Device',

	// Edit policy group
	'policies.editGroup.title': 'Edit Policy Groups',
	'policies.editGroup.description': 'Policy groups for {name}',
	'policies.editGroup.placeholder': 'Please select policy groups',
	'policies.editGroup.success': 'Policy groups edited successfully',
	'policies.editGroup.failed': 'Failed to edit policy groups, please try again later',

	// Remove from policy group
	'policies.removeFromGroup.title': 'Remove Policy from Group',
	'policies.removeFromGroup.confirmMessage': 'Are you sure you want to remove policy {policyName} from group {groupName}?',
	'policies.removeFromGroup.successTitle': 'Operation Successful',
	'policies.removeFromGroup.successMessage': 'Policy {policyName} has been removed from group {groupName}',
	'policies.removeFromGroup.failedTitle': 'Operation Failed',
	'policies.removeFromGroup.failedMessage': 'Failed to remove policy {policyName} from group {groupName}',

	// User groups management
	'policies.groups.title': 'User Groups',
	'policies.groups.preview': 'Preview',
	'policies.groups.description': 'User groups define a short name for a group of users, which you can then use in ACL rules instead of explicitly listing each user. Any changes you make to group members will automatically propagate to all rules that reference the group.',
	'policies.groups.name': 'Name',
	'policies.groups.users': 'Users',
	'policies.groups.allUsers': 'All Users',
	'policies.groups.specificUsers': 'Specific Users',
	'policies.groups.nameRequired': 'Name is required',
	'policies.groups.nameCannotContainPrefix': 'Name cannot contain prefix',
	'policies.groups.nameExists': 'Name already exists',
	'policies.groups.usersRequired': 'Users cannot be empty',

	// Device management
	'devices.title': 'Device List',
	'devices.description': 'Manage devices connected to the network and pending approval',
	'devices.dataExport': 'Data Export',
	'devices.dataImport': 'Data Import',
	'devices.deviceGroup': 'Device Groups',
	'devices.tags': 'Tags',
	'devices.deploy': 'Deploy',
	'devices.totalCount': 'Total Devices',
	'devices.endMessage': '---- End of List ----',

	// Device table
	'devices.table.device': 'Device',
	'devices.table.deviceGroup': 'Device Groups',
	'devices.table.ip': 'IP',
	'devices.table.version': 'Version',
	'devices.table.lastOnlineTime': 'Last Online Time',
	'devices.table.online': 'Online',
	'devices.table.meshEnabled': 'Mesh Mode Enabled',
	'devices.table.meshDisabled': 'Mesh Mode Disabled',
	'devices.table.meshEnabledTooltip': 'Mesh mode is enabled, devices can communicate with each other through zero-trust networking (when access control allows)',
	'devices.table.meshDisabledTooltip': 'Mesh mode is disabled, devices cannot communicate with each other through zero-trust networking, only accessible through subnet routing',

	// Device filters
	'devices.filter.search': 'Search by device name, user, IP, version, tags, etc.',
	'devices.filter.query': 'Query',
	'devices.filter.selectOS': 'Select OS',
	'devices.filter.operatingSystem': 'Operating System',
	'devices.filter.selectMeshMode': 'Select Mesh Mode',
	'devices.filter.meshMode': 'Mesh Mode',
	'devices.filter.enable': 'Enable',
	'devices.filter.disable': 'Disable',
	'devices.filter.selectDeviceGroup': 'Please select device groups',
	'devices.filter.deviceGroup': 'Device Groups',
	'devices.filter.apply': 'Apply',

	// Device additional features
	'devices.connectorGroup': 'Connector Groups',
	'devices.searchPlaceholder': 'Search by user, device name, IP, version, tags, etc.',
	'devices.status': 'Status',
	'devices.all': 'All',
	'devices.meshMode': 'Mesh Mode',
	'devices.enabled': 'Enabled',
	'devices.disabled': 'Disabled',
	'devices.allDevices': 'All Devices',

	// Device details
	'devices.detail.allowAccess': 'Allow Access',
	'devices.detail.viewLogs': 'View Logs',
	'devices.detail.modifyDeviceName': 'Modify Device Name',
	'devices.detail.enableKeyExpiry': 'Enable Key Expiry',
	'devices.detail.disableKeyExpiry': 'Disable Key Expiry',
	'devices.detail.viewConfigLogs': 'View Config Logs',
	'devices.detail.editRouteSettings': 'Edit Route Settings',
	'devices.detail.setRejectRoutes': 'Set Reject Routes',
	'devices.detail.editRelayNode': 'Edit Relay Node',
	'devices.detail.editMeshMode': 'Edit Mesh Mode',
	'devices.detail.editAccessControlTags': 'Edit Access Control Tags',
	'devices.detail.forceKeyExpiry': 'Force Key Expiry',
	'devices.detail.deleteDevice': 'Delete Device',
	'devices.detail.editDeviceGroup': 'Edit Device Group',
	'devices.detail.tags': 'Tags',
	'devices.detail.user': 'User',
	'devices.detail.tagsTooltip': 'After tagging, device permissions are managed by tags in access control',
	'devices.detail.userTooltip': 'Default to the device accessor',
	'devices.detail.status': 'Status',
	'devices.detail.deviceGroup': 'Device Group',
	'devices.detail.previewSubnetRouteFailed': 'Failed to preview subnet routes, please try again later',
	'devices.detail.getDeviceRouteFailed': 'Failed to get device route information, please try again later',
	'devices.detail.getDeviceFailed': 'Failed to get device, please try again later',
	'devices.detail.getRelayRouteFailed': 'Failed to get relay route information, please try again later',
	'devices.detail.getDNSFailed': 'Failed to get device DNS information, please try again later',
	'devices.detail.deviceDetailTitle': 'Device Detail',
	'devices.detail.deviceDetailDesc': 'Device and network details for troubleshooting network issues.',
	'devices.detail.routeSettings': 'Route Settings',
	'devices.detail.connector': 'Connector',
	'devices.detail.setRoute': 'Set Route',
	'devices.detail.connectorDesc': 'The connector links physical networks scattered everywhere into a network that can communicate with each other.',
	'devices.detail.announceRoute': 'Announce Route',
	'devices.detail.noAnnounceRoute': 'This device has not announced any routes',
	'devices.detail.excludeRoute': 'Exclude Route',
	'devices.detail.noExcludeRoute': 'This device has no excluded routes',
	'devices.detail.preview': 'Preview',
	'devices.detail.noPreviewRoute': 'This device has not announced any routes',
	'devices.detail.rejectRoute': 'Reject Route',
	'devices.detail.setRejectRoute': 'Set Reject Route',
	'devices.detail.noRejectRoute': 'This device has not rejected any routes',
	'devices.detail.rejectRouteDesc': 'After setting the reject route, the device will no longer accept the routes announced in the announce route settings.',
	'devices.detail.networkSettings': 'Network Settings',
	'devices.detail.networkSettingsDesc': 'The network settings of the device can be configured at multiple levels, with priority from high to low: device > device group > user > user group > global. The settings on the device have the highest priority, and the global settings have the lowest. For example, the DNS server set on the device is preferred. If not set, the DNS server settings of the device group, user, user group, and global are used in turn.',
	'devices.detail.securityRiskBanner': 'This device has security risks',
	'devices.detail.riskLevelHigh': 'High',
	'devices.detail.riskLevelMedium': 'Medium',
	'devices.detail.riskLevelLow': 'Low',
	'devices.detail.creator': 'Creator',
	'devices.detail.deviceName': 'Device Name',
	'devices.detail.hostname': 'Hostname',
	'devices.detail.os': 'Operating System',
	'devices.detail.version': 'Version',
	'devices.detail.createdAt': 'Created At',
	'devices.detail.lastSeen': 'Last Seen',
	'devices.detail.endpoint': 'Endpoint',
	'devices.detail.keyExpiry': 'Key Expiry Time',
	'devices.detail.preferredRelay': 'Preferred Relay',
	'devices.detail.editDomainTitle': 'Edit Custom Domain Resolution for Current Device',
	'devices.detail.editDnsTitle': 'Edit DNS Server for Current Device',
	'devices.detail.editRelayMapTitle': 'Edit Relay Route for Current Device',
	'devices.detail.saveDnsSuccess': 'DNS information saved successfully',
	'devices.detail.saveDnsFailed': 'Failed to save DNS information, please try again later',
	'devices.detail.saveRelayMapSuccess': 'Relay route information saved successfully',
	'devices.detail.saveRelayMapFailed': 'Failed to save relay route information, please try again later',
	'devices.detail.deviceGroupLabel': 'Device Group: {group}',
	'devices.detail.userLabel': 'User: {user}',
	'devices.detail.userGroupLabel': 'User Group: {group}',
	'devices.detail.riskRegistryModified': 'System registry has been tampered',
	'devices.detail.riskPasswordShort': 'System password is too short',
	'devices.detail.riskAutoUpdateOff': 'System auto-update is not enabled',
	'devices.detail.currentDevice': 'Current Device',

	// Device group management
	'devices.group.title': 'Device Group List',
	'devices.group.createGroup': 'Create Device Group',
	'devices.group.searchPlaceholder': 'Search by device group name, code, remarks',
	'devices.group.query': 'Query',

	// Device operations
	'devices.delete.title': 'Delete Device',
	'devices.delete.description': 'This device will be removed from the network, and the services provided by this device will become unavailable in the network. Re-adding this device requires re-authentication.',
	'devices.delete.confirmText': 'Enter',
	'devices.delete.confirmAction': 'to confirm deletion',
	'devices.delete.success': 'Device deleted successfully',
	'devices.delete.failed': 'Failed to delete device, please try again later',

	'devices.rename.title': 'Modify Device Name',
	'devices.rename.description': 'Device name displayed in console and client',
	'devices.rename.autoGenerate': 'Auto-generate based on device information',
	'devices.rename.deviceName': 'Device Name',
	'devices.rename.deviceDescription': 'Description',
	'devices.rename.success': 'Device renamed successfully',
	'devices.rename.failed': 'Failed to rename device',

	'devices.expiry.title': 'This device\'s key will be forcibly expired.',
	'devices.expiry.description': 'After the device key expires, this device will not be able to communicate with other devices in the network. To restore, please re-authenticate this device.',
	'devices.expiry.success': 'Device key expired successfully',
	'devices.expiry.failed': 'Failed to expire device key, please try again later',

	'devices.deploy.title': 'Deploy Device',

	// Data export/import
	'devices.dataExport.title': 'Export Records',
	'devices.dataExport.exportDevice': 'Export Device',
	'devices.dataExport.searchPlaceholder': 'Search by filename, export parameters',
	'devices.dataExport.fileName': 'File Name',
	'devices.dataExport.download': 'Download',
	'devices.dataExport.operator': 'Operator',
	'devices.dataExport.unknown': 'Unknown',
	'devices.dataExport.recordCount': 'Record Count',
	'devices.dataExport.exportTime': 'Export Time',
	'devices.dataExport.exportParams': 'Export Parameters',
	'devices.dataExport.none': 'None',
	'devices.dataImport.title': 'Import Records',
	'devices.dataImport.importDevice': 'Import Device',
	'devices.dataImport.searchPlaceholder': 'Search by filename, import parameters',
	'devices.dataImport.fileName': 'File Name',
	'devices.dataImport.download': 'Download',
	'devices.dataImport.totalLines': 'Total Lines',
	'devices.dataImport.errorLines': 'Error Lines',
	'devices.dataImport.createLines': 'Created Lines',
	'devices.dataImport.updateLines': 'Updated Lines',
	'devices.dataImport.isFinished': 'Finished',
	'devices.dataImport.yes': 'Yes',
	'devices.dataImport.no': 'No',
	'devices.dataImport.operator': 'Operator',
	'devices.dataImport.unknown': 'Unknown',
	'devices.dataImport.importTime': 'Import Time',

	// Device group operations
	'devices.groupAdd.title': 'Create Device Group',
	'devices.groupAdd.name': 'Name',
	'devices.groupAdd.nameRequired': 'Name is required',
	'devices.groupAdd.code': 'Code',
	'devices.groupAdd.codeTooltip': 'Cannot be modified after creation',
	'devices.groupAdd.codeRequired': 'Code is required',
	'devices.groupAdd.codeCannotStartWithDash': 'Code cannot start with -',
	'devices.groupAdd.codeInvalidFormat': 'Code can only contain letters, numbers and \'-\'',
	'devices.groupAdd.remarks': 'Remarks',
	'devices.groupAdd.type': 'Type',
	'devices.groupAdd.staticGroup': 'Static Device Group',
	'devices.groupAdd.dynamicGroup': 'Dynamic Device Group',
	'devices.groupAdd.devices': 'Devices',
	'devices.groupAdd.input': 'Input',
	'devices.groupAdd.expressionEmpty': 'Expression cannot be empty',
	'devices.groupAdd.triggerParamError': 'Trigger parameter error',
	'devices.groupAdd.paramCombination': 'Parameter Combination',
	'devices.groupAdd.paramCombinationHelp': 'Operators support and, or, not, () brackets, e.g.: 1 and 2, 1 or 2, not 1, 1 or (not 1)',
	'devices.groupAdd.dynamicGroupAttributeEmpty': 'Dynamic device group attributes are empty, please go to',
	'devices.groupAdd.settings': 'Settings',
	'devices.groupAdd.createSuccess': 'Device group created successfully',
	'devices.groupAdd.createFailed': 'Failed to create device group',

	'devices.groupEdit.title': 'Edit Device Group',
	'devices.groupEdit.updateSuccess': 'Device group updated successfully',
	'devices.groupEdit.updateFailed': 'Failed to update device group',

	'devices.groupDel.title': 'Delete Device Group',
	'devices.groupDel.description': 'The device group will be deleted. After deletion, devices cannot be assigned to this group.',
	'devices.groupDel.confirmText': 'Type',
	'devices.groupDel.confirmSuffix': 'to confirm deletion',
	'devices.groupDel.deleteSuccess': 'Device group deleted successfully',
	'devices.groupDel.deleteFailed': 'Failed to delete device group, please try again later',

	'devices.groupDns.title': 'Network Settings',
	'devices.groupDns.getRelayMapFailed': 'Failed to get relay routing information, please try again later',
	'devices.groupDns.getDnsConfigFailed': 'Failed to get device DNS information, please try again later',
	'devices.groupDns.saveSuccess': 'Saved successfully',
	'devices.groupDns.saveFailed': 'Save failed, please try again later',
	'devices.groupDns.saveRelayMapFailed': 'Failed to save relay routing information, please try again later',

	// DNS manager component related
	'devices.dnsManager.title': 'DNS Servers',
	'devices.dnsManager.description': 'Configure DNS servers used by devices',
	'devices.dnsManager.restrictedDomain': 'Restricted Domain',
	'devices.dnsManager.globalDNS': 'Global DNS Servers',
	'devices.dnsManager.overrideLocalDNS': 'Override Local DNS',
	'devices.dnsManager.overrideLocalDNSDescription': 'When enabled, clients will ignore local DNS settings and always use global DNS servers.<br />When disabled, clients prioritize local DNS settings and only use global DNS servers when needed.',
	'devices.dnsManager.noGlobalDNSConfigured': 'You have not configured global DNS servers yet',
	'devices.dnsManager.localDNSSettings': 'Local DNS Settings',
	'devices.dnsManager.addDNSServer': 'Add DNS Server',
	'devices.dnsManager.ipDescription': 'Use IPv4 or IPv6 addresses to resolve domain names',
	'devices.dnsManager.applyToSpecificDomain': 'Apply to Specific Domain',
	'devices.dnsManager.splitDNSDescription': 'This DNS server will only be used for certain domains',
	'devices.dnsManager.domain': 'Domain',
	'devices.dnsManager.domainUsageDescription': 'Use this DNS server only for the following domains. For example, if your domain is example.com, this DNS server will only be used for example.com and its subdomains (e.g., www.example.com)',
	'devices.dnsManager.validation.ipRequired': 'Please enter IP address',
	'devices.dnsManager.validation.cannotUseDefaultIP': 'Cannot use default IP address ***************',
	'devices.dnsManager.validation.invalidIP': 'Please enter a valid IP address',
	'devices.dnsManager.validation.ipExists': 'This IP address already exists',
	'devices.dnsManager.validation.domainRequired': 'Please enter domain',
	'devices.dnsManager.validation.invalidDomainFormat': 'Domain should contain lowercase alphanumeric characters, hyphens and dots',
	'devices.dnsManager.editDNSServer': 'Edit DNS Server',

	// Page titles related
	'pages.overview': 'Overview',
	'pages.dashboard': 'Dashboard',
	'pages.deviceMonitor': 'Device Monitor',
	'pages.physicalInterface': 'Physical Interface',
	'pages.deviceManagement': 'Device Management',
	'pages.networkTopology': 'Network Topology',
	'pages.networks': 'Networks',
	'pages.devices': 'Devices',
	'pages.deviceDetail': 'Device Detail',
	'pages.deviceLogs': 'Device Logs',
	'pages.deviceNetworkLogs': 'Device Network Logs',
	'pages.deviceGroup': 'Device Group',
	'pages.deviceImport': 'Device Import',
	'pages.deviceImportDetail': 'Device Import Detail',
	'pages.deviceExport': 'Device Export',
	'pages.settings': 'Settings',
	'pages.users': 'Users',
	'pages.userDetail': 'User Detail',
	'pages.userGroup': 'User Group',
	'pages.userGroupDetail': 'User Group Detail',
	'pages.userImport': 'User Import',
	'pages.userImportDetail': 'User Import Detail',
	'pages.userExport': 'User Export',
	'pages.userExportDetail': 'User Export Detail',
	'pages.settingsUserImport': 'User Import',
	'pages.sangforUserImport': 'Sangfor User Import',
	'pages.oidcImport': 'OIDC Import',
	'pages.resources': 'Resources',
	'pages.policies': 'Policies',
	'pages.accessPolicies': 'Access Policies',
	'pages.policyGroup': 'Policy Group',
	'pages.accessControls': 'Policies',
	'pages.sangforPolicyImport': 'Sangfor Policy Import',
	'pages.policyImport': 'Policy Import',
	'pages.policyImportDetail': 'Policy Import Detail',
	'pages.policyExport': 'Policy Export',
	'pages.policyExportDetail': 'Policy Export Detail',
	'pages.networkAccessPolicy': 'Network Access Policy',
	'pages.deviceAccessPolicy': 'Device Access Policy',
	'pages.baselineCheckDetail': 'Baseline Check Configuration Detail',
	'pages.baselineCheck': 'Baseline Check Configuration',
	'pages.appCheck': 'Application Check Configuration',
	'pages.appCheckDetail': 'Application Check Configuration Detail',
	'pages.processCheck': 'Process Check Configuration',
	'pages.processCheckDetail': 'Process Check Configuration Detail',
	'pages.services': 'Services',
	'pages.serviceDetail': 'Service Detail',
	'pages.serviceGroup': 'Service Group',
	'pages.serviceGroupDetail': 'Service Group Detail',
	'pages.autoDiscoveredServices': 'Auto Discovered Services',
	'pages.sangforServiceImport': 'Sangfor Service Import',
	'pages.serviceImport': 'Service Import',
	'pages.applicationSettings': 'Application Settings',
	'pages.applicationServices': 'Application Services',
	'pages.applicationGroup': 'Application Group',
	'pages.applicationGroupDetail': 'Application Group Detail',
	'pages.networkServices': 'Network Services',
	'pages.systemServices': 'System Services',
	'pages.systemServiceGroup': 'System Service Group',
	'pages.systemServiceGroupDetail': 'System Service Group Detail',
	'pages.routingPool': 'Routing Pool',
	'pages.networkServiceGroup': 'Network Service Group',
	'pages.networkServiceGroupDetail': 'Network Service Group Detail',
	'pages.routingTable': 'Routing Table',
	'pages.logs': 'Logs',
	'pages.configLogs': 'Configuration Logs',
	'pages.networkLogs': 'Network Logs',
	'pages.dnsLogs': 'DNS Logs',
	'pages.serviceLogs': 'Service Logs',
	'pages.logPushConfig': 'Log Push Configuration',
	'pages.pushConfigDetail': 'Push Configuration Detail',
	'pages.userGuide': 'User Guide',
	'pages.deviceNetworkingGuide': 'Device Networking Guide',
	'pages.vpnReplacementGuide': 'VPN Replacement Guide',
	'pages.intranetPenetrationGuide': 'Intranet Penetration Guide',
	'pages.selectNetwork': 'Select Network',
	'pages.trialDescription': 'Trial Description',
	'pages.personalTrial': 'Personal Trial',
	'pages.teamTrial': 'Team Trial',
	'pages.error': 'Error',
	'pages.download': 'Download',
	'pages.applicationPanel': 'Application Panel',
	'pages.notFound': 'Not Found',
	'pages.notFoundDescription': 'Nothing here~',

	// Router related
	'router.noPermission': 'No Permission',
	'router.notLoggedIn': 'You are not logged in, please log in first',

	// Simple layout related
	'menu.applicationPanel': 'Application Panel',
	'menu.adminConsole': 'Admin Console',
	'tooltip.userManual': 'View User Manual',

	// License related
	'license.expired.title': 'License Expired',
	'license.expired.license': 'license',
	'license.expired.status': 'expired',
	'license.expired.clickToActivate': 'Click here to activate',
	'license.expired.expireTime': 'Expire time',
	'license.notActivated.title': 'Software Not Activated',
	'license.notActivated.software': 'software',
	'license.notActivated.status': 'not activated',
	'license.notActivated.clickToActivate': 'Click here to activate',

	// Footer related
	'footer.basedOn': 'Based on',

	// Download component related
	'download.authFailed': 'Authentication failed, please try again later',
	'download.getPackageInfoFailed': 'Failed to get package information, please try again later',
	'download.zeroTrustAccessUrl': 'Your Zero Trust access URL is',
	'download.version': 'Version',
	'download.download': 'Download',
	'download.chipVersion': ' Chip Version',
	'download.howToCheckChipType': 'How to determine chip type',
	'download.macChipStep1': '1. In the top left corner, click',
	'download.macChipStep1Suffix': '-"About This Mac"',
	'download.macChipStep2': '2. In the "Overview" page under "Chip" or "Processor", check if it\'s "Intel" or "Apple"',
	'download.supportedVersions': 'Compatible with {version} or higher',
	'download.scanQROrClick': 'Scan QR code or click the link below to download',
	'download.installClient': 'Install {appName} client for {platform}',
	'download.requiresVersion': 'Requires {version} or higher',
	'download.authorizeAuth': 'Authorize Authentication',
	'download.authAfterInstall': 'Click "Authorize Authentication" button after installing the APP',
	'download.authFailedTitle': 'Authorization Authentication Failed ?',
	'download.troubleshootMethods': 'Troubleshooting methods:',
	'download.confirmAppInstalled': 'Please confirm if {appName} APP is installed',
	'download.confirmSystemBrowser': 'Please confirm if you are using system browser to open this page',
	'download.viewAuthCode': 'View Auth Code',
	'download.comingSoon': 'Coming Soon',
	'download.downloadWindowsClient': 'Download Windows Client',
	'download.oneClickInstall': 'One-click install using command line',
	'download.manualInstall': 'Manual Install',
	'download.macChipStep1Full': '1. In the top left corner, click',
	'download.macChipStep1About': '-"About This Mac"',
	'download.macChipStep2Full': '2. In the "Overview" page under "Chip" or "Processor", check if it\'s "Intel" or "Apple"',
	'download.authAfterInstallFull': 'Click "Authorize Authentication" button after installing the APP',
	'download.authFailedBannerTitle': 'Authorization Authentication Failed ?',
	'download.troubleshootMethodsFull': 'Troubleshooting methods:',
	'download.confirmAppInstalledFull': 'Please confirm if {appName} APP is installed',
	'download.confirmSystemBrowserFull': 'Please confirm if you are using system browser to open this page',
	'download.macChipStep1Complete': '1. In the top left corner, click "',
	'download.macChipStep1End': '"-"About This Mac"',
	'download.macChipStep2Complete': '2. In the "Overview" page under "Chip" or "Processor", check if it\'s "Intel" or "Apple"',
	'download.windowsSystemStep1': '1. Select "My Computer", right-click and select "Properties"',
	'download.windowsSystemStep2': '2. Under "Device specifications" or "System", check if the system type is "64-bit operating system" or "32-bit operating system"',
	'download.windowsSystemStep3': '3. Currently about 95% of computers are 64-bit operating systems. Users who are unsure about the system type and have relatively old computers can choose to download the [32-bit operating system version]',
	'download.howToCheckSystemType': 'How to determine system type',
	'download.download64BitVersion': 'Download 64-bit OS version',
	'download.download32BitVersion': 'Download 32-bit OS version',
	'download.mostUsersChoose': 'About 95% of users choose this',


	'devices.editGroup.title': 'Edit Device Groups',
	'devices.editGroup.description': 'Device groups for {name}',
	'devices.editGroup.placeholder': 'Please select device groups',
	'devices.editGroup.updateSuccess': 'Device groups updated successfully',
	'devices.editGroup.updateFailed': 'Failed to update device groups, please try again later',

	'devices.removeFromGroup.title': 'Remove Device from Group',
	'devices.removeFromGroup.description': 'Are you sure you want to remove device {deviceName} from group {groupName}?',
	'devices.removeFromGroup.successTitle': 'Operation Successful',
	'devices.removeFromGroup.successContent': 'Device {deviceName} has been removed from group {groupName}',
	'devices.removeFromGroup.failedTitle': 'Operation Failed',
	'devices.removeFromGroup.failedContent': 'Failed to remove device {deviceName} from group {groupName}',

	'devices.editMesh.title': 'Modify Mesh Mode for Device {deviceName}',
	'devices.editMesh.description': 'Mesh mode allows devices to join the zero-trust network via broadcast. When enabled, devices can route to all other devices and be accessed by all other devices (subject to access control policies)',
	'devices.editMesh.enableMesh': 'Enable Mesh Mode',
	'devices.editMesh.warning': 'Disabling Mesh mode may cause the device to lose connection to the zero-trust network. Please proceed with caution. Contact your administrator if you encounter issues',
	'devices.editMesh.updateSuccess': 'Successfully modified Mesh mode for device {deviceName}',
	'devices.editMesh.updateFailed': 'Failed to modify Mesh mode for device {deviceName}, please try again later',

	'devices.editRelay.title': 'Modify Relay Node for Device {deviceName}',
	'devices.editRelay.description': 'Device Relay Node',
	'devices.editRelay.placeholder': 'Please select',
	'devices.editRelay.auto': 'Auto',
	'devices.editRelay.updateSuccess': 'Successfully modified preferred relay',
	'devices.editRelay.updateFailed': 'Failed to modify preferred relay, please try again later',

	// Route management
	'devices.addRoute.title': 'Add Subnet Route for Device {deviceName}',
	'devices.addRoute.type': 'Type',
	'devices.addRoute.advertisedRoute': 'Advertised Route',
	'devices.addRoute.excludeRoute': 'Exclude Route',
	'devices.addRoute.subnetRoute': 'Subnet Route, CIDR format or IP range',
	'devices.addRoute.placeholder': '**********/24 or **********-************ or **********-255',
	'devices.addRoute.remark': 'Remark',
	'devices.addRoute.enableImmediately': 'Enable Immediately',
	'devices.addRoute.confirm': 'Confirm',
	'devices.addRoute.routeExists': 'Failed to add subnet route, subnet route {route} already exists',
	'devices.addRoute.addSuccess': 'Subnet route added successfully',
	'devices.addRoute.addFailed': 'Failed to add subnet route, please try again later',

	'devices.editRoute.title': 'Edit Subnet Routes for Device {deviceName}',
	'devices.editRoute.advertisedRoutes': 'Advertised Routes',
	'devices.editRoute.excludeRoutes': 'Exclude Routes',
	'devices.editRoute.addRoute': 'Add Route',
	'devices.editRoute.batchEdit': 'Batch Edit',
	'devices.editRoute.search': 'Search',
	'devices.editRoute.searchPlaceholder': 'Search routes',
	'devices.editRoute.enabled': 'Enabled',
	'devices.editRoute.disabled': 'Disabled',
	'devices.editRoute.enableSuccess': 'Route enabled successfully',
	'devices.editRoute.enableFailed': 'Failed to enable route, please try again later',
	'devices.editRoute.disableSuccess': 'Route disabled successfully',
	'devices.editRoute.disableFailed': 'Failed to disable route, please try again later',
	'devices.editRoute.deleteSuccess': 'Route deleted successfully',
	'devices.editRoute.deleteFailed': 'Failed to delete route, please try again later',
	'devices.editRoute.updateSuccess': 'Route updated successfully',
	'devices.editRoute.updateFailed': 'Failed to update route, please try again later',

	'devices.aclTag.title': 'Edit Access Control Tags for Device {deviceName}',
	'devices.aclTag.description': 'Tag devices as needed to implement different access control policies',
	'devices.aclTag.noTags': 'This device has no tags assigned',
	'devices.aclTag.selectTags': 'Select Tags',
	'devices.aclTag.updateSuccess': 'Access control tags updated successfully',
	'devices.aclTag.updateFailed': 'Failed to update access control tags, please try again later',
	'devices.aclTag.getPolicyFailed': 'Failed to get access control policy, please try again later',

	'devices.logs.title': 'Logs',
	'devices.logs.getDeviceFailed': 'Failed to get device, please try again later',

	'devices.addRouteNew.getServicesFailed': 'Failed to get network services, please try again later',

	// Data import detail component
	'devices.dataImportDetail.title': 'Device Import',
	'devices.dataImportDetail.import': 'Import',
	'devices.dataImportDetail.templateDownload': 'Template Download',
	'devices.dataImportDetail.uploadText': 'Click to upload file or drag file here',
	'devices.dataImportDetail.uploadSubText': 'Support CSV files',
	'devices.dataImportDetail.uploadError': 'Please upload CSV file',
	'devices.dataImportDetail.importSuccess': 'Import successful',
	'devices.dataImportDetail.importFailed': 'Import failed',
	'devices.dataImportDetail.updatedDevices': 'Updated Devices',
	'devices.dataImportDetail.errorRows': 'Error Rows',
	'devices.dataImportDetail.lineNumber': 'Line Number',
	'devices.dataImportDetail.errorMessage': 'Error Message',
	'devices.dataImportDetail.noErrorRows': 'No error rows',
	'devices.dataImportDetail.fields.id': 'ID',
	'devices.dataImportDetail.fields.deviceName': 'Device Name',
	'devices.dataImportDetail.fields.customDeviceName': 'Custom Device Name',
	'devices.dataImportDetail.fields.autoNaming': 'Auto Naming',
	'devices.dataImportDetail.fields.description': 'Description',
	'devices.dataImportDetail.fields.deviceGroup': 'Device Group',
	'devices.dataImportDetail.fields.disableKeyExpiry': 'Disable Key Expiry',
	'devices.dataImportDetail.fields.keyExpiryTime': 'Key Expiry Time',
	'devices.dataImportDetail.fields.deviceRoutes': 'Device Routes',
	'devices.dataImportDetail.fields.rejectRoutes': 'Reject Routes',
	'devices.dataImportDetail.fields.relayNode': 'Relay Node',
	'devices.dataImportDetail.fields.meshMode': 'Mesh Mode',

	// useTable operation menu
	'devices.menu.viewRecentLogs': 'View Recent Logs',
	'devices.menu.editRouteSettings': 'Edit Route Settings',
	'devices.menu.editRouteSettingsNew': 'Edit Route Settings (New)',
	'devices.menu.serviceReceptionRange': 'Service Reception Range Settings',
	'devices.menu.setRejectRoutes': 'Set Reject Routes',
	'devices.menu.editRelayNode': 'Edit Relay Node',
	'devices.menu.editMeshMode': 'Edit Mesh Mode',
	'devices.menu.editAccessControlTags': 'Edit Access Control Tags',
	'devices.menu.forceKeyExpiry': 'Force Key Expiry',
	'devices.menu.deleteDevice': 'Delete Device',
	'devices.menu.editDeviceGroup': 'Edit Device Group',
	'devices.menu.removeFromGroup': 'Remove from Group',
	'devices.menu.enableRemoteDesktop': 'Enable Remote Desktop',
	'devices.menu.disableRemoteDesktop': 'Disable Remote Desktop',
	'devices.menu.remoteDesktopUserNotEnabled': 'Remote desktop feature is not enabled for this device user, please enable it in user details',
	'devices.menu.remoteDesktopNotEnabled': 'Remote desktop feature is not enabled, please enable it in Settings/Device/Remote Desktop',
	'devices.menu.renameDevice': 'Rename Device',
	'devices.menu.enableKeyExpiry': 'Enable Key Expiry',
	'devices.menu.disableKeyExpiry': 'Disable Key Expiry',
	'devices.menu.viewConfigLogs': 'View Config Logs',

	// Services management module
	'services.table.serviceName': 'Service Name',
	'services.table.serviceType': 'Service Type',
	'services.table.protocolPort': 'Protocol/Port',
	'services.table.serviceGroup': 'Service Group',
	'services.table.serviceNodes': 'Service Nodes',
	'services.type.systemDaemon': 'System Service',
	'services.type.webApp': 'Web Application',
	'services.type.remoteDesktop': 'Remote Desktop',
	'services.routeMode.direct': 'Direct Mode',
	'services.routeMode.forward': 'Route Mode',
	'services.filter.searchPlaceholder': 'Search by service name or description',
	'services.filter.query': 'Query',
	'services.filter.serviceType': 'Service Type',
	'services.filter.source': 'Source',
	'services.filter.protocol': 'Protocol',
	'services.filter.serviceGroup': 'Service Group',
	'services.filter.selectServiceGroup': 'Select Service Group',
	'services.filter.all': 'All',
	'services.filter.apply': 'Apply',
	'services.error.loadServicesFailed': 'Failed to load services',

	// Service group management
	'services.group.table.groupName': 'Service Group Name',
	'services.group.table.serviceCount': 'Service Count',
	'services.group.table.createdTime': 'Created Time',

	// Service operations
	'services.delete.success': 'Service deleted successfully',
	'services.delete.failed': 'Failed to delete service, please try again later',
	'services.delete.title': 'Delete Service',
	'services.delete.confirmText': 'Are you sure you want to delete this service?',
	'services.create.success': 'Service created successfully',
	'services.create.failed': 'Failed to create service',
	'services.edit.success': 'Service edited successfully',
	'services.edit.failed': 'Failed to edit service',

	// Service creation guide
	'services.guide.connectorMismatch': 'Connector Mismatch',
	'services.guide.connectorMismatchContent': 'Subnet node IP addresses are not within the connector broadcast range, mismatched subnet nodes: ',
	'services.guide.ipPortOccupied': 'Service IP address or port is already occupied by other services',
	'services.guide.remoteDesktop.title': 'Remote Desktop',
	'services.guide.remoteDesktop.content': 'Allow other devices to connect to the device\'s remote desktop service through this service',
	'services.guide.systemService.title': 'System Service',
	'services.guide.systemService.content': 'Allow other devices to connect to the system service through this service',
	'services.guide.webApp.title': 'Web Application',
	'services.guide.webApp.content': 'Allow other devices to connect to the web application service through this service',

	// Device services table
	'services.devices.table.service': 'Service',
	'services.devices.table.port': 'Port',
	'services.devices.table.protocol': 'Protocol',
	'services.devices.table.device': 'Device',
	'services.devices.table.user': 'User',
	'services.devices.table.os': 'Operating System',
	'services.devices.table.lastOnline': 'Last Online Time',
	'services.devices.status.online': 'Online',
	'services.devices.error.loadDevicesFailed': 'Failed to load device list, please try again later',

	// Application table
	'services.application.table.name': 'Name',
	'services.application.table.category': 'Category',
	'services.application.table.icon': 'Icon',
	'services.application.table.urlMasquerade': 'Enable URL Masquerade',
	'services.application.export.name': 'Name',
	'services.application.export.description': 'Description',
	'services.application.export.category': 'Resource Category',
	'services.application.export.iconType': 'Icon Type(0: URL 1: Name)',
	'services.application.export.icon': 'Icon',
	'services.application.export.url': 'URL',
	'services.application.export.urlMasquerade': 'Enable URL Masquerade',
	'services.application.delete.success': 'Application deleted successfully',
	'services.application.delete.failed': 'Failed to delete application, please try again later',

	// Application editing
	'services.application.edit.error.loadCategoryFailed': 'Failed to load categories',
	'services.application.edit.error.loadFailed': 'Failed to load',
	'services.application.edit.success.updateSuccess': 'Updated successfully',
	'services.application.edit.error.updateFailed': 'Update failed',
	'services.application.edit.form.name': 'Name',
	'services.application.edit.form.nameRequired': 'Please enter name',
	'services.application.edit.form.namePlaceholder': 'Please enter name',
	'services.application.edit.form.category': 'Category',
	'services.application.edit.form.categoryRequired': 'Please select category',
	'services.application.edit.form.categoryPlaceholder': 'Please select category',
	'services.application.edit.form.inputCategory': 'Input category',
	'services.application.edit.error.createCategoryFailed': 'Failed to create category',
	'services.application.edit.form.icon': 'Icon',
	'services.application.edit.form.iconRequired': 'Please select icon type',
	'services.application.edit.form.iconPlaceholder': 'Please select icon type',
	'services.application.edit.form.iconName': 'Icon',
	'services.application.edit.form.iconUpload': 'Upload',
	'services.application.edit.error.fileFormat': 'Only JPG/PNG format files are allowed!',
	'services.application.edit.error.fileSize': 'Uploaded file must be smaller than 2MB!',
	'services.application.edit.error.uploadFailed': 'Upload failed',
	'services.application.edit.error.selectIcon': 'Please select icon',
	'services.application.edit.form.url': 'URL',
	'services.application.edit.form.urlRequired': 'Please enter URL',
	'services.application.edit.form.urlPlaceholder': 'Please enter URL',
	'services.application.edit.form.urlMasquerade': 'Enable URL Masquerade',

	// Application creation
	'services.application.new.success.createSuccess': 'Created successfully',
	'services.application.new.error.createFailed': 'Creation failed',

	// Application import
	'services.application.import.success.importSuccess': 'Import successful',
	'services.application.import.error.importFailed': 'Import failed',
	'services.application.import.error.checkFileFormat': 'Please check if the file format is correct',
	'services.application.import.table.resourceDescription': 'Resource Description',
	'services.application.import.table.resourceCategory': 'Resource Category',
	'services.application.import.table.iconType': 'Icon Type',
	'services.application.import.table.iconTypeUrl': 'URL',
	'services.application.import.table.iconTypeName': 'Name',
	'services.application.import.table.isValid': 'Is Valid',
	'services.application.import.table.yes': 'Yes',

	// Service components
	'services.components.type.remoteDesktop': 'Remote Desktop',
	'services.components.type.systemService': 'System Service',
	'services.components.type.all': 'All',
	'services.components.lastSeen.apply': 'Apply',

	// Application group sorting
	'services.application.groupSort.error.loadCategoryFailed': 'Failed to load categories',
	'services.application.groupSort.success.saveSuccess': 'Saved successfully',
	'services.application.groupSort.error.saveFailed': 'Save failed',
	'services.application.groupSort.error.nameRequired': 'Name cannot be empty',
	'services.application.groupSort.success.modifySuccess': 'Modified successfully',
	'services.application.groupSort.error.modifyFailed': 'Modification failed',
	'services.application.groupSort.success.deleteSuccess': 'Deleted successfully',
	'services.application.groupSort.error.deleteFailed': 'Deletion failed',
	'services.application.groupSort.form.namePlaceholder': 'Please enter name',
	'services.application.groupSort.success.createSuccess': 'Created successfully',
	'services.application.groupSort.error.createCategoryFailed': 'Failed to create category',
	'services.application.groupSort.button.confirm': 'Confirm',

	// Remove service from group
	'services.removeFromGroup.success.operationSuccess': 'Operation successful',
	'services.removeFromGroup.success.serviceRemovedFromGroup': 'Service {serviceName} has been removed from service group {serviceGroupName}',
	'services.removeFromGroup.error.operationFailed': 'Operation failed',
	'services.removeFromGroup.error.serviceRemoveFromGroupFailed': 'Failed to remove service {serviceName} from service group {serviceGroupName}',

	// Service import
	'services.import.breadcrumb.services': 'Services',
	'services.import.breadcrumb.dataImport': 'Data Import',
	'services.import.title.dataImport': 'Data Import',
	'services.import.description': 'Import existing data into this system',
	'services.import.sangforVpn': 'Sangfor VPN',

	// Service group page
	'services.group.filter.queryPlaceholder': 'Search by service group name, code, or remarks',
	'services.group.filter.query': 'Query',
	'services.group.breadcrumb.allServices': 'All Services',
	'services.group.breadcrumb.serviceGroups': 'Service Groups',

	// Device services page
	'services.devices.status.all': 'All',
	'services.devices.status.offline': 'Offline',
	'services.devices.breadcrumb.autoDiscoveredServices': 'Auto-discovered Services',
	'services.devices.filter.searchPlaceholder': 'Search by user, device name, IP, version, etc.',
	'services.devices.filter.status': 'Status',

	// Application main page
	'services.application.filter.searchPlaceholder': 'Search by application name, description, URL',
	'services.application.breadcrumb.application': 'Applications',
	'services.application.index.title.applicationPanel': 'Application Panel',
	'services.application.index.description.applicationPanel': 'View and manage application panel',
	'services.application.index.title.application': 'Applications',
	'services.application.index.description.application': 'View and manage applications',
	'services.application.index.button.dataImport': 'Data Import',
	'services.application.index.button.dataExport': 'Data Export',
	'services.application.index.button.categoryManagement': 'Category Management',
	'services.application.index.button.newApplication': 'New Application',
	'services.application.index.filter.category': 'Category',
	'services.application.index.stats.totalApplications': 'Total Applications',
	'services.application.index.endMessage': '---- End of List ----',
	'services.application.table.yes': 'Yes',
	'services.application.table.no': 'No',
	'services.application.dropdown.editApplication': 'Edit Application',
	'services.application.dropdown.deleteApplication': 'Delete Application',
	'services.application.export.filename': 'application-list.csv',
	'services.application.import.templateFilename': 'application-import-template.csv',
	'services.application.import.invalidDataFilename': 'invalid-data.csv',
	'services.application.import.title': 'Application Import',
	'services.application.import.button.import': 'Import',
	'services.application.import.upload.dragMainText': 'Click to upload file or drag file here',
	'services.application.import.upload.dragSubText': 'Supports CSV files',
	'services.application.import.button.templateDownload': 'Download Template',
	'services.application.import.button.invalidDataDownload': 'Download Invalid Data',
	'services.application.import.stats.totalData': 'Total Data',
	'services.application.import.stats.validData': 'Valid Records',
	'services.application.import.stats.invalidData': 'Invalid Records',
	'services.application.import.warning': 'Only valid applications will be imported. If an application already exists, old data will be overwritten',

	// Logs module
	'logs.index.button.logPushConfig': 'Log Push Configuration',
	'logs.index.tab.configLog': 'Configuration Logs',
	'logs.index.tab.networkLog': 'Network Logs',
	'logs.index.tab.dnsLog': 'DNS Logs',
	'logs.index.tab.servicesLog': 'Service Logs',
	'logs.index.notification.enableNetworkLogSuccess': 'Network logs enabled successfully',
	'logs.index.notification.enableNetworkLogFailed': 'Failed to enable network logs, please try again later',
	'logs.index.notification.disableNetworkLogSuccess': 'Network logs disabled successfully',
	'logs.index.notification.disableNetworkLogFailed': 'Failed to disable network logs, please try again later',
	'logs.index.notification.enableDnsLogSuccess': 'DNS logs enabled successfully',
	'logs.index.notification.enableDnsLogFailed': 'Failed to enable DNS logs, please try again later',
	'logs.index.notification.disableDnsLogSuccess': 'DNS logs disabled successfully',
	'logs.index.notification.disableDnsLogFailed': 'Failed to disable DNS logs, please try again later',

	// Log table
	'logs.table.column.time': 'Time',
	'logs.table.column.action': 'Action',
	'logs.table.column.operator': 'Operator',
	'logs.table.column.target': 'Target',
	'logs.table.error.loadLogsFailed': 'Failed to load log list, please try again later',
	'logs.table.error.loadEventsFailed': 'Failed to load log events list, please try again later',

	// DNS logs
	'logs.dnsLog.modal.enable': 'Enable',
	'logs.dnsLog.modal.disable': 'Disable',
	'logs.dnsLog.modal.title': 'DNS Logs',
	'logs.dnsLog.modal.content': 'Are you sure you want to {action} DNS logs?',
	'logs.dnsLog.modal.okText': 'Confirm',
	'logs.dnsLog.modal.cancelText': 'Cancel',
	'logs.dnsLog.table.column.user': 'User',
	'logs.dnsLog.table.column.device': 'Device',
	'logs.dnsLog.table.column.domain': 'Domain',
	'logs.dnsLog.table.column.type': 'Type',
	'logs.dnsLog.table.column.result': 'Resolution Result',

	// Network flow logs
	'logs.networkFlowLog.modal.enable': 'Enable',
	'logs.networkFlowLog.modal.disable': 'Disable',
	'logs.networkFlowLog.modal.title': 'Network Logs',
	'logs.networkFlowLog.modal.content': 'Are you sure you want to {action} network logs?',
	'logs.networkFlowLog.modal.okText': 'Confirm',
	'logs.networkFlowLog.modal.cancelText': 'Cancel',
	'logs.networkFlowLog.table.column.user': 'User',
	'logs.networkFlowLog.table.column.device': 'Device',
	'logs.networkFlowLog.table.column.summary': 'Summary',
	'logs.networkFlowLog.table.column.source': 'Source',
	'logs.networkFlowLog.table.column.target': 'Target',
	'logs.networkFlowLog.table.error.loadTrafficFailed': 'Failed to load traffic, please try again later',
	'logs.networkFlowLog.table.error.loadLogTypesFailed': 'Failed to load log operation types, please try again later',
	'logs.networkFlowLog.description.networkDevice': 'Network Device',
	'logs.networkFlowLog.description.recordTime': 'Record Time',
	'logs.networkFlowLog.description.timeWindow': 'Time Window',
	'logs.networkFlowLog.link.viewDevice': 'View Device',

	// Service logs
	'logs.servicesLog.table.column.user': 'User',
	'logs.servicesLog.table.column.device': 'Device',
	'logs.servicesLog.table.column.service': 'Service',

	// Log push configuration
	'logs.pushConfig.breadcrumb.logs': 'Logs',
	'logs.pushConfig.breadcrumb.pushConfig': 'Push Configuration',
	'logs.pushConfig.filter.placeholder': 'Search by name or description',
	'logs.pushConfig.filter.label': 'Search',

	// Log push configuration table
	'logs.pushConfig.table.column.name': 'Name',
	'logs.pushConfig.table.column.status': 'Status',
	'logs.pushConfig.table.column.statistics': 'Statistics',
	'logs.pushConfig.table.status.enabled': 'Enabled',
	'logs.pushConfig.table.status.disabled': 'Disabled',

	// Log push configuration delete
	'logs.pushConfig.del.success': 'Log push configuration deleted successfully',
	'logs.pushConfig.del.failed': 'Failed to delete log push configuration, please try again later',

	// Log push configuration new
	'logs.pushConfig.new.success': 'Created successfully',
	'logs.pushConfig.new.failed': 'Creation failed',
	'logs.pushConfig.new.form.name': 'Name',
	'logs.pushConfig.new.form.nameRequired': 'Name cannot be empty',

	// Log push configuration edit
	'logs.pushConfig.edit.success': 'Updated successfully',
	'logs.pushConfig.edit.failed': 'Update failed',
	'logs.pushConfig.edit.form.name': 'Name',
	'logs.pushConfig.edit.form.nameRequired': 'Name cannot be empty',

	// Log push configuration more text
	'logs.pushConfig.index.title': 'Log Push Configuration',
	'logs.pushConfig.index.button.newConfig': 'New Configuration',
	'logs.pushConfig.table.dropdown.editConfig': 'Edit Configuration',
	'logs.pushConfig.table.dropdown.deleteConfig': 'Delete Configuration',

	// Log push configuration edit more text
	'logs.pushConfig.edit.title': 'Edit Push Configuration',
	'logs.pushConfig.edit.form.code': 'Code',
	'logs.pushConfig.edit.form.codeTooltip': 'Cannot be modified after creation',
	'logs.pushConfig.edit.form.status': 'Status',
	'logs.pushConfig.edit.form.statusEnabled': 'Enabled',
	'logs.pushConfig.edit.form.statusDisabled': 'Disabled',
	'logs.pushConfig.edit.form.description': 'Description',
	'logs.pushConfig.edit.section.pushDestination': 'Push Destination',
	'logs.pushConfig.edit.link.pushTargetConfig': 'Push Target Configuration',
	'logs.pushConfig.edit.button.add': 'Add',
	'logs.pushConfig.edit.confirm.deleteTitle': 'Confirm deletion of this item',
	'logs.pushConfig.edit.confirm.deleteContent': 'This operation is irreversible',
	'logs.pushConfig.edit.section.pushEvents': 'Push Events',
	'logs.pushConfig.edit.link.pushEventConfig': 'Push Event Configuration',

	// Log push configuration new more text
	'logs.pushConfig.new.title': 'New Push Configuration',
	'logs.pushConfig.new.form.codeRequired': 'Code cannot be empty',
	'logs.pushConfig.new.form.codeStartError': 'Code cannot start with -',
	'logs.pushConfig.new.form.codeFormatError': 'Code can only contain letters, numbers and \'-\'',

	// Log push configuration detail more text
	'logs.pushConfig.detail.section.statistics': 'Statistics',
	'logs.pushConfig.detail.section.pushDestination': 'Push Destination',
	'logs.pushConfig.detail.section.pushEvents': 'Push Events',
	'logs.pushConfig.detail.dataTransform': 'Data Transform Configuration',

	// Log push configuration delete more text
	'logs.pushConfig.del.title': 'Delete Log Push Configuration',
	'logs.pushConfig.del.content': 'The log push configuration will be deleted and cannot be recovered. Are you sure you want to delete it?',
	'logs.pushConfig.del.confirmText': 'Enter {name} to confirm deletion',

	// Push metadata
	'logs.pushConfig.pushMeta.column.event': 'Event',
	'logs.pushConfig.pushMeta.column.target': 'Target',
	'logs.pushConfig.pushMeta.column.time': 'Time',
	'logs.pushConfig.pushMeta.column.successCount': 'Success Count',
	'logs.pushConfig.pushMeta.column.failureCount': 'Failure Count',
	'logs.pushConfig.pushMeta.range.all': 'All',
	'logs.pushConfig.pushMeta.range.today': 'Today',

	// Transform management
	'logs.pushConfig.transformManage.title': 'Data Transform',
	'logs.pushConfig.transformManage.noData': 'No Data',

	// Form validation messages
	'logs.pushConfig.validation.required': '{field} cannot be empty',
	'logs.pushConfig.validation.emailFormat': '{field} format is incorrect, please enter a valid email address',
	'logs.pushConfig.validation.urlFormat': '{field} format is incorrect, please enter a valid URL',
	'logs.pushConfig.validation.phoneFormat': '{field} format is incorrect, please enter a valid phone number',
	'logs.pushConfig.validation.ipFormat': '{field} format is incorrect, please enter a valid IP address',
	'logs.pushConfig.validation.idCardFormat': '{field} format is incorrect, please enter a valid ID card number',
	'logs.pushConfig.validation.numberFormat': '{field} format is incorrect, please enter a valid number',
	'logs.pushConfig.validation.integerFormat': '{field} format is incorrect, please enter a valid integer',
	'logs.pushConfig.validation.decimalFormat': '{field} format is incorrect, please enter a valid decimal',
	'logs.pushConfig.validation.jsonFormat': '{field} format is incorrect, please enter valid JSON format',
	'logs.pushConfig.validation.arrayFormat': '{field} format is incorrect, please enter valid array format',
	'logs.pushConfig.validation.invalidFormat': '{field} format is incorrect',
	'logs.pushConfig.validation.minLength': '{field} length cannot be less than {minLength}',
	'logs.pushConfig.validation.maxLength': '{field} length cannot be greater than {maxLength}',

	// User management module
	'users.breadcrumb.userGroups': 'User Groups',

	// User list page
	'users.index.title': 'Users',
	'users.index.description': 'Manage user accounts, permissions and access control',
	'users.index.button.addUser': 'Add User',
	'users.index.button.batchOperation': 'Batch Operation',
	'users.index.button.dataImport': 'Data Import',
	'users.index.button.dataExport': 'Data Export',
	'users.index.filter.searchPlaceholder': 'Search by username or display name',
	'users.index.filter.userGroup': 'User Group',
	'users.index.filter.userGroupPlaceholder': 'Please select user group',
	'users.index.filter.userType': 'User Type',
	'users.index.filter.userTypePlaceholder': 'Please select type',
	'users.type.temporary': 'Temporary User',
	'users.type.regular': 'Regular User',
	'users.table.user': 'User',
	'users.table.role': 'Role',
	'users.table.userGroups': 'User Groups',
	'users.table.joinTime': 'Join Time',
	'users.table.lastSeen': 'Last Seen',
	'users.table.status': 'Status',
	'users.action.removeFromFilter': 'Remove from filter',
	'users.action.addToFilter': 'Add to filter',
	'users.action.editRole': 'Edit User Role',
	'users.action.viewDevices': 'View User Devices',
	'users.action.viewLogs': 'View Recent Logs',
	'users.action.editUser': 'Edit User Information',
	'users.action.resetPassword': 'Reset Password',
	'users.action.resetPasswordConfirm': 'Resetting password will generate a new password for the user',
	'users.action.resetPasswordSuccess': 'Password reset successful',
	'users.action.resetPasswordFailed': 'Password reset failed, please try again later',
	'users.action.unlockAccount': 'Unlock Account',
	'users.action.editUserGroups': 'Edit User Groups',
	'users.action.enableUser': 'Enable User',
	'users.action.disableUser': 'Disable User',
	'users.action.deleteUser': 'Delete User',
	'users.action.setExpiration': 'Set Expiration',
	'users.action.removeMFADevice': 'Remove MFA Device',
	'users.error.fetchUserListFailed': 'Failed to fetch user list, please try again later',
	'users.removeMfaDevice.confirmText': 'Enter {loginName} to confirm removing MFA device',
	'users.dataExport.filter.searchPlaceholder': 'Username or display name',
	'users.dataExport.filter.search': 'Search',
	'users.dataExport.filter.statusPlaceholder': 'Please select status',
	'users.dataExport.filter.status': 'Status',
	'users.local.allUsers': 'All Users',
	'users.local.totalUsers': 'Total Users {count}',
	'users.local.selectedUsers': 'Selected {count} users',
	'users.local.notJoinedUsers': 'Not joined users: {count}',
	'users.local.batchAddToGroup': 'Batch Add to Group',
	'users.local.batchDelete': 'Batch Delete',
	'users.local.batchDisable': 'Batch Disable',
	'users.local.batchEnable': 'Batch Enable',
	'users.local.batchEditRole': 'Batch Edit Role',
	'users.local.batchSetExpiration': 'Batch Set Expiration',
	'users.local.endOfList': '---- End of List ----',

	// Data import related
	'users.dataImport.import': 'Import',
	'users.dataImport.newUsers': 'Users to Create',
	'users.dataImport.updateUsers': 'Users to Update',
	'users.dataImport.errorRows': 'Error Rows',
	'users.dataImport.rowNumber': 'Row Number',
	'users.dataImport.errorMessage': 'Error Message',
	'users.dataImport.noErrors': 'No error rows',
	'users.dataImport.uploadCsvOnly': 'Please upload CSV file only',
	'users.dataImport.dragMainText': 'Click to upload file or drag file here',
	'users.dataImport.dragSubText': 'Support CSV files',
	'users.dataImport.downloadTemplate': 'Download Template',

	// User detail related
	'users.detail.device': 'Device',
	'users.detail.version': 'Version',
	'users.detail.lastOnlineTime': 'Last Online Time',
	'users.detail.updateAvailable': 'Update Available',
	'users.detail.updateMessage': 'Latest version 1.40.0 has been released. Go read the release notes to see what\'s new!',
	'users.detail.fetchDeviceListFailed': 'Failed to fetch device list, please try again later',
	'users.detail.deviceListTitle': 'User\'s device list',

	// Template editor related
	'users.add.templateEditor.invalidJson': 'Expression template is not valid JSON',
	'users.add.templateEditor.saveSuccess': 'Save successful',
	'users.add.templateEditor.title': 'Edit User Template',
	'users.add.templateEditor.save': 'Save',
	'users.add.templateEditor.cancel': 'Cancel',

	// User group add related
	'users.groupAdd.name': 'Name',
	'users.groupAdd.nameRequired': 'Name cannot be empty',
	'users.groupAdd.code': 'Code',
	'users.groupAdd.codeHint': 'Cannot be modified after creation',
	'users.groupAdd.codeRequired': 'Code cannot be empty',
	'users.groupAdd.codeNoStartDash': 'Code cannot start with -',
	'users.groupAdd.codeFormat': 'Code can only contain letters, numbers and \'-\'',
	'users.groupAdd.input': 'Input',

	// User edit related
	'users.editUser.fetchNetworkInfoFailed': 'Failed to fetch network information',
	'users.editUser.loginNameRequired': 'Login name cannot be empty',
	'users.editUser.displayNameRequired': 'Display name cannot be empty',
	'users.editUser.loginName': 'Login Name',
	'users.editUser.displayName': 'Display Name',
	'users.editUser.accountExpiry': 'Account Expiry',
	'users.editUser.expiryTime': 'Expiry Time',
	'users.editUser.forceMFA': 'Force Enable MFA',
	'users.editUser.role': 'Role',
	'users.editUser.admin': 'Administrator',
	'users.editUser.adminDescription': 'Can view management console and manage devices and user network settings.',
	'users.editUser.user': 'Regular User',
	'users.editUser.userDescription': 'Can only use devices and network, cannot access management console.',
	'users.editUser.enableRDP': 'Enable Remote Desktop',
	'users.editUser.autoGrant': 'Auto Grant',
	'users.editUser.autoGrantDescription': 'After enabling remote desktop, new devices for this user will automatically enable remote desktop',

	// Domain manager related
	'domainManager.title': 'Custom Domain Resolution',
	'domainManager.description': 'Configure custom domain resolution',
	'domainManager.noDomainConfigured': 'You have not configured custom domain resolution yet',
	'domainManager.addCustomDomain': 'Add Custom Domain Resolution',
	'domainManager.aRecord': 'A Record',
	'domainManager.domain': 'Domain',
	'domainManager.enterDomain': 'Please enter domain',
	'domainManager.validation.ipRequired': 'Please enter IP address',
	'domainManager.validation.cannotUseDefaultIP': 'Cannot use default IP address ***************',
	'domainManager.validation.invalidIP': 'Please enter a valid IP address',
	'domainManager.validation.domainRequired': 'Please enter domain',
	'domainManager.validation.invalidDomainFormat': 'Domain should contain lowercase alphanumeric characters, hyphens and dots',
	'domainManager.validation.domainExists': 'Domain already exists',
	'domainManager.validation.cnameRequired': 'Please enter CNAME',
	'domainManager.validation.cnameFormat': 'CNAME should contain lowercase alphanumeric characters, hyphens and dots',
	'domainManager.validation.recordExists': 'Record already exists',
	'domainManager.validation.circularReference': 'Circular reference not allowed',
	'domainManager.name': 'Name',
	'domainManager.type': 'Type',
	'domainManager.value': 'Value',
	'domainManager.example': 'Example',
	'domainManager.ipv4Address': 'IPv4 Address',
	'domainManager.cnameTarget': 'Target, e.g.: www.example.com',
	'domainManager.editCustomDomain': 'Edit Custom Domain Resolution',
	'domainManager.nameFormatDescription': 'Supported name formats (priority increases from top to bottom)',
	'domainManager.nameFormat.wildcard': 'Wildcard',
	'domainManager.nameFormat.wildcardDesc': 'Match any',
	'domainManager.nameFormat.suffixMatch': 'Suffix Match',
	'domainManager.nameFormat.suffixMatchDesc': 'Match hostnames ending with abc',
	'domainManager.nameFormat.prefixMatch': 'Prefix Match',
	'domainManager.nameFormat.prefixMatchDesc': 'Match hostnames starting with abc',
	'domainManager.nameFormat.exactMatch': 'Exact Match',
	'domainManager.nameFormat.exactMatchDesc': 'Only match abc',
	'domainManager.nameFormat.wildcardSubdomain': 'Wildcard Subdomain',
	'domainManager.nameFormat.wildcardSubdomainDesc': 'Match any prefix like *.abc',
	'domainManager.nameFormat.suffixSubdomain': 'Suffix Subdomain',
	'domainManager.nameFormat.suffixSubdomainDesc': 'Match xabc.d like patterns',
	'domainManager.nameFormat.prefixSubdomain': 'Prefix Subdomain',
	'domainManager.nameFormat.prefixSubdomainDesc': 'Match abcx.d like patterns',
	'domainManager.nameFormat.exactDomain': 'Exact Domain',
	'domainManager.nameFormat.exactDomainDesc': 'Complete match',

	// User delete related
	'users.del.confirmMessage': 'Deleting {displayName}({loginName}) will delete all their devices. Deleting devices that advertise subnet routes or act as exit nodes may affect other users on the network.',
	'users.del.confirmText': 'Enter {loginName} to confirm deletion',
	'users.del.confirmMessagePrefix': 'Deleting',
	'users.del.confirmMessageSuffix': 'will delete all their devices. Deleting devices that advertise subnet routes or act as exit nodes may affect other users on the network.',
	'users.del.confirmTextPrefix': 'Enter',
	'users.del.confirmTextSuffix': 'to confirm deletion',

	// User group add/edit extensions
	'users.groupAdd.description': 'Description',
	'users.groupAdd.type': 'Type',
	'users.groupAdd.staticGroup': 'Static User Group',
	'users.groupAdd.dynamicGroup': 'Dynamic User Group',
	'users.groupAdd.builtinUsers': 'Built-in Users',
	'users.groupAdd.expressionRequired': 'Expression cannot be empty',
	'users.groupAdd.triggerParamError': 'Trigger parameter error',
	'users.groupAdd.operatorHelp': 'Operators support and, or, not, and parentheses. Examples: 1 and 2, 1 or 2, not 1, 1 or (not 1)',
	'users.groupAdd.paramCombination': 'Parameter Combination',
	'users.groupAdd.dynamicGroupEmptyHint': 'Dynamic user group attributes are empty, please go to',
	'users.groupAdd.settings': 'Settings',
	'users.groupAdd.expertMode': 'Expert Mode',
	'users.groupAdd.otherUsers': 'Other Users',
	'users.groupAdd.otherUsersHint': 'Other users besides built-in users, please enter directly, separated by commas.',

	// MFA edit related
	'users.editMfa.description': 'Edit MFA settings for user {displayName}({loginName})',
	'users.editMfa.forceMfaEnabled': 'Force Enable MFA',

	// Remote desktop edit related
	'users.editRdp.description': 'Edit remote desktop settings for user {displayName}({loginName})',
	'users.editRdp.enableRdp': 'Enable Remote Desktop',
	'users.editRdp.autoGrant': 'Auto Grant',
	'users.editRdp.autoGrantHint': 'After enabling remote desktop, new devices for this user will automatically enable remote desktop',

	// User suspend/restore related
	'users.suspend.modalTitle': '{action} User {displayName}({loginName})',
	'users.suspend.modalDescription': '{action} {displayName} ({loginName}) will {operation} access permissions for all devices they own.',
	'users.suspend.restore': 'restore',
	'users.suspend.suspend': 'suspend',

	// User unlock related
	'users.unlock.notLocked': 'This account is not locked',
	'users.unlock.passwordLocked': 'This account is locked due to too many incorrect password attempts',
	'users.unlock.mfaLocked': 'This account is locked due to too many incorrect MFA verification code attempts',
	'users.unlock.device': 'Device',
	'users.unlock.unlockTime': 'Unlock Time',
	'users.unlock.unlockAll': 'Unlock All',
	'users.unlock.unlockDevice': 'Unlock Device',

	// Data export extensions
	'users.dataExport.filter.all': 'All',
	'users.dataExport.filter.enabled': 'Enabled',
	'users.dataExport.filter.disabled': 'Disabled',
	'users.dataExport.filter.groupPlaceholder': 'Please select user group',
	'users.dataExport.filter.group': 'User Group',
	'users.dataExport.filter.typePlaceholder': 'Please select type',
	'users.dataExport.filter.type': 'User Type',
	'users.dataExport.filter.temporaryUser': 'Temporary User',
	'users.dataExport.filter.normalUser': 'Normal User',
	'users.dataExport.noPermission': 'No permission',
	'users.dataExport.modal.title': 'User Export',
	'users.dataExport.modal.export': 'Export',
	'users.dataExport.fetchListFailed': 'Failed to fetch user list, please try again later',
	'users.dataExport.deleteSuccess': 'Export record deleted successfully',
	'users.dataExport.deleteFailed': 'Failed to delete export record, please try again later',
	'users.dataExport.deleteTitle': 'Delete export record {fileName}',
	'users.dataExport.deleteConfirm': 'Enter {fileName} to confirm deletion',
	'users.dataExport.detail.title': 'Export Details',

	// Data import extensions
	'users.dataImport.table.operator': 'Operator',
	'users.dataImport.table.unknown': 'Unknown',
	'users.dataImport.table.importTime': 'Import Time',
	'users.dataImport.fetchListFailed': 'Failed to fetch user list, please try again later',
	'users.dataImport.deleteSuccess': 'Import record deleted successfully',
	'users.dataImport.deleteFailed': 'Failed to delete import record, please try again later',
	'users.dataImport.deleteTitle': 'Delete import record {fileName}',
	'users.dataImport.deleteConfirm': 'Enter {fileName} to confirm deletion',
	'users.dataImport.detail.title': 'Import Details',
	'users.dataImport.detail.recordDetail': 'Import Record Details',
	'users.dataImport.detail.fileName': 'File Name',
	'users.dataImport.detail.fileUrl': 'File URL',
	'users.dataImport.detail.totalLines': 'Total Lines',
	'users.dataImport.detail.errorLines': 'Error Lines',
	'users.dataImport.detail.createLines': 'Created Lines',
	'users.dataImport.detail.updateLines': 'Updated Lines',
	'users.dataImport.detail.isFinished': 'Is Finished',
	'users.dataImport.detail.errorRows': 'Error Rows',
	'users.dataImport.detail.errorRowsDescription': 'Error rows from user import',
	'users.dataImport.detail.lineNumber': 'Line Number',
	'users.dataImport.detail.errorMessage': 'Error Message',
	'users.dataImport.detail.importDetails': 'Import Details',
	'users.dataImport.detail.importDetailsDescription': 'Details of each data item imported by user',
	'users.dataImport.detail.isSuccess': 'Is Success',
	'users.dataImport.detail.message': 'Message',

	// Direct add user related
	'users.addDirect.loginNameRequired': 'Login name is required',
	'users.addDirect.loginNameFormat': 'Invalid login name format',
	'users.addDirect.passwordRequired': 'Password is required',
	'users.addDirect.passwordLength': 'Password must be at least 8 characters',
	'users.addDirect.passwordStrength': 'Password must contain numbers, letters and special characters',
	'users.addDirect.displayNameRequired': 'Display name is required',
	'users.addDirect.loginName': 'Login Name',
	'users.addDirect.displayName': 'Display Name',
	'users.addDirect.password': 'Password',
	'users.addDirect.passwordStrengthLabel': 'Password Strength',
	'users.addDirect.generatePassword': 'Generate',
	'users.addDirect.accountExpiry': 'Account Expiry',
	'users.addDirect.expiryTime': 'Expiry Time',
	'users.addDirect.forceMFA': 'Force Enable MFA',
	'users.addDirect.userGroups': 'User Groups',
	'users.addDirect.selectUserGroups': 'Please select user groups',
	'users.addDirect.role': 'Role',
	'users.addDirect.admin': 'Administrator',
	'users.addDirect.adminDescription': 'Can view management console and manage devices and user network settings.',
	'users.addDirect.user': 'Regular User',
	'users.addDirect.userDescription': 'Can only use devices and network, cannot access management console.',
	'users.index.filter.search': 'Search Users',
	'users.index.filter.group': 'User Group',
	'users.index.filter.role': 'Role',
	'users.index.filter.status': 'Status',
	'users.index.filter.all': 'All',
	'users.index.filter.active': 'Active',
	'users.index.filter.suspended': 'Suspended',
	'users.index.filter.expired': 'Expired',

	// User table
	'users.table.column.name': 'Name',
	'users.table.column.email': 'Email',
	'users.table.column.role': 'Role',
	'users.table.column.group': 'User Group',
	'users.table.column.status': 'Status',
	'users.table.column.lastLogin': 'Last Login',
	'users.table.column.expiresAt': 'Expires At',
	'users.table.column.actions': 'Actions',
	'users.table.status.active': 'Active',
	'users.table.status.suspended': 'Suspended',
	'users.table.status.expired': 'Expired',
	'users.table.status.locked': 'Locked',
	'users.table.dropdown.viewDetails': 'View Details',
	'users.table.dropdown.editUser': 'Edit User',
	'users.table.dropdown.editRole': 'Edit Role',
	'users.table.dropdown.editGroup': 'Edit User Group',
	'users.table.dropdown.resetPassword': 'Reset Password',
	'users.table.dropdown.editMFA': 'Edit MFA',
	'users.table.dropdown.editRDP': 'Edit RDP',
	'users.table.dropdown.editExpiresAt': 'Edit Expires At',
	'users.table.dropdown.suspend': 'Suspend User',
	'users.table.dropdown.unlock': 'Unlock User',
	'users.table.dropdown.deleteUser': 'Delete User',

	// Batch operations
	'users.batch.title': 'Batch Operation',
	'users.batch.selected': '{count} users selected',
	'users.batch.addToGroup': 'Add to User Group',
	'users.batch.removeFromGroup': 'Remove from User Group',
	'users.batch.editRole': 'Edit Role',
	'users.batch.editMFA': 'Edit MFA',
	'users.batch.editRDP': 'Edit RDP',
	'users.batch.editExpiresAt': 'Edit Expires At',
	'users.batch.suspend': 'Suspend Users',
	'users.batch.delete': 'Delete Users',

	// User add
	'users.add.title': 'Add User',
	'users.add.form.name': 'Name',
	'users.add.form.email': 'Email',
	'users.add.form.role': 'Role',
	'users.add.form.group': 'User Group',
	'users.add.form.expiresAt': 'Expires At',
	'users.add.button.save': 'Save',
	'users.add.button.cancel': 'Cancel',
	'users.add.success': 'User added successfully',
	'users.add.failed': 'Failed to add user',

	// User edit
	'users.edit.title': 'Edit User',
	'users.edit.success': 'User updated successfully',
	'users.edit.failed': 'Failed to update user',

	// User delete
	'users.delete.title': 'Delete User',
	'users.delete.content': 'Are you sure you want to delete user {name}? This action cannot be undone.',
	'users.delete.success': 'User deleted successfully',
	'users.delete.failed': 'Failed to delete user',

	// User suspend
	'users.suspend.title': 'Suspend User',
	'users.suspend.content': 'Are you sure you want to suspend user {name}?',
	'users.suspend.success': 'User suspended successfully',
	'users.suspend.failed': 'Failed to suspend user',

	// User unlock
	'users.unlock.title': 'Unlock User',
	'users.unlock.content': 'Are you sure you want to unlock user {name}?',
	'users.unlock.success': 'User unlocked successfully',
	'users.unlock.failed': 'Failed to unlock user',

	// User group management
	'users.group.title': 'User Groups',
	'users.group.description': 'Manage user groups and group members',
	'users.group.button.addGroup': 'Add User Group',
	'users.group.table.column.name': 'Group Name',
	'users.group.table.column.description': 'Description',
	'users.group.table.column.memberCount': 'Member Count',
	'users.group.table.column.actions': 'Actions',

	// User group add
	'users.groupAdd.title': 'Add User Group',
	'users.groupAdd.form.name': 'Group Name',
	'users.groupAdd.form.description': 'Description',
	'users.groupAdd.success': 'User group added successfully',
	'users.groupAdd.failed': 'Failed to add user group',

	// User group edit
	'users.groupEdit.title': 'Edit User Group',
	'users.groupEdit.success': 'User group updated successfully',
	'users.groupEdit.failed': 'Failed to update user group',

	// User group delete
	'users.groupDelete.title': 'Delete User Group',
	'users.groupDelete.content': 'Are you sure you want to delete user group {name}? This action cannot be undone.',
	'users.groupDelete.success': 'User group deleted successfully',
	'users.groupDelete.failed': 'Failed to delete user group',

	// Data import/export basic
	'users.dataImport.description': 'Batch import user data',
	'users.dataExport.description': 'Export user data',

	// User suspend/unlock
	'users.suspend.enable': 'Enable User',
	'users.suspend.disable': 'Disable User',
	'users.suspend.enableSuccess': 'User enabled successfully',
	'users.suspend.disableSuccess': 'User disabled successfully',
	'users.suspend.enableFailed': 'Failed to enable user',
	'users.suspend.disableFailed': 'Failed to disable user',

	// User password reset
	'users.password.title': 'Reset Password',
	'users.password.success': 'Password reset successfully',
	'users.password.failed': 'Failed to reset password',

	// User role edit
	'users.editRole.title': 'Edit Role',
	'users.editRole.success': 'Role updated successfully',
	'users.editRole.failed': 'Failed to update role',

	// User MFA edit
	'users.editMFA.title': 'Edit MFA',
	'users.editMFA.success': 'MFA settings updated successfully',
	'users.editMFA.failed': 'Failed to update MFA settings',

	// User RDP edit
	'users.editRDP.title': 'Edit RDP',
	'users.editRDP.success': 'RDP settings updated successfully',
	'users.editRDP.failed': 'Failed to update RDP settings',

	// User expires at edit
	'users.editExpiresAt.title': 'Edit Expires At',
	'users.editExpiresAt.success': 'Expires at updated successfully',
	'users.editExpiresAt.failed': 'Failed to update expires at',

	// Common buttons and actions
	'users.button.save': 'Save',
	'users.button.cancel': 'Cancel',
	'users.button.confirm': 'Confirm',
	'users.button.query': 'Query',
	'users.button.reset': 'Reset',
	'users.button.close': 'Close',

	// Common messages
	'users.message.loading': 'Loading...',
	'users.message.noData': 'No Data',
	'users.message.operationSuccess': 'Operation Successful',
	'users.message.operationFailed': 'Operation Failed',

	// User group delete
	'users.groupDel.title': 'Delete User Group',
	'users.groupDel.content': 'Are you sure you want to delete user group {name}? All users in this group will be removed.',
	'users.groupDel.confirmText': 'Enter {name} to confirm deletion',
	'users.groupDel.success': 'User group deleted successfully',
	'users.groupDel.failed': 'Failed to delete user group',

	// Data export
	'users.dataExport.title': 'Data Export',
	'users.dataExport.button.export': 'Export Users',
	'users.dataExport.table.column.fileName': 'File Name',
	'users.dataExport.table.column.status': 'Status',
	'users.dataExport.table.column.createTime': 'Create Time',
	'users.dataExport.table.column.fileSize': 'File Size',
	'users.dataExport.table.column.actions': 'Actions',
	'users.dataExport.status.processing': 'Processing',
	'users.dataExport.status.completed': 'Completed',
	'users.dataExport.status.failed': 'Failed',
	'users.dataExport.action.download': 'Download',
	'users.dataExport.action.delete': 'Delete',
	'users.dataExport.delete.title': 'Delete Export Record',
	'users.dataExport.delete.content': 'Are you sure you want to delete this export record?',
	'users.dataExport.delete.success': 'Export record deleted successfully',
	'users.dataExport.delete.failed': 'Failed to delete export record',

	// Data import
	'users.dataImport.title': 'Data Import',
	'users.dataImport.button.import': 'Import Users',
	'users.dataImport.table.column.fileName': 'File Name',
	'users.dataImport.table.column.status': 'Status',
	'users.dataImport.table.column.createTime': 'Create Time',
	'users.dataImport.table.column.totalCount': 'Total Count',
	'users.dataImport.table.column.successCount': 'Success Count',
	'users.dataImport.table.column.failedCount': 'Failed Count',
	'users.dataImport.table.column.actions': 'Actions',
	'users.dataImport.status.processing': 'Processing',
	'users.dataImport.status.completed': 'Completed',
	'users.dataImport.status.failed': 'Failed',
	'users.dataImport.action.viewDetails': 'View Details',
	'users.dataImport.action.delete': 'Delete',
	'users.dataImport.delete.title': 'Delete Import Record',
	'users.dataImport.delete.content': 'Are you sure you want to delete this import record?',
	'users.dataImport.delete.success': 'Import record deleted successfully',
	'users.dataImport.delete.failed': 'Failed to delete import record',
	'users.dataImport.template.filename': 'User Import Template',
	'users.dataImport.success': 'Import Successful',
	'users.dataImport.failed': 'Import Failed',

	// MFA device removal
	'users.removeMfaDevice.title': 'Remove MFA Device',
	'users.removeMfaDevice.content': 'Are you sure you want to remove MFA device for user {name}?',
	'users.removeMfaDevice.success': 'MFA device removed successfully',
	'users.removeMfaDevice.failed': 'Failed to remove MFA device',

	// Password reset
	'users.passwordReset.title': 'Reset Password',
	'users.passwordReset.content': 'Are you sure you want to reset password for user {name}?',
	'users.passwordReset.success': 'Password reset successfully',
	'users.passwordReset.failed': 'Failed to reset password',

	// MFA edit
	'users.mfaEdit.title': 'Edit MFA Settings',
	'users.mfaEdit.form.enabled': 'Enable MFA',
	'users.mfaEdit.form.disabled': 'Disable MFA',
	'users.mfaEdit.success': 'MFA settings updated successfully',
	'users.mfaEdit.failed': 'Failed to update MFA settings',

	// RDP edit
	'users.rdpEdit.title': 'Edit RDP Settings',
	'users.rdpEdit.form.enabled': 'Enable RDP',
	'users.rdpEdit.form.disabled': 'Disable RDP',
	'users.rdpEdit.success': 'RDP settings updated successfully',
	'users.rdpEdit.failed': 'Failed to update RDP settings',

	// User main page additional
	'users.index.totalCount': 'Total Users',
	'users.index.selectedCount': 'Selected',
	'users.index.users': 'users',
	'users.index.filter.statusPlaceholder': 'Please select status',
	'users.index.filter.rolePlaceholder': 'Please select role',
	'users.index.filter.groupPlaceholder': 'Please select user group',
	'users.index.filter.temporary': 'Please select temporary user',
	'users.index.batch.enable': 'Batch Enable',
	'users.index.batch.disable': 'Batch Disable',
	'users.index.batch.delete': 'Batch Delete',
	'users.index.batch.editRole': 'Batch Edit Role',
	'users.index.batch.editGroup': 'Batch Edit User Group',
	'users.index.batch.editMFA': 'Batch Edit MFA',
	'users.index.batch.editRDP': 'Batch Edit RDP',
	'users.index.batch.editExpiresAt': 'Batch Edit Expires At',

	// Breadcrumb navigation
	'users.breadcrumb.users': 'All Users',
	'users.breadcrumb.dataImport': 'Data Import',
	'users.breadcrumb.dataExport': 'Data Export',

	// User group page
	'users.group.totalCount': 'Total User Groups',
	'users.group.filter.search': 'Search by user group name, code, or description',

	// User roles and fields
	'users.role.admin': 'Administrator',
	'users.role.user': 'User',
	'users.role.superAdmin': 'Super Administrator',
	'users.role.unknown': 'Unknown',
	'users.field.userGroup': 'User Group',
	'users.field.loginName': 'Login Name',
	'users.field.displayName': 'Display Name',
	'users.field.password': 'Password',
	'users.field.temporary': 'Account Validity',
	'users.field.expiredAt': 'Expires At',
	'users.field.mfaEnabled': 'Force Enable MFA',
	'users.field.groups': 'User Groups',
	'users.field.role': 'Role',

	// User status
	'users.status.enabled': 'Enabled',
	'users.status.disabled': 'Disabled',

	// Role descriptions
	'users.role.admin.description': 'Can view the admin console and manage devices and user network settings.',
	'users.role.user.description': 'Can only use devices and network, cannot use admin console.',
	'users.role.networkAdmin': 'Network Administrator',
	'users.role.networkAdmin.description': 'Can view the admin console and manage ACL and network settings. Cannot manage devices or users.',
	'users.role.itAdmin': 'IT Administrator',
	'users.role.itAdmin.description': 'Can view the admin console and manage devices and users. Cannot manage ACL or network settings.',
	'users.role.billingAdmin': 'Billing Administrator',
	'users.role.billingAdmin.description': 'Can view the admin console and manage billing.',
	'users.role.auditor': 'Auditor',
	'users.role.auditor.description': 'Can view the admin console',

	// Edit role
	'users.editRole.description': 'Edit user role',

	// Edit MFA and RDP
	'users.editMFA.description': 'Edit MFA settings for the following users',
	'users.editRDP.description': 'Edit remote desktop settings for the following users',
	'users.field.rdpEnabled': 'Enable Remote Desktop',
	'users.field.rdpAutoGrant': 'Auto Grant',
	'users.field.rdpAutoGrant.description': 'After enabling remote desktop, new devices for this user will automatically enable remote desktop',

	// Batch suspend
	'users.batch.suspend.confirm': 'Are you sure you want to {action} the following users?',
	'users.batch.suspend.description': 'Will {action} device access permissions for the following users.',
	'users.batch.suspend.pause': 'suspend',
	'users.batch.suspend.resume': 'resume',

	// Local page additional
	'users.index.titleLocal': 'User List',
	'users.temporary.yes': 'Temporary User',
	'users.temporary.no': 'Non-temporary User',

	// Table column headers
	'users.table.column.user': 'User',
	'users.table.column.roleLocal': 'Role',
	'users.table.column.groupLocal': 'User Group',
	'users.table.column.createdAtLocal': 'Join Time',
	'users.table.column.lastSeenLocal': 'Last Online',
	'users.table.column.statusLocal': 'Status',

	// Password reset
	'users.passwordReset.description': 'Resetting password will generate a new password for the user',

	// Common
	'common.all': 'All',
	'common.yes': 'Yes',
	'common.no': 'No',
	'common.saveSuccess': 'Save successful',
	'common.saveFailed': 'Save failed',

	// User detail page
	'users.detail.getUserFailed': 'Failed to get user information, please try again later',
	'users.detail.getRelayConfigFailed': 'Failed to get relay server configuration, please try again later',
	'users.detail.getDNSConfigFailed': 'Failed to get DNS configuration, please try again later',
	'users.detail.viewDevices': 'View User Devices',
	'users.detail.viewRecentLogs': 'View Recent Logs',
	'users.detail.editUserInfo': 'Edit User Information',
	'users.detail.editUserGroup': 'Edit User Group',
	'users.detail.editUserRole': 'Edit User Role',
	'users.detail.unlockAccount': 'Unlock Account',
	'users.detail.enableUser': 'Enable User',
	'users.detail.disableUser': 'Disable User',
	'users.detail.deleteUser': 'Delete User',
	'users.detail.setExpiry': 'Set Expiry',
	'users.detail.removeMFADevice': 'Remove MFA Device',
	'users.detail.onlineStatus': 'Online Status',
	'users.detail.online': 'Online',
	'users.detail.offline': 'Offline',
	'users.detail.unassigned': 'Unassigned',
	'users.detail.notFilled': 'Not filled',
	'users.detail.accountDetails': 'Account Details',
	'users.detail.accountAttributes': 'User account attributes',
	'users.detail.deviceList': 'Device List',
	'users.detail.networkSettings': 'Network Settings',
	'users.detail.saveDNSSuccess': 'DNS configuration saved successfully',
	'users.detail.saveDNSFailed': 'Failed to save DNS configuration, please try again later',
	'users.detail.saveRelaySuccess': 'Relay routing information saved successfully',
	'users.detail.saveRelayFailed': 'Failed to save relay routing information, please try again later',

	// User add page additional
	'users.add.successTitle': 'Add Successful',
	'users.add.successMessage': 'User added successfully',
	'users.add.generate': 'Generate',

	// Form validation
	'form.validation.required': ' cannot be empty',
	'form.validation.minLength': ' length cannot be less than {length}',
	'form.validation.maxLength': ' length cannot be greater than {length}',
	'form.validation.invalidFormat': ' format is incorrect',
	'form.validation.invalidEmail': ' format is incorrect, please enter a valid email address',
	'form.validation.invalidIPv4': ' format is incorrect, please enter a valid IPv4 address',
	'form.validation.invalidIPv6': ' format is incorrect, please enter a valid IPv6 address',

	// User group edit
	'users.group.edit.title': 'Edit User Group',
	'users.group.edit.success': 'User group modified successfully',
	'users.group.edit.failed': 'Failed to modify user group',
	'users.group.edit.getFailed': 'Failed to get user group',
	'users.group.field.name': 'Name',
	'users.group.field.code': 'Code',
	'users.group.field.code.help': 'Cannot be modified after creation',
	'users.group.field.description': 'Description',
	'users.group.field.type': 'Type',
	'users.group.field.expressionsCombo': 'Parameter Combination',
	'users.group.field.expressionsCombo.help': 'Operators support and, or, not, (), e.g.: 1 and 2, 1 or 2, not 1, 1 or (not 1)',
	'users.group.type.static': 'Static User Group',
	'users.group.type.dynamic': 'Dynamic User Group',
	'users.group.builtinUsers': 'Built-in Users',
	'users.group.validation.nameRequired': 'Name cannot be empty',
	'users.group.validation.codeRequired': 'Code cannot be empty',
	'users.group.validation.codeInvalidStart': 'Code cannot start with -',
	'users.group.validation.codeInvalidFormat': 'Code can only contain letters, numbers and \'-\'',
	'users.group.validation.expressionRequired': 'Expression cannot be empty',
	'users.group.validation.parameterError': 'Parameter error',
	'users.group.dynamic.emptyAttributes': 'Dynamic user group attributes are empty, please go to',
	'users.group.dynamic.goToSettings': 'Settings',

	// Batch add to user group
	'users.batch.addToGroup.title': 'Add Users to User Group',
	'users.batch.addToGroup.description': 'The following users will be added to user groups:',
	'users.batch.addToGroup.selectPlaceholder': 'Please select user groups',
	'users.batch.addToGroup.success': 'Users added to user group successfully',
	'users.batch.addToGroup.failed': 'Failed to add users to user group, please try again later',

	// Edit user role (single user)
	'users.editRole.singleTitle': 'Edit role for user {user}',
	'users.editRole.singleDescription': 'Edit role for user {user}',
	'users.editRole.singleSuccess': 'User role edited successfully',
	'users.editRole.singleFailed': 'Failed to edit user role, please try again later',

	// Edit user expiry time
	'users.editExpiry.title': 'Modify expiry time for user {user}',
	'users.editExpiry.warning': 'After modifying the expiry time, users will be able to log in within the new validity period and will not be able to log in after expiration',
	'users.editExpiry.success': 'Modification successful',
	'users.editExpiry.successMessage': 'Expiry time for user {user} has been modified',
	'users.editExpiry.failed': 'Modification failed',

	// Batch edit user expiry time
	'users.batch.editExpiry.title': 'Batch Modify User Expiry Time',
	'users.batch.editExpiry.description': 'Batch set expiry time for the following users',
	'users.batch.editExpiry.success': 'Modification successful',
	'users.batch.editExpiry.successMessage': 'User expiry time has been modified',
	'users.batch.editExpiry.failed': 'Modification failed',

	// Add user to group
	'users.addToGroup.title': 'Add User to User Group',
	'users.addToGroup.description': 'Add user {user} to user groups',
	'users.addToGroup.getACLFailed': 'Failed to get access control policy, please try again later',

	// Remove user from group
	'users.removeFromGroup.title': 'Remove User from User Group',
	'users.removeFromGroup.confirm': 'Are you sure you want to remove user {user} from user group {group}?',
	'users.removeFromGroup.success': 'Operation successful',
	'users.removeFromGroup.successMessage': 'User {user} has been removed from user group {group}',
	'users.removeFromGroup.failed': 'Operation failed',
	'users.removeFromGroup.failedMessage': 'Failed to remove user {user} from user group {group}',

	// Edit user group
	'users.editUserGroup.title': 'Edit User Groups',
	'users.editUserGroup.description': 'User groups for user {user}',
	'users.editUserGroup.selectPlaceholder': 'Please select user groups',
	'users.editUserGroup.success': 'User groups edited successfully',
	'users.editUserGroup.failed': 'Failed to edit user groups, please try again later',

	// User group DNS settings
	'users.groupDNS.title': '{group} Network Settings',
	'users.groupDNS.getRelayFailed': 'Failed to get relay routing information, please try again later',
	'users.groupDNS.getDNSFailed': 'Failed to get device DNS information, please try again later',
	'users.groupDNS.saveRelayFailed': 'Failed to save relay routing information, please try again later',

	// Data export
	'users.dataExport.fileName': 'File Name',
	'users.dataExport.operator': 'Operator',
	'users.dataExport.recordCount': 'Record Count',
	'users.dataExport.exportTime': 'Export Time',
	'users.dataExport.exportParams': 'Export Parameters',

	// Batch delete users
	'users.batch.delete.warning': 'The following users will be deleted in batch',

	// Batch enable users
	'users.batch.enable': 'Batch Enable',

	// Data import
	'users.dataImport.fileName': 'File Name',
	'users.dataImport.totalLines': 'Total Lines',
	'users.dataImport.errorLines': 'Error Lines',
	'users.dataImport.createdLines': 'Created Lines',
	'users.dataImport.updatedLines': 'Updated Lines',
	'users.dataImport.completed': 'Completed',

	// User group table
	'users.group.table.name': 'Name',
	'users.group.table.description': 'Description',
	'users.group.table.type': 'Type',
	'users.group.table.userCount': 'User Count',
	'users.group.table.createdAt': 'Created At',

	// User group actions
	'users.group.actions.viewUsers': 'View Users',
	'users.group.actions.edit': 'Edit User Group',
	'users.group.actions.networkSettings': 'Network Settings',
	'users.group.actions.delete': 'Delete User Group',

	// Local user management page
	'users.index.titleLocalPage': 'User Management',
	'users.index.descriptionLocal': 'Manage users and their permissions for network access',
	'users.search.placeholder': 'Search by user information',
	'users.filter.status': 'User Status',
	'users.filter.role': 'Role',
	'users.filter.type': 'Type',

	// Extended user status
	'users.status.temporary': 'Temporary User',
	'users.status.expired': 'Expired',
	'users.status.notJoined': 'Not Joined',
	'users.status.online': 'Online',
	'users.status.expiryDate': 'Expiry Date',

	// Extended user actions
	'users.actions.editRole': 'Edit User Role',
	'users.actions.viewDevices': 'View User Devices',
	'users.actions.viewLogs': 'View Recent Logs',
	'users.actions.editUser': 'Edit User Information',
	'users.actions.resetPassword': 'Reset Password',
	'users.actions.unlockAccount': 'Unlock Account',
	'users.actions.editUserGroup': 'Edit User Groups',
	'users.actions.removeFromGroup': 'Remove from Group',
	'users.actions.mfaSettings': 'MFA Settings',
	'users.actions.enableUser': 'Enable User',
	'users.actions.disableUser': 'Disable User',
	'users.actions.deleteUser': 'Delete User',
	'users.actions.setExpiry': 'Set Expiry Date',
	'users.actions.removeMFADevice': 'Remove MFA Device',

	// User error messages
	'users.error.getUserListFailed': 'Failed to get user list, please try again later',

	// Filters
	'users.filter.selectUserGroups': 'Please select user groups',

	// Data export/import search
	'users.dataExport.searchPlaceholder': 'Search by file name, export parameters',
	'users.dataImport.searchPlaceholder': 'Search by file name',

	// Password reset
	'users.password.copyInstruction': 'Please copy the user\'s new password below.',
	'users.password.expiryWarning': 'This password will expire on XXXXXXX. If you want to continue using the authentication key later, you need to generate a new key.',

	// Resource management
	'resources.title': 'Resource List',
	'resources.description': 'Manage resource groups for network access',
	'resources.tab.preview': 'Preview',
	'resources.tab.resourceGroup': 'Resource Group',
	'resources.search.placeholder': 'Search by IP or memo',
	'resources.searchByName': 'Search by name',
	'resources.preview.helpText': 'Preview of all IPs under resource groups and associated resource groups',
	'resources.empty.title': 'No Resource Group Selected',
	'resources.empty.description': 'Please select a resource group.',
	'resources.button.batchEdit': 'Batch Edit',

	// Resource table
	'resources.table.index': 'Index',
	'resources.table.memo': 'Memo',

	// IP input
	'resources.ip.helpText': 'Please enter IP or IP range, e.g.: ********** or **********/24 or **********-**********00 or **********-100',
	'resources.ip.placeholder': 'Please enter IP or IP range',
	'resources.memo.placeholder': 'Please enter memo',

	// Resource validation
	'resources.validation.ipRequired': 'Please enter IP',
	'resources.validation.ipInvalid': 'Invalid IP format',
	'resources.validation.nameExists': 'Name already exists',
	'resources.validation.nameRequired': 'Name cannot be empty',
	'resources.validation.ipListError': 'IP list has errors, please check',
	'resources.validation.contentRequired': 'Content cannot be empty',
	'resources.validation.emptyLine': 'Line {line} is empty',
	'resources.validation.ipFormatError': 'Line {line} has invalid IP format',
	'resources.validation.ipDuplicate': 'Line {line1} and line {line2} have duplicate IPs',

	// Resource creation
	'resources.new.title': 'Create Resource Group',
	'resources.field.name': 'Name',
	'resources.field.description': 'Description',
	'resources.field.associatedGroups': 'Associated Resource Groups',

	// Batch edit
	'resources.batchEdit.title': 'Batch Edit Resource Group',
	'resources.batchEdit.helpText': 'One IP per line, format: IP or IP range, description, separated by comma. IP or IP range examples: ********** or **********/24 or **********-**********00 or **********-100',

	// Preview functionality
	'resources.preview.searchPlaceholder': 'Search by IP',

	// Resource group actions
	'resources.action.moveToTop': 'Move to Top',
	'resources.action.moveUp': 'Move Up',
	'resources.action.moveDown': 'Move Down',
	'resources.action.moveToBottom': 'Move to Bottom',
	'resources.action.copyAdd': 'Copy and Add',
	'resources.action.copySuffix': ' Copy',

	// Delete confirmation
	'resources.delete.confirmTitle': 'Are you sure you want to delete?',
	'resources.delete.confirmContent': 'This action cannot be undone',

	// Settings page categories
	'settings.category.system': 'System Settings',
	'settings.category.network': 'Network Settings',
	'settings.category.security': 'Security Settings',
	'settings.category.data': 'Data Settings',
	'settings.category.rights': 'Rights Center',

	// Settings menu items
	'settings.menu.device': 'Device',
	'settings.menu.user': 'User',
	'settings.menu.client': 'Client Release',
	'settings.menu.service': 'Service',
	'settings.menu.flynet': 'Basic',
	'settings.menu.relay': 'Relay',
	'settings.menu.connector': 'Connector',
	'settings.menu.api': 'API Access',
	'settings.menu.sensitive': 'Data Masking',
	'settings.menu.keys': 'Key Management',
	'settings.menu.schema': 'Schema',
	'settings.menu.logs': 'Logs',
	'settings.menu.datasource': 'Data Source',
	'settings.menu.application': 'Application Panel',
	'settings.menu.rights': 'Rights Settings',

	// Device settings
	'settings.device.title': 'Device',
	'settings.device.description': 'Manage device settings for zero-trust networking',
	'settings.device.approval.title': 'Device Approval',
	'settings.device.approval.description': 'Require new devices to be approved by administrators before accessing the zero-trust network',
	'settings.device.approval.manual': 'Manual approval for new devices',
	'settings.device.mesh.title': 'Mesh Mode',
	'settings.device.mesh.description': 'Disabled by default. When enabled, new devices will join the Mesh network by default and be visible to other devices.',
	'settings.device.mesh.enable': 'Enable Mesh Mode',

	// User settings
	'settings.user.title': 'User',
	'settings.user.description': 'Manage user settings',
	'settings.user.manualCreate.title': 'Manual Account Creation',
	'settings.user.manualCreate.description': 'Administrators can manually create accounts in the user management module of the management console',
	'settings.user.manualCreate.enable': 'Manual account creation',

	// Network basic settings
	'settings.flynet.title': 'Basic',
	'settings.flynet.description': 'Manage network basic settings',
	'settings.flynet.basicInfo.title': 'Basic Information',
	'settings.flynet.basicInfo.description': 'Network basic information',
	'settings.flynet.networkName': 'Network Name',
	'settings.flynet.networkId': 'Network ID',

	// API settings
	'settings.api.title': 'API Access',
	'settings.api.description': 'Manage API access basic settings',
	'settings.api.whitelist.title': 'API Authorization Whitelist',
	'settings.api.whitelist.description': 'Set API authorization whitelist. IP addresses in the whitelist can access APIs through API KEY.',
	'settings.api.editMode.list': 'List Edit',
	'settings.api.editMode.text': 'Text Edit',
	'settings.api.notification.success': 'Settings saved successfully',
	'settings.api.notification.failed': 'Failed to save settings',
	'settings.api.ipFormat.title': 'Supported formats:',
	'settings.api.ipFormat.single': 'Single IP: **********',
	'settings.api.ipFormat.cidr': 'CIDR format: **********/24',
	'settings.api.ipFormat.range': 'IP range: **********-************ or **********-255',
	'settings.api.validation.ipRequired': 'Please enter IP address',
	'settings.api.validation.ipPlaceholder': 'Please enter IP address',

	// Key management
	'settings.keys.title': 'Keys',
	'settings.keys.description': 'View and manage your authentication keys',

	// Data source settings
	'settings.datasource.title': 'Data Source',
	'settings.datasource.description': 'View and manage data sources',
	'settings.datasource.importPolicy': 'Policy Data Import',
	'settings.datasource.importUser': 'User Data Import',

	// Log settings
	'settings.logs.title': 'Logs',
	'settings.logs.description': 'View and manage your logs',

	// Relay settings
	'settings.relay.title': 'Relay',
	'settings.relay.description': 'Manage relay server settings',
	'settings.relay.saveSuccess': 'Settings saved successfully',
	'settings.relay.saveFailed': 'Settings failed',

	// Connector settings
	'settings.connector.title': 'Connector',
	'settings.connector.description': 'Manage connector settings',
	'settings.connector.groups.title': 'Connector Groups',
	'settings.connector.groups.description': 'Used for grouping and managing connectors',
	'settings.connector.createGroup': 'Create Group',

	// Data masking settings
	'settings.sensitive.title': 'Sensitive Fields',
	'settings.sensitive.description': 'Configure sensitive fields that will be masked for data protection.',
	'settings.sensitive.editMode.list': 'List Edit',
	'settings.sensitive.editMode.text': 'Text Edit',
	'settings.sensitive.notification.success': 'Settings saved successfully',
	'settings.sensitive.notification.failed': 'Failed to save settings',
	'settings.sensitive.column.operation': 'Operation',
	'settings.sensitive.column.path': 'Path',
	'settings.sensitive.column.value': 'Value',
	'settings.sensitive.operation.add': 'Add',
	'settings.sensitive.operation.replace': 'Replace',
	'settings.sensitive.operation.mask': 'Mask',
	'settings.sensitive.valueType.string': 'String',
	'settings.sensitive.valueType.number': 'Number',
	'settings.sensitive.valueType.boolean': 'Boolean',
	'settings.sensitive.valueType.object': 'Object',
	'settings.sensitive.valueType.array': 'Array',
	'settings.sensitive.valueType.null': 'Null',
	'settings.sensitive.validation.pathRequired': 'Path cannot be empty for item {index}',
	'settings.sensitive.validation.pathMustStartWithSlash': 'Path must start with \'/\' for item {index}',
	'settings.sensitive.validation.operationRequired': 'Operation cannot be empty for item {index}',
	'settings.sensitive.validation.valueRequired': 'Value cannot be empty for item {index}',
	'settings.sensitive.validation.incompleteData': 'Incomplete data, please check and resubmit',
	'settings.sensitive.validation.pathRequiredSimple': 'Path cannot be empty',
	'settings.sensitive.validation.pathMustStartWithSlashSimple': 'Path must start with /',

	// Schema settings
	'settings.schema.title': 'Schema',
	'settings.schema.description': 'Manage user and device attribute settings',
	'settings.schema.attribute.title': 'Attribute Configuration',
	'settings.schema.attribute.description': 'Manage user and device attribute settings. Configuration example:',
	'settings.schema.attribute.invalidJson': 'Attribute configuration is not valid JSON',
	'settings.schema.attribute.saveConfig': 'Save Configuration',
	'settings.schema.attribute.preview': 'Attribute Preview',
	'settings.schema.attribute.userAttributes': 'User Attributes',
	'settings.schema.attribute.deviceAttributes': 'Device Attributes',
	'settings.schema.aclGroup.invalidJson': 'ACL group template is not valid JSON',
	'settings.schema.aclGroup.editTitle': 'Edit ACL Group Template',
	'settings.schema.userGroup.invalidJson': 'User group template is not valid JSON',
	'settings.schema.userGroup.editTitle': 'Edit User Group Template',
	'settings.schema.attribute.exampleDescription': 'Attribute Template',
	'settings.schema.attribute.input': 'Input',
	'settings.schema.attribute.user': 'User',
	'settings.schema.attribute.account': 'Account',
	'settings.schema.attribute.attributes': 'Attributes',
	'settings.schema.attribute.nickname': 'Nickname',
	'settings.schema.attribute.profile': 'Profile',
	'settings.schema.attribute.website': 'Website',
	'settings.schema.attribute.gender': 'Gender',
	'settings.schema.admission.invalidJson': 'Network admission policy template is not valid JSON',
	'settings.schema.admission.editTitle': 'Edit Network Admission Policy Template',
	'settings.schema.deviceGroup.invalidJson': 'Device group template is not valid JSON',
	'settings.schema.deviceGroup.editTitle': 'Edit Device Group Template',

	// Client release settings (moved to client settings section)

	// Service settings
	'settings.service.title': 'Service',
	'settings.service.description': 'Manage service settings',

	// Rights settings
	'settings.rights.title': 'Rights',
	'settings.rights.description': 'Manage rights settings',
	'settings.rights.activationWarning': 'The system has not been activated. To avoid affecting normal service use, please activate the system.',
	'settings.rights.activate': 'Activate',
	'settings.rights.licensee': 'Licensed User',
	'settings.rights.updateLicense': 'Update License',
	'settings.rights.validUntil': 'Valid Until',
	'settings.rights.permanent': 'Permanent License',
	'settings.rights.machineCode': 'Machine Code',

	// Data source functions
	'settings.datasource.createSuccess': 'Data source created successfully',
	'settings.datasource.createFailed': 'Failed to create data source',
	'settings.datasource.addTitle': 'Add Data Source',
	'settings.datasource.field.name': 'Name',
	'settings.datasource.field.code': 'Code',
	'settings.datasource.field.codeHint': 'Cannot be modified after creation',
	'settings.datasource.validation.nameRequired': 'Name is required',
	'settings.datasource.validation.codeRequired': 'Code is required',

	// Key management functions
	'settings.keys.createTokenSuccess': 'Token created successfully',
	'settings.keys.createTokenFailed': 'Failed to create token, please try again later',
	'settings.keys.generateApiToken': 'Generate API Access Token',
	'settings.keys.field.description': 'Description',
	'settings.keys.field.validity': 'Validity Period',
	'settings.keys.field.expiryDays': 'Days until this API access token expires',
	'settings.keys.field.permanent': 'This API access token will be permanently valid',
	'settings.keys.field.expiryRange': 'Must be between {min} and {max} days',
	'settings.keys.keyExpire.title': 'Key Expiration Time',
	'settings.keys.keyExpire.description': 'Set the number of days devices can stay logged in before requiring re-authentication',
	'settings.keys.keyExpire.daysLabel': 'Days',
	'settings.keys.keyExpire.daysRange': 'Must be between {min} and {max} days.',
	'settings.keys.authKey.title': 'Device Authentication Keys',
	'settings.keys.authKey.description': 'Authenticate devices without interactive login',
	'settings.keys.authKey.generateButton': 'Generate Authentication Key',
	'settings.keys.authKey.invalidKeysCount': '{count} recently expired authorization keys',
	'settings.keys.apiKey.title': 'API Access Tokens',
	'settings.keys.apiKey.description': 'Use access tokens to call APIs, endpoint address: {url}',
	'settings.keys.apiKey.sdkInfo': 'Use',
	'settings.keys.apiKey.generateSdk': 'to generate client SDK',
	'settings.keys.apiKey.generateButton': 'Generate API Access Token',
	'settings.keys.apiKey.invalidKeysCount': '{count} recently expired API access tokens',
	'settings.keys.column.createdAt': 'Created At',
	'settings.keys.column.expiration': 'Expiration',
	'settings.keys.column.config': 'Configuration',
	'settings.keys.config.reusable': 'Reusable',
	'settings.keys.config.oneTime': 'One-time use',
	'settings.keys.config.ephemeral': 'Ephemeral device',
	'settings.keys.config.persistent': 'Persistent device',
	'settings.keys.config.preAuthorized': 'Pre-authorized',
	'settings.keys.config.notPreAuthorized': 'Not pre-authorized',
	'settings.keys.action.revoke': 'Revoke',
	'settings.keys.status.revoked': 'Revoked',
	'settings.keys.error.fetchFailed': 'Failed to fetch key list, please try again later',
	'settings.keys.error.fetchAclFailed': 'Failed to fetch access control policy, please try again later',
	'settings.keys.error.selectTags': 'Please select tags',
	'settings.keys.generateAuthKey': 'Generate Authentication Key',
	'settings.keys.field.reusable': 'Reusable',
	'settings.keys.field.reusableDesc': 'Whether to allow this key to be used for multiple authentications',
	'settings.keys.field.ephemeral': 'Ephemeral Device',
	'settings.keys.field.ephemeralDesc': 'Devices authenticated with this key will be automatically removed when offline',
	'settings.keys.field.preAuthorized': 'Pre-authorized',
	'settings.keys.field.preAuthorizedDesc': 'Devices authenticated with this key will be automatically approved',
	'settings.keys.field.tags': 'Tags',
	'settings.keys.field.tagsDesc': 'Devices will be tagged. Tagged keys will be permanently valid.',
	'settings.keys.field.tagsPlaceholder': 'Please select tags',
	'settings.keys.section.deviceSettings': 'Device Related Settings',
	'settings.keys.section.deviceSettingsDesc': 'The following settings will be applied to devices authenticated with this key',
	'settings.keys.result.title': 'Key Generation Result',
	'settings.keys.result.warning': 'Please make sure to copy your new key below. It will not be displayed in full again.',
	'settings.keys.result.expireInfo': 'This key will expire on {expire}. If you want to continue using authentication keys later, you need to generate a new key.',
	'settings.keys.revoke.title': 'Revoke Key',
	'settings.keys.revoke.success': 'Key revoked successfully',
	'settings.keys.revoke.failed': 'Failed to revoke key, please try again later',
	'settings.keys.revoke.apiDescription': 'After revoking this key, devices that have already been authenticated will remain authenticated, but it will prevent you from authenticating new devices.',
	'settings.keys.revoke.authDescription': 'Revoking this key will not cancel the authentication of devices that have been authenticated using this key, but it will prevent you from authenticating new devices.',

	// Device settings functions
	'settings.device.keyExpiry.title': 'Key Expiry Time',
	'settings.device.keyExpiry.description': 'Set the number of days a device can remain logged in before requiring re-authentication',

	// Log settings functions
	'settings.logs.createSuccess': 'Created successfully',
	'settings.logs.createFailed': 'Creation failed',
	'settings.logs.createConfig': 'Create Configuration',
	'settings.logs.allTypesExist': 'All types of configurations already exist, cannot create new ones',
	'settings.logs.field.name': 'Name',
	'settings.logs.field.code': 'Code',
	'settings.logs.field.codeHint': 'Cannot be modified after creation',
	'settings.logs.validation.nameRequired': 'Name is required',
	'settings.logs.validation.codeRequired': 'Code is required',

	// Connector settings functions
	'settings.connector.createSuccess': 'Connector group created successfully',
	'settings.connector.createFailed': 'Failed to create connector group',
	'settings.connector.createTitle': 'Create Connector Group',
	'settings.connector.field.name': 'Name',
	'settings.connector.field.code': 'Code',
	'settings.connector.field.codeHint': 'Cannot be modified after creation',
	'settings.connector.validation.nameRequired': 'Name is required',
	'settings.connector.validation.nameDuplicate': 'Name already exists',
	'settings.connector.validation.codeRequired': 'Code is required',

	// User account schema functions
	'settings.user.schema.title': 'Available Account Fields',
	'settings.user.schema.description': 'Configure available fields for user accounts',
	'settings.user.schema.column.name': 'Name',
	'settings.user.schema.column.title': 'Title',
	'settings.user.schema.column.type': 'Type',
	'settings.user.schema.column.enabled': 'Enabled',
	'settings.user.schema.status.enabled': 'Enabled',
	'settings.user.schema.status.disabled': 'Disabled',
	'settings.user.schema.preview.title': 'Preview Account Fields',

	// Sensitive field settings
	'settings.user.sensitivePatch.title': 'Sensitive Fields',
	'settings.user.sensitivePatch.description': 'Set sensitive fields, which will be desensitized.',
	'settings.user.sensitivePatch.listEdit': 'List Edit',
	'settings.user.sensitivePatch.textEdit': 'Text Edit',
	'settings.user.sensitivePatch.success': 'Settings saved successfully',
	'settings.user.sensitivePatch.failed': 'Settings failed',
	'settings.user.sensitivePatch.validation.incomplete': 'Data incomplete, please check and resubmit',
	'settings.user.sensitivePatch.validation.pathRequired': 'Path for item {index} cannot be empty;',
	'settings.user.sensitivePatch.validation.pathFormat': 'Path for item {index} must start with \'/\';',
	'settings.user.sensitivePatch.validation.opRequired': 'Operation for item {index} cannot be empty;',
	'settings.user.sensitivePatch.validation.valueRequired': 'Value for item {index} cannot be empty;',

	// Sensitive field list editing
	'settings.user.sensitivePatch.list.operation': 'Operation',
	'settings.user.sensitivePatch.list.path': 'Path',
	'settings.user.sensitivePatch.list.value': 'Value',
	'settings.user.sensitivePatch.operation.add': 'Add',
	'settings.user.sensitivePatch.operation.replace': 'Replace',
	'settings.user.sensitivePatch.operation.remove': 'Remove',
	'settings.user.sensitivePatch.operation.mask': 'Mask',
	'settings.user.sensitivePatch.validation.pathEmpty': 'Path cannot be empty',
	'settings.user.sensitivePatch.validation.pathStartSlash': 'Path must start with /',
	'settings.user.sensitivePatch.valueType.string': 'String',
	'settings.user.sensitivePatch.valueType.number': 'Number',
	'settings.user.sensitivePatch.valueType.boolean': 'Boolean',
	'settings.user.sensitivePatch.valueType.object': 'Object',
	'settings.user.sensitivePatch.valueType.array': 'Array',
	'settings.user.sensitivePatch.valueType.null': 'Null',

	// Account field editing
	'settings.user.schema.edit.title': 'Edit Available Fields',
	'settings.user.schema.edit.success': 'Settings saved successfully',
	'settings.user.schema.edit.failed': 'Settings failed',

	// Client settings
	'settings.client.title': 'Client',
	'settings.client.description': 'Manage client versions and downloads',

	// Client version management
	'settings.client.version.list.title': 'Version List',
	'settings.client.version.list.createVersion': 'Create Version',
	'settings.client.version.unknownVersion': 'Unknown Version',
	'settings.client.version.table.index': 'Index',
	'settings.client.version.table.versionNumber': 'Version Number',
	'settings.client.version.table.releaseNotes': 'Release Notes',
	'settings.client.version.table.forceUpgrade': 'Force Upgrade',
	'settings.client.version.table.releaseDate': 'Release Date',
	'settings.client.version.table.actions': 'Actions',
	'settings.client.version.delete.confirmTitle': 'Are you sure to delete this version?',
	'settings.client.version.delete.confirmContent': 'This change cannot be undone',

	// Add/Edit version
	'settings.client.addInstall.editTitle': 'Edit Version',
	'settings.client.addInstall.createTitle': 'Create Version',
	'settings.client.addInstall.updateSuccess': 'Version updated successfully',
	'settings.client.addInstall.updateFailed': 'Failed to update version, please try again later',
	'settings.client.addInstall.createSuccess': 'Version created successfully',
	'settings.client.addInstall.createFailed': 'Failed to create version, please try again later',
	'settings.client.addInstall.form.versionName': 'Version Name',
	'settings.client.addInstall.form.platform': 'Platform',
	'settings.client.addInstall.form.platformPlaceholder': 'Please select platform',
	'settings.client.addInstall.form.releaseDate': 'Release Date',
	'settings.client.addInstall.form.appstoreRelease': 'AppStore Release',
	'settings.client.addInstall.form.forceUpgrade': 'Force Upgrade',
	'settings.client.addInstall.form.releaseNotes': 'Release Notes',
	'settings.client.addInstall.form.packageType': 'Type',
	'settings.client.addInstall.form.chipType': 'Chip Type',
	'settings.client.addInstall.form.address': 'Address',
	'settings.client.addInstall.packageType.installer': 'Installer',
	'settings.client.addInstall.packageType.incremental': 'Incremental',
	'settings.client.addInstall.packageType.upgrade': 'Upgrade',
	'settings.client.addInstall.validation.versionNameRequired': 'Please enter version name',
	'settings.client.addInstall.validation.versionNameFormat': 'Version format should be x.y.z (e.g. 1.2.0)',
	'settings.client.addInstall.validation.versionExists': 'This version already exists for this platform, please change version number or edit existing one',
	'settings.client.addInstall.validation.platformRequired': 'Please select platform',
	'settings.client.addInstall.validation.releaseDateRequired': 'Please select release date',
	'settings.client.addInstall.validation.releaseNotesRequired': 'Please enter release notes',
	'settings.client.addInstall.upload.installerSuccess': 'Installer uploaded successfully',
	'settings.client.addInstall.upload.upgradeSuccess': 'Upgrade package uploaded successfully',
	'settings.client.addInstall.validation.distroRequired': 'Please select distribution',
	'settings.client.addInstall.validation.downloadLinkRequired': 'Please enter package URL',
	'settings.client.addInstall.upload.macosInstallerFolder': 'Please select folder named【MacOS Installer_{arch}】',
	'settings.client.addInstall.upload.windowsInstallerFolder': 'Please select folder named【Windows Installer】',
	'settings.client.addInstall.upload.androidInstallerFolder': 'Please select folder named【Android Installer】',
	'settings.client.addInstall.upload.iosInstallerFolder': 'Please select folder named【iOS Installer】',
	'settings.client.addInstall.upload.macosUpgradeFolder': 'Please select folder named【MacOS Upgrade_{arch}】',
	'settings.client.addInstall.upload.windowsUpgradeFolder': 'Please select folder named【Windows Upgrade】',
	'settings.client.addInstall.upload.androidUpgradeFolder': 'Please select folder named【Android Upgrade】',
	'settings.client.addInstall.upload.iosUpgradeFolder': 'Please select folder named【iOS Upgrade】',
	'settings.client.addInstall.upload.defaultTip': 'Do not modify files, select corresponding folder to upload directly',
	'settings.client.addInstall.upload.failed': 'Upload failed',
	'settings.client.addInstall.upload.clickToUpload': 'Click to Upload',
	'settings.client.addInstall.upload.invalidFormat': 'Invalid file format',

	// Add package
	'settings.client.addPackage.title': 'Create Package',
	'settings.client.addPackage.createSuccess': 'Package created successfully',
	'settings.client.addPackage.createFailed': 'Failed to create package, please try again later',
	'settings.client.addPackage.form.packageType': 'Package Type',
	'settings.client.addPackage.form.distro': 'Distribution',
	'settings.client.addPackage.form.chipType': 'Chip Type',
	'settings.client.addPackage.form.downloadLink': 'Package URL',
	'settings.client.addPackage.packageType.installer': 'Installer',
	'settings.client.addPackage.packageType.incremental': 'Incremental',
	'settings.client.addPackage.packageType.upgrade': 'Upgrade URL',
	'settings.client.addPackage.validation.downloadLinkRequired': 'Package URL cannot be empty',
	'settings.client.addPackage.validation.distroRequired': 'Distribution cannot be empty',
	'settings.client.addPackage.validation.packageTypeRequired': 'Package type cannot be empty',

	// Flynet network settings page
	'settings.flynet.page.title': 'Flynet Network',
	'settings.flynet.page.description': 'Manage Flynet network configuration and settings',
	'settings.flynet.page.networkName': 'Network Name',
	'settings.flynet.page.cancelEdit': 'Cancel Edit',
	'settings.flynet.page.saveEdit': 'Save Edit',
	'settings.flynet.page.zeroTrustSegment': 'Zero Trust Segment',
	'settings.flynet.page.contactServiceProvider': 'Please contact service provider if you need to modify',

	// DNS settings
	'settings.dns.title': 'Domain Resolution',
	'settings.dns.description': 'Manage domain resolution for zero trust networking',
	'settings.dns.domainName': 'Domain Name',
	'settings.dns.deviceDomain.title': 'Assign Device Domains',
	'settings.dns.deviceDomain.description': 'Automatically assign domain names to devices in the network, allowing you to access devices directly using their names',
	'settings.dns.deviceDomain.enable': 'Enable Device Domain Assignment',
	'settings.dns.deviceDomain.disable': 'Disable Device Domain Assignment',
	'settings.dns.httpsCerts.title': 'HTTPS Certificates',
	'settings.dns.httpsCerts.description': 'Allow users to provide HTTPS certificates for their devices',
	'settings.dns.httpsCerts.enable': 'Enable HTTPS Certificates',
	'settings.dns.httpsCerts.disable': 'Disable HTTPS Certificates',
	'settings.dns.dnsServers.title': 'DNS Servers',
	'settings.dns.dnsServers.description': 'Configure DNS servers used by devices',
	'settings.dns.dnsServers.add': 'Add DNS Server',
	'settings.dns.dnsServers.deleteSuccess': 'DNS server deleted successfully',
	'settings.dns.dnsServers.deleteFailed': 'Failed to delete DNS server, please try again later',
	'settings.dns.deviceDomain.enableSuccess': 'Device domain assignment enabled successfully',
	'settings.dns.deviceDomain.enableFailed': 'Failed to enable device domain assignment, please try again later',
	'settings.dns.httpsCerts.enablePrerequisite': 'Please enable device domain assignment before enabling HTTPS',
	'settings.dns.httpsCerts.enableSuccess': 'HTTPS enabled successfully',
	'settings.dns.httpsCerts.enableFailed': 'Failed to enable HTTPS, please try again later',
	'settings.dns.overrideLocalDns.success': 'Override local DNS successfully',
	'settings.dns.overrideLocalDns.failed': 'Failed to override local DNS, please try again later',
	'settings.dns.overrideLocalDns.label': 'Override Local DNS',
	'settings.dns.overrideLocalDns.tooltip': 'When enabled, clients will ignore local DNS settings and always use global DNS servers.',
	'settings.dns.overrideLocalDns.tooltipDisabled': 'When disabled, clients prioritize local DNS settings and only use global DNS servers when needed.',
	'settings.dns.globalDnsServers': 'Global DNS Servers',
	'settings.dns.globalDnsServers.notSet': 'You have not set up global DNS servers yet',
	'settings.dns.localDnsSettings': 'Local DNS Settings',
	'settings.dns.splitDomain': 'Split Domain',
	'settings.dns.recordType.a': 'A Record',
	'settings.dns.customDomain.title': 'Custom Domain Resolution',
	'settings.dns.customDomain.description': 'Configure custom domain resolution',
	'settings.dns.customDomain.add': 'Add Custom Domain Resolution',
	'settings.dns.customDomain.notSet': 'You have not set up custom domain resolution or enabled device domain assignment yet',
	'settings.dns.customDomain.enableOverrideRequired': 'To use this feature, please enable "Override Local DNS"',
	'settings.dns.customDomain.deleteSuccess': 'Custom domain resolution deleted successfully',
	'settings.dns.customDomain.deleteFailed': 'Failed to delete custom domain resolution, please try again later',
	'settings.dns.addNameserver.success': 'DNS server added successfully',
	'settings.dns.addNameserver.failed': 'Failed to add DNS server, please try again later',
	'settings.dns.addNameserver.title': 'Add DNS Server',
	'settings.dns.addNameserver.dnsServerTitle': 'DNS Server',
	'settings.dns.addNameserver.dnsServerDescription': 'Use IPv4 or IPv6 addresses to resolve domain names',
	'settings.dns.addNameserver.applyToSpecificDomain': 'Apply to Specific Domain',
	'settings.dns.addNameserver.splitDnsDescription': 'This DNS server will only be used for certain domains',
	'settings.dns.addNameserver.domainTitle': 'Domain',
	'settings.dns.addNameserver.domainUsageDescription': 'Use this DNS server only for the following domains. For example, if your domain is example.com, this DNS server will only be used for example.com and its subdomains (e.g., www.example.com)',
	'settings.dns.validation.ipRequired': 'Please enter IP address',
	'settings.dns.validation.cannotUseDefaultIp': 'Cannot use default IP address ***************',
	'settings.dns.validation.invalidIp': 'Please enter a valid IP address',
	'settings.dns.validation.ipExists': 'This IP address already exists',
	'settings.dns.validation.domainRequired': 'Please enter domain name',
	'settings.dns.validation.invalidDomain': 'Domain should contain lowercase alphanumeric characters, hyphens and periods.',
	'settings.dns.editNameserver.success': 'DNS server edited successfully',
	'settings.dns.editNameserver.failed': 'Failed to edit DNS server, please try again later',
	'settings.dns.addSearchdomain.success': 'Domain server added successfully',
	'settings.dns.addSearchdomain.failed': 'Failed to add domain server, please try again later',
	'settings.dns.disableDns.title': 'Disable Device Domain Assignment',
	'settings.dns.disableDns.success': 'Device domain assignment disabled successfully',
	'settings.dns.disableDns.failed': 'Failed to disable device domain assignment, please try again later',
	'settings.dns.disableDns.description': 'Users in your network will no longer be able to access devices using device names.',
	'settings.dns.disableDns.warning': 'Note: Disabling device domain assignment will also disable HTTPS certificates.',
	'settings.dns.disableHttps.title': 'Disable HTTPS Certificate Issuance',
	'settings.dns.disableHttps.success': 'HTTPS certificate issuance disabled successfully',
	'settings.dns.disableHttps.failed': 'Failed to disable HTTPS certificate issuance, please try again later',
	'settings.dns.disableHttps.description': 'After disabling HTTPS certificate issuance, users in your network will not be able to access devices using HTTPS.',
	'settings.dns.addNameserver.ipPlaceholder': 'Enter IP address',
	'settings.dns.addSearchdomain.title': 'Add Custom Domain Resolution',
	'settings.dns.addSearchdomain.domainTitle': 'Domain',
	'settings.dns.addSearchdomain.domainPlaceholder': 'Enter domain name',
	'settings.dns.addSearchdomain.nameColumn': 'Name',
	'settings.dns.addSearchdomain.typeColumn': 'Type',
	'settings.dns.addSearchdomain.valueColumn': 'Value',
	'settings.dns.addSearchdomain.namePlaceholder': 'Name',
	'settings.dns.addSearchdomain.ipv4Placeholder': 'IPv4 Address',
	'settings.dns.validation.domainExists': 'Domain already exists',
	'settings.dns.domainNameTitle': 'Domain Name',
	'settings.dns.domainNameDescription': 'Unique name for DNS registration and TLS certificate issuance',
	'settings.dns.modifyDomainName': 'Modify Domain Name',
	'settings.dns.editSearchdomain.title': 'Edit Custom Domain Resolution',
	'settings.dns.editSearchdomain.success': 'Domain server edited successfully',
	'settings.dns.editSearchdomain.failed': 'Failed to edit domain server, please try again later',
	'settings.dns.editSearchdomain.domainTitle': 'Domain',
	'settings.dns.editSearchdomain.domainPlaceholder': 'Domain',
	'settings.dns.editSearchdomain.nameColumn': 'Name',
	'settings.dns.editSearchdomain.typeColumn': 'Type',
	'settings.dns.editSearchdomain.valueColumn': 'Value',
	'settings.dns.editSearchdomain.namePlaceholder': 'Name',
	'settings.dns.editSearchdomain.ipv4Placeholder': 'IPv4 Address',
	'settings.dns.editSearchdomain.cnamePlaceholder': 'Target, e.g.: www.example.com',
	'settings.dns.validation.cnameRequired': 'Please enter CNAME',
	'settings.dns.validation.invalidCname': 'CNAME should contain lowercase alphanumeric characters, hyphens and periods',
	'settings.dns.validation.recordExists': 'Record already exists',
	'settings.dns.validation.circularReference': 'Circular reference not allowed',
	'settings.dns.editNameserver.title': 'Edit DNS Server',
	'settings.dns.editNameserver.dnsServerTitle': 'DNS Server',
	'settings.dns.editNameserver.dnsServerDescription': 'Use IPv4 or IPv6 addresses to resolve domain names',
	'settings.dns.editNameserver.applyToSpecificDomain': 'Apply to Specific Domain',
	'settings.dns.editNameserver.splitDnsDescription': 'This DNS server will only be used for certain domains',
	'settings.dns.editNameserver.domainTitle': 'Domain',
	'settings.dns.editNameserver.domainUsageDescription': 'Use this DNS server only for the following domains. For example, if your domain is example.com, this DNS server will only be used for example.com and its subdomains (e.g., www.example.com).',
	'settings.dns.addSearchdomain.cnamePlaceholder': 'Target, e.g.: www.example.com',

	// MFA settings
	'settings.mfa.title': 'MFA',
	'settings.mfa.description': 'Manage MFA settings',
	'settings.mfa.sectionTitle': 'MFA',
	'settings.mfa.sectionDescription': 'Improve authentication security by adding two or more different authentication factors to the identity verification process',
	'settings.mfa.enableMfa': 'Enable MFA',

	// Common components
	'components.common.yes': 'Yes',
	'components.common.no': 'No',

	// Dashboard functions
	'dashboard.deviceManagement': 'Device Management',
	'dashboard.networkTopology': 'Network Topology',
	'dashboard.physicalPorts': 'Physical Ports',
	'dashboard.deviceMonitoring': 'Device Monitoring',

	// Policy management functions
	'policies.resourceGroup': 'Resource Group',
	'policies.expressions': 'Expressions',
	'policies.tags': 'Tags',

	// Policy selectors
	'policies.selector.all': 'All',
	'policies.selector.device': 'Device',
	'policies.selector.deviceGroup': 'Device Group',
	'policies.service': 'Service',

	// Policy group management
	'policies.group.input': 'Input',
	'policies.group.groupType': 'Type',
	'policies.group.static': 'Static Policy Group',
	'policies.group.dynamic': 'Dynamic Policy Group',
	'policies.group.expertMode': 'Expert Mode',

	// User selector
	'policies.user.fetchFailed': 'Failed to fetch user list, please try again later',

	// Search filter components
	'common.filter': 'Filter',
	'common.apply': 'Apply',

	// Services management
	'services.group.title': 'Service Group',
	'services.field.name': 'Service Name',
	'services.field.type': 'Service Type',
	'services.field.source': 'Service Source',
	'services.field.status': 'Service Status',
	'services.field.network': 'Network',
	'services.field.site': 'Site',
	'services.field.protocol': 'Service Protocol',
	'services.field.port': 'Service Port',
	'services.field.accessAddress': 'Service Access Address',
	'services.field.physicalAddress': 'Service Physical Address',
	'services.field.createTime': 'Service Create Time',
	'services.field.discoveryTime': 'Service Discovery Time',
	'services.field.deviceGroup': 'Device Group',
	'services.field.customTags': 'Custom Tags',

	// Service components (additional)
	'services.components.source.config': 'System Configuration',
	'services.components.source.detect': 'Auto Discovery',

	// Service actions
	'services.action.edit': 'Edit Service',
	'services.action.delete': 'Delete Service',
	'services.action.removeFromGroup': 'Remove from Group',
	'services.action.selectDevice': 'Select Device',

	// Service editing
	'services.edit.title': 'Edit Service',
	'services.field.selectServiceGroup': 'Select Service Group',

	// Service validation
	'services.validation.nameRequired': 'Name is required',
	'services.validation.nameFormat': "Name can only contain letters, numbers and '-'",

	// Service deletion (detailed)
	'services.delete.titleWithName': 'Delete Service {name}',
	'services.delete.warning': 'The service will be deleted and will no longer be accessible?',
	'services.delete.confirmInput': 'Type {name} to confirm deletion',

	// Policy labels (additional)
	'policies.label.resourceGroup': 'Resource Group',
	'policies.label.user': 'User',
	'policies.label.userGroup': 'User Group',
	'policies.label.builtinUserGroup': 'Built-in User Group',
	'policies.label.device': 'Device',
	'policies.label.deviceGroup': 'Device Group',
	'policies.label.service': 'Service',
	'policies.label.serviceGroup': 'Service Group',
	'policies.label.expression': 'Expression',
	'policies.label.l7Expression': 'Layer 7 Expression',
	'policies.label.tag': 'Tag',

	// Built-in user groups
	'policies.builtinGroup.self': 'Self',
	'policies.builtinGroup.members': 'Members',
	'policies.builtinGroup.internet': 'Internet Users',

	// Policy errors and sync
	'policies.error.fetchFailed': 'Failed to fetch access control policies, please try again later',
	'policies.sync.success': 'Sync successful',
	'policies.sync.failed': 'Sync failed, please try again later',

	// Policy tooltips
	'policies.tooltip.dynamicGroup': 'Dynamic Policy Group',

	// Port related
	'policies.port.label': 'Port',
	'policies.port.formatDescription': 'Port supports the following formats:',
	'policies.port.allPorts': 'All ports: *',
	'policies.port.singlePort': 'Single port: 22 or 80',
	'policies.port.rangePort': 'Port range: 1-65535 or 100-20000',
	'policies.port.multiplePorts': 'Multiple ports: 22,80,443 or 100-200,300-400 or 22,3389-3399',
	'policies.port.required': 'Port cannot be empty',
	'policies.port.invalidFormat': 'Invalid port format',

	// IP related
	'policies.ip.cidrSupport': 'Supports CIDR format',
	'policies.ip.placeholder': 'Please enter IP',
	'policies.ip.required': 'IP cannot be empty',

	// Device related
	'policies.device.placeholder': 'Please select device',
	'policies.device.noDevices': 'No devices',
	'policies.device.fetchFailed': 'Failed to fetch device list, please try again later',

	// User related
	'policies.user.placeholder': 'Please select user',
	'policies.user.noUsers': 'No users',
	'policies.user.orEnterUser': 'Or enter user',
	'policies.user.required': 'User cannot be empty',

	// Service related
	'policies.service.placeholder': 'Please select service',

	// Domain related
	'policies.domain.placeholder': 'Please enter domain',
	'policies.domain.required': 'Domain cannot be empty',

	// Service group related
	'policies.serviceGroup.placeholder': 'Please select service group',

	// IP related extensions
	'policies.ip.multiSupport': 'Supports CIDR format, multiple separated by commas',

	// Expression related
	'policies.expression.placeholder': 'Please select expression',
	'policies.expression.layer7': 'Layer 7 Network',

	// Resource group related
	'policies.resourceGroup.placeholder': 'Please select resource group',

	// Domain related extensions
	'policies.domain.label': 'Domain',
	// 数据导入导出基础
	'policies.dataExport.export': 'Export Policy',
	'policies.dataExport.modal.title': 'Policy Export',
	'policies.dataExport.modal.export': 'Export',


	// User auth key component
	'userAuthKey.title': 'Auth Code',
	'userAuthKey.description': 'This key is valid for 10 minutes and can only be used once',
	'userAuthKey.createFailed': 'Failed to create key, please try again later',
	'userAuthKey.refresh': 'Refresh',
	'userAuthKey.generateTime': 'Generate Time',
	'userAuthKey.scanInstruction': 'Please use App to scan QR code to access network',

	// User device component
	'userDevice.fetchFailed': 'Failed to fetch device list, please try again later',
	'userDevice.deviceList': 'Device List',
	'userDevice.viewUserDevices': 'View this user\'s device list',

	// User modal selector component
	'userModal.selectUser': 'Select User',
	'userModal.searchPlaceholder': 'Search by user information',
	'userModal.role': 'Role',
	'userModal.totalUsers': 'Total Users',
	'userModal.normalUser': 'Normal User',
	'userModal.admin': 'Administrator',

	// User selector component
	'userSelector.fetchFailed': 'Failed to fetch user list, please try again later',
	'userSelector.operator': 'Operator',
	'userSelector.noUsers': 'No users',

	// Time range component
	'timeRange.manualSelect': 'Manual Time Selection',
	'timeRange.quickSelect': 'Quick Selection',
	'timeRange.last5Minutes': 'Last 5 Minutes',
	'timeRange.last15Minutes': 'Last 15 Minutes',
	'timeRange.last30Minutes': 'Last 30 Minutes',
	'timeRange.last1Hour': 'Last 1 Hour',
	'timeRange.last3Hours': 'Last 3 Hours',
	'timeRange.last6Hours': 'Last 6 Hours',
	'timeRange.last24Hours': 'Last 24 Hours',
	'timeRange.last3Days': 'Last 3 Days',
	'timeRange.last7Days': 'Last 7 Days',
	'timeRange.last30Days': 'Last 30 Days',
	'timeRange.last90Days': 'Last 90 Days',

	// Services - Flynet Service
	'services.flynet.queryNetworkError': 'Network query error, please try again later',
	'services.flynet.enableMachineAuthSuccess': 'Enable new device approval successfully',
	'services.flynet.enableMachineAuthFailed': 'Failed to enable new device approval, please try again later',
	'services.flynet.disableMachineAuthSuccess': 'Disable new device approval successfully',
	'services.flynet.disableMachineAuthFailed': 'Failed to disable new device approval, please try again later',
	'services.flynet.updateMeshSuccess': 'Update Mesh mode successfully',
	'services.flynet.updateMeshFailed': 'Failed to update Mesh mode, please try again later',
	'services.flynet.updateAppPanelSuccess': 'Update app panel status successfully',
	'services.flynet.updateAppPanelFailed': 'Failed to update app panel status, please try again later',
	'services.flynet.updateKeyExpireSuccess': 'Update key expiration time successfully',
	'services.flynet.updateKeyExpireFailed': 'Failed to update key expiration time, please try again later',
	'services.flynet.updateNetworkNameSuccess': 'Update network name successfully',
	'services.flynet.updateNetworkNameFailed': 'Failed to update network name, please try again later',
	'services.flynet.updateRdpSettingsSuccess': 'Update remote desktop settings successfully',
	'services.flynet.updateRdpSettingsFailed': 'Failed to update remote desktop settings, please try again later',
	'services.flynet.updateSuccess': 'Update successfully',
	'services.flynet.updateFailed': 'Update failed, please try again later',

	// Services - Device Service
	'services.device.enable': 'Enable',
	'services.device.disable': 'Disable',
	'services.device.keyExpireSuccess': 'key expiration successfully',
	'services.device.keyExpireFailed': 'key expiration failed, please try again later',
	'services.device.meshModeSuccess': 'Mesh mode successfully',
	'services.device.meshModeFailed': 'Mesh mode failed, please try again later',
	'services.device.fetchDeviceListFailed': 'Failed to fetch device list, please try again later',
	'services.device.queryEmptyData': 'Query error, returned empty data',
	'services.device.fetchDeviceFailed': 'Failed to fetch device, please try again later',
	'services.device.approveDeviceSuccess': 'Approve device successfully',
	'services.device.approveDeviceFailed': 'Failed to approve device, please try again later',
	'services.device.updateRdpSettingsSuccess': 'Update remote desktop settings successfully',
	'services.device.updateRdpSettingsFailed': 'Failed to update remote desktop settings, please try again later',

	// Services - User Service
	'services.user.fetchUserFormFailed': 'Failed to fetch user form',

	// Services - File Service
	'services.file.uploadSuccess': 'Upload successfully',
	'services.file.uploadFailed': 'File upload failed',

	// Download page
	'download.title': 'Download {name}',
	'download.description': 'Install the app and log in to start using {name}',

	// Guide page
	'guide.title': 'Choose Your Purpose',
	'guide.description': 'Based on your purpose, we will provide different operation guides.',
	'guide.mesh.title': 'Mesh Network',
	'guide.mesh.content': 'Build your private network, connecting devices in different physical locations into an interconnected virtual logical network.',
	'guide.vpn.title': 'Zero Trust Network Access',
	'guide.vpn.content': 'A new zero trust access mode that replaces traditional VPN, protecting resources through strict identity authentication and authorization verification.',

	// Log push configuration detail
	'logs.pushConfig.detail.loadFailed': 'Failed to load push configuration details',

	// DNS log page
	'logs.dnsLog.description': 'DNS logs record domain name resolution records of user devices',
	'logs.dnsLog.switch.label': 'Enable DNS Logs',
	'logs.dnsLog.banner.title': 'DNS Logs Not Enabled',
	'logs.dnsLog.banner.content': 'Please go to {link} to enable and then view logs',
	'logs.dnsLog.banner.linkText': 'Settings/Data Settings/Logs',
	'logs.dnsLog.filter.user': 'User',
	'logs.dnsLog.filter.device': 'Device',
	'logs.dnsLog.filter.type': 'Type',
	'logs.dnsLog.filter.domain': 'Domain',
	'logs.dnsLog.filter.all': 'All',
	'logs.dnsLog.button.query': 'Query',
	'logs.dnsLog.tooltip.addToFilter': 'Add to search criteria',
	'logs.dnsLog.tooltip.removeFromFilter': 'Remove from search criteria',

	// Network flow log page
	'logs.networkFlowLog.description': 'Network logs record user network access behavior, including source IP address, target IP address, protocol, access time, and device information',
	'logs.networkFlowLog.switch.label': 'Enable Network Logs',
	'logs.networkFlowLog.banner.title': 'Network Logs Not Enabled',
	'logs.networkFlowLog.banner.content': 'Please go to {link} to enable and then view logs',
	'logs.networkFlowLog.banner.linkText': 'Settings/Data Settings/Logs',
	'logs.networkFlowLog.filter.user': 'User',
	'logs.networkFlowLog.filter.device': 'Device',
	'logs.networkFlowLog.filter.protocol': 'Protocol Type',
	'logs.networkFlowLog.filter.sourceIp': 'Source IP',
	'logs.networkFlowLog.filter.targetIp': 'Target IP',
	'logs.networkFlowLog.filter.all': 'All',
	'logs.networkFlowLog.button.query': 'Query',
	'logs.networkFlowLog.tooltip.addToFilter': 'Add to search criteria',
	'logs.networkFlowLog.tooltip.removeFromFilter': 'Remove from search criteria',
	'logs.networkFlowLog.traffic.exit': 'Exit Traffic',
	'logs.networkFlowLog.traffic.subnet': 'Subnet Traffic',
	'logs.networkFlowLog.traffic.virtual': 'Virtual Traffic',
	'logs.networkFlowLog.traffic.physical': 'Physical Traffic',

	// Service log page
	'logs.servicesLog.description': 'Service logs record user access to services',
	'logs.servicesLog.filter.user': 'User',
	'logs.servicesLog.filter.device': 'Device',
	'logs.servicesLog.filter.keywords': 'Keywords',
	'logs.servicesLog.filter.service': 'Service',
	'logs.servicesLog.filter.servicePlaceholder': 'Please select service',
	'logs.servicesLog.button.query': 'Query',
	'logs.servicesLog.tooltip.addToFilter': 'Add to search criteria',
	'logs.servicesLog.tooltip.removeFromFilter': 'Remove from search criteria',

	// Audit log page
	'logs.auditLog.description': 'Check logs of changes made to network configuration settings in the past 90 days',
	'logs.auditLog.banner.title': 'Looking for network traffic logs?',
	'logs.auditLog.banner.description': 'We do not record any information about network traffic as it may contain sensitive information.',
	'logs.auditLog.filter.timeRange': 'Time Range',
	'logs.auditLog.filter.keywords': 'Keywords',
	'logs.auditLog.filter.action': 'Action',
	'logs.auditLog.filter.actionPlaceholder': 'Please select action',
	'logs.auditLog.filter.target': 'Target',
	'logs.auditLog.button.query': 'Query',
	'logs.auditLog.tooltip.addToFilter': 'Add to search criteria',
	'logs.auditLog.tooltip.removeFromFilter': 'Remove from search criteria',
	'services.application.delete.title': 'Delete Application {name}',
	'services.application.delete.description': 'Application {name} {description} will be deleted and cannot be used after deletion.',
	'services.application.delete.confirmText': 'Enter {name} to confirm deletion',

	// Application switch
	'services.application.switch.title': 'Enable Application Panel',
	'services.application.switch.description': 'When enabled, the application panel will be displayed in user information and can be configured through the console.',
	'services.application.switch.applicationPanel': 'Application Panel',
	'services.application.edit.title': 'Edit Application',
	'services.application.edit.form.description': 'Description',
	'services.application.edit.form.descriptionPlaceholder': 'Please enter description',
	'services.application.new.title': 'New Application',
	'services.application.groupSort.title': 'Category Management',
	'services.application.groupSort.button.moveToTop': 'Move to Top',
	'services.application.groupSort.button.moveUp': 'Move Up',
	'services.application.groupSort.button.moveDown': 'Move Down',
	'services.application.groupSort.button.moveToBottom': 'Move to Bottom',
	'services.application.groupSort.confirmDelete': 'Are you sure you want to delete this category?',
	'services.application.groupSort.sortWarning': 'Tip: The sorting will take effect after clicking the confirm button',
	'services.application.import.validation.nameRequired': 'Name cannot be empty',
	'services.application.import.validation.urlRequired': 'URL cannot be empty',
	'services.application.import.validation.categoryRequired': 'Category cannot be empty',
	'services.application.import.validation.iconRequired': 'Icon cannot be empty',
	'services.application.import.validation.iconTypeError': 'Icon type error, should be 0: URL 1: Name',
	'services.application.import.validation.duplicateUrl': 'Duplicate URL information',

	// Services main page
	'services.index.title': 'Service List',
	'services.index.button.dataImport': 'Data Import',
	'services.index.button.application': 'Applications',
	'services.index.button.serviceGroup': 'Service Groups',
	'services.index.button.newService': 'New Service',
	'services.index.description': 'Manage services running on devices',
	'services.index.totalServices': 'Total Services',
	'services.index.endMessage': '---- End of List ----',

	// Service details
	'services.detail.error.loadDetailFailed': 'Failed to load service details, please try again later',
	'services.detail.nodes': 'Nodes',
	'services.detail.subnetNodes': 'Subnet Nodes',

	// Service group operations
	'services.group.create.success': 'Service group created successfully',
	'services.group.create.failed': 'Failed to create service group',
	'services.group.edit.success': 'Service group edited successfully',
	'services.group.edit.failed': 'Failed to edit service group',
	'services.group.delete.success': 'Service group deleted successfully',
	'services.group.delete.failed': 'Failed to delete service group',

	// Service editing
	'services.edit.error.loadServiceFailed': 'Failed to load service information, please try again later',
	'services.edit.error.selectDevice': 'Please select a device',
	'services.edit.error.addNodes': 'Please add nodes',
	'services.edit.error.addConnector': 'Please add connector',
	'services.edit.error.addSubnetNodes': 'Please add subnet nodes',
	'services.edit.success.updateService': 'Service updated successfully',
	'services.edit.error.updateServiceFailed': 'Failed to update service, please try again later',
	'services.edit.mode.direct': 'Direct Mode',
	'services.edit.mode.forward': 'Forward Mode',

	// Error messages
	'devices.error.getDeviceListFailed': 'Failed to get device list, please try again later',
	'devices.error.getUserListFailed': 'Failed to get user list, please try again later',

	// Device group table
	'devices.group.table.name': 'Name',
	'devices.group.table.description': 'Description',
	'devices.group.table.type': 'Type',
	'devices.group.table.deviceCount': 'Device Count',
	'devices.group.table.createdTime': 'Created Time',
	'devices.group.type.static': 'Static Device Group',
	'devices.group.type.dynamic': 'Dynamic Device Group',

	'devices.batchEditRoute.title': 'Batch Edit Subnet Routes for Device {deviceName}',
	'devices.batchEditRoute.description': 'One subnet route per line, three fields per line: enable immediately, subnet (CIDR format), description.',
	'devices.batchEditRoute.example': 'Example: true, **********/24, Office Area 1',
	'devices.batchEditRoute.warning': 'Modifications may cause some networks to become inaccessible. Please proceed with caution.',
	'devices.batchEditRoute.advertisedRoutes': 'Advertised Routes',
	'devices.batchEditRoute.excludeRoutes': 'Exclude Routes',
	'devices.batchEditRoute.updateSuccess': 'Batch edit subnet routes successfully',
	'devices.batchEditRoute.updateFailed': 'Failed to batch edit subnet routes, please try again later',

	'devices.rangeAccept.title': 'Set Service Reception Range',
	'devices.rangeAccept.serviceGroups': 'Service Groups',
	'devices.rangeAccept.networkServices': 'Network Services',
	'devices.rangeAccept.acceptServices': 'Accept Services',
	'devices.rangeAccept.excludeServices': 'Exclude Services',
	'devices.rangeAccept.getServicesFailed': 'Failed to get network services',

	'devices.editRejectRoute.title': 'Modify Reject Routes for Device {deviceName}',
	'devices.editRejectRoute.description': 'After setting reject routes for the device, it will no longer accept route advertisements from advertised route settings',
	'devices.editRejectRoute.placeholder': 'Please enter reject routes in CIDR format (e.g., **********/24), multiple entries separated by commas',
	'devices.editRejectRoute.updateSuccess': 'Reject routes updated successfully',
	'devices.editRejectRoute.updateFailed': 'Failed to update reject routes, please try again later',

	'logs.switch.title': 'Log Collection',
	'logs.switch.subtitle': 'Manage whether to enable the collection of related logs',

	'logs.destination.title': 'Push Destination Configuration',
	'logs.destination.subtitle': 'Push Destination Configuration',
	'logs.destination.new': 'New Configuration',
	'logs.destination.name': 'Name',
	'logs.destination.type': 'Destination Type',
	'logs.destination.status': 'Status',
	'logs.destination.enabled': 'Enabled',
	'logs.destination.disabled': 'Disabled',
	'logs.destination.edit': 'Edit',
	'logs.destination.delete': 'Delete',
	'logs.destination.createTitle': 'Create Push Destination',
	'logs.destination.createSuccess': 'Push destination created successfully',
	'logs.destination.createFailed': 'Failed to create push destination',
	'logs.destination.editTitle': 'Edit Push Destination',
	'logs.destination.editSuccess': 'Push destination updated successfully',
	'logs.destination.editFailed': 'Failed to update push destination',
	'logs.destination.deleteTitle': 'Delete Push Destination',
	'logs.destination.deleteConfirm': 'The push destination will be deleted and cannot be recovered. Confirm deletion?',
	'logs.destination.deleteInput': 'Enter {name} to confirm deletion',
	'logs.destination.deleteSuccess': 'Push destination deleted successfully',
	'logs.destination.deleteFailed': 'Failed to delete push destination',
	'logs.destination.nameRequired': 'Name is required',
	'logs.destination.code': 'Code',
	'logs.destination.codeHint': 'Cannot be modified after creation',
	'logs.destination.codeRequired': 'Code is required',
	'logs.destination.codeStartError': 'Code cannot start with -',
	'logs.destination.codeFormatError': 'Code can only contain letters, numbers and \'-\'',
	'logs.destination.remark': 'Remark',

	'fieldGroups.title': 'Field Group',
	'fieldGroups.noFields': 'No fields',
	'fieldGroups.key': 'Key',
	'fieldGroups.name': 'Name',
	'fieldGroups.type': 'Type',
	'fieldGroups.default': 'Default',
	'fieldGroups.placeholder': 'Placeholder',
	'fieldGroups.extra': 'Extra Info',
	'fieldGroups.rules': 'Validation Rules',
	'fieldGroups.addTitle': 'Add Field Group',
	'fieldGroups.editTitle': 'Edit Field Group',
	'fieldGroups.inputName': 'Please enter name',
	'fieldGroups.inputLabel': 'Please enter label',
	'fieldGroups.disabled': 'Disabled',
	'fieldGroups.enabled': 'Enabled',
	'fieldGroups.fieldList': 'Field List',
	'fieldGroups.addField': 'Add Field',
	'fieldGroups.fieldName': 'Field Name',
	'fieldGroups.inputFieldName': 'Please enter field name',
	'fieldGroups.label': 'Label',
	'fieldGroups.selectType': 'Please select type',
	'fieldGroups.input': 'Input',
	'fieldGroups.select': 'Select',
	'fieldGroups.radio': 'Radio',
	'fieldGroups.checkbox': 'Checkbox',
	'fieldGroups.inputNumber': 'Number Input',
	'fieldGroups.switch': 'Switch',
	'fieldGroups.date': 'Date Picker',
	'fieldGroups.time': 'Time Picker',
	'fieldGroups.datetime': 'DateTime Picker',
	'fieldGroups.textarea': 'Textarea',
	'fieldGroups.keyvalues': 'Key-Value',
	'fieldGroups.inputDefault': 'Please enter default value',
	'fieldGroups.inputPlaceholder': 'Please enter placeholder',
	'fieldGroups.inputExtra': 'Please enter extra info',
	'fieldGroups.readonly': 'Readonly',
	'fieldGroups.hidden': 'Hidden',
	'fieldGroups.preview': 'Preview',
	'fieldGroups.list': 'List',
	'fieldGroups.optionListTitle': 'Field Option List (for select, radio, checkbox, etc.)',
	'fieldGroups.addOption': 'Add Option',
	'fieldGroups.optionLabel': 'Label',
	'fieldGroups.optionValue': 'Value',
	'fieldGroups.ruleListTitle': 'Field Rule List',
	'fieldGroups.addRule': 'Add Rule',
	'fieldGroups.required': 'Required',
	'fieldGroups.whitespace': 'Allow Whitespace',
	'fieldGroups.format': 'Format',
	'fieldGroups.pattern': 'Pattern',
	'fieldGroups.minLength': 'Min Length',
	'fieldGroups.maxLength': 'Max Length',
	'fieldGroups.formatEmail': 'Email',
	'fieldGroups.formatPhone': 'Phone Number',
	'fieldGroups.formatIdCard': 'ID Card',
	'fieldGroups.formatNumber': 'Number',
	'fieldGroups.formatInteger': 'Integer',
	'fieldGroups.formatFloat': 'Float',
	'fieldGroups.formatArray': 'JSON Array',
	'logs.event.title': 'Push Event Configuration',
	'logs.event.new': 'New Configuration',
	'logs.event.subtitle': 'Push Event Configuration',
	'logs.event.name': 'Event Name',
	'logs.event.type': 'Event Type',
	'logs.event.type.audit': 'Audit Log',
	'logs.event.type.network': 'Network Log',
	'logs.event.type.dns': 'DNS Log',
	'logs.event.type.service': 'Service Log',
	'logs.event.type.device': 'Device Log',
	'logs.event.format': 'Log Format',
	'logs.event.status': 'Status',
	'logs.event.enabled': 'Enabled',
	'logs.event.disabled': 'Disabled',
	'logs.event.edit': 'Edit',
	'logs.event.delete': 'Delete',
	'logs.event.createTitle': 'Create Subscription Event Configuration',
	'logs.event.editTitle': 'Edit Subscription Event Configuration',
	'logs.event.ok': 'OK',
	'logs.event.cancel': 'Cancel',
	'logs.event.nameRequired': 'Name is required',
	'logs.event.remark': 'Remark',
	'logs.event.code': 'Code',
	'logs.event.codeHint': 'Cannot be modified after creation',
	'logs.event.codeRequired': 'Code is required',
	'logs.event.codeStartError': 'Code cannot start with -',
	'logs.event.codeFormatError': 'Code can only contain letters, numbers and \'-\'',
	'logs.event.logType': 'Log Type',
	'logs.event.createSuccess': 'Created successfully',
	'logs.event.createFailed': 'Creation failed',
	'logs.event.deleteTitle': 'Delete Push Event',
	'logs.event.deleteSuccess': 'Push event deleted successfully',
	'logs.event.deleteFailed': 'Failed to delete push event, please try again later',
	'logs.event.deleteConfirm': 'The push event will be deleted and cannot be recovered. Confirm deletion?',
	'logs.event.deleteInput': 'Enter {name} to confirm deletion',
}
