export const zhCN_menu = {
	// 主导航菜单
	'nav.overview': '总览',
	'nav.services': '服务',
	'nav.policies': '策略',
	'nav.logs': '日志',
	'nav.devices': '设备',
	'nav.users': '用户',
	'nav.resources': '资源',
	'nav.settings': '设置',
	'nav.networks': '网络',
	'nav.download': '下载',

	// 工具提示
	'tooltip.help': '查看帮助文档',
	'tooltip.theme': '选择主题',
	'tooltip.darkMode': '切换到暗色模式',
	'tooltip.lightMode': '切换到亮色模式',
	'tooltip.language': '切换语言',

	// 语言选项
	'language.chinese': '中文',
	'language.english': 'English',

	// 通用文本
	'common.expired': '已过期',
	'common.expiresSoon': '后到期',
	'common.accountExpired': '账号已过期',
	'common.clickToRenew': '点击此处续费',
	'common.expireTime': '过期时间',

	// 总览页面
	'overview.lastQueryTime': '上次查询时间',
	'overview.refresh.none': '无',
	'overview.refresh.5s': '5秒',
	'overview.refresh.10s': '10秒',
	'overview.refresh.30s': '30秒',
	'overview.refresh.1m': '1分',
	'overview.refresh.5m': '5分',
	'overview.refresh.15m': '15分',
	'overview.getOverviewFailed': '获取总览信息失败, 请稍后重试',

	// 地图组件
	'overview.map.title': '全网设备分布',
	'overview.map.totalDevices': '总设备',
	'overview.map.onlineDevices': '总在线',
	'overview.map.totalUsers': '总用户数',
	'overview.map.onlineUsers': '在线用户',
	'overview.map.totalDevicesCount': '总设备数',
	'overview.map.onlineDevicesCount': '在线设备',
	'overview.map.city': '城市',

	// 设备在线趋势
	'overview.deviceOnline.title': '设备连接数趋势',
	'overview.deviceOnline.deviceCount': '设备连接数',
	'overview.deviceOnline.recent6h': '最近6小时',
	'overview.deviceOnline.recent12h': '最近12小时',
	'overview.deviceOnline.recent24h': '最近24小时',
	'overview.deviceOnline.recent3d': '最近3天',
	'overview.deviceOnline.recent7d': '最近7天',

	// 设备类型分布
	'overview.deviceType.title': '设备类型分布',
	'overview.deviceType.name': '设备类型',

	// 网络流量
	'overview.traffic.title': '网络流量',
	'overview.traffic.recent6h': '最近6小时',
	'overview.traffic.recent12h': '最近12小时',
	'overview.traffic.recent24h': '最近24小时',
	'overview.traffic.recent3d': '最近3天',
	'overview.traffic.recent7d': '最近7天',
	'overview.traffic.upPeak': '上行流量峰值',
	'overview.traffic.downPeak': '下行流量峰值',
	'overview.traffic.upTotal': '上行流量总计',
	'overview.traffic.downTotal': '下行流量总计',
	'overview.traffic.receive': '接收',
	'overview.traffic.send': '发送',

	// 客户端版本
	'overview.clientVersion.title': '客户端版本分布',
	'overview.clientVersion.name': '客户端版本',
	'overview.clientVersion.deviceType': '设备类型',

	// 组件通用文本
	'components.common.cancel': '取消',
	'components.common.confirm': '确定',
	'components.common.close': '关闭',
	'components.common.save': '保存',
	'components.common.delete': '删除',
	'components.common.edit': '编辑',
	'components.common.add': '添加',
	'components.common.new': '新建',
	'components.common.search': '搜索',
	'components.common.reset': '重置',
	'components.common.submit': '提交',
	'components.common.loading': '加载中...',
	'components.common.noData': '暂无数据',
	'components.common.all': '全部',
	'components.common.online': '在线',
	'components.common.offline': '离线',
	'components.common.status': '状态',
	'components.common.operatingSystem': '操作系统',
	'components.common.pleaseSelect': '请选择',

	// DND 拖拽组件
	'components.dnd.dragToSort': '注：可以拖动上面的字段进行排序',

	// 中继映射管理器组件
	'components.relayMapManager.title': '中继服务器配置',
	'components.relayMapManager.description': '中继服务器配置包含了中继服务器的名称、地址、ID等信息。',
	'components.relayMapManager.saveConfig': '保存配置',

	// 设备选择器
	'components.machineSelector.title': '选择设备',
	'components.machineSelector.searchPlaceholder': '根据用户，设备名称，IP，版本号等搜索',

	// 客服组件
	'components.customerService.title': '联系专属客服',
	'components.customerService.description': '请使用微信扫码联系专属客服',

	// 主题切换
	'components.themeSwitch.feiyueCloud': '飞越云',
	'components.themeSwitch.feiyueCloudColor': '飞越云彩色主题',

	// 展开组件
	'components.expandable.expand': '+ 展开',
	'components.expandable.collapse': '- 收起',

	// 激活组件
	'components.activation.title': '输入授权码',
	'components.activation.machineCode': '机器码',
	'components.activation.licenseKey': '授权码',
	'components.activation.getLicenseKey': '获取授权码',
	'components.activation.placeholder': '请在此处粘贴或输入您的授权码',
	'components.activation.success': '授权成功',
	'components.activation.failed': '授权失败',

	// 用户指示器
	'components.userIndicator.profile': '个人中心',
	'components.userIndicator.appPanel': '应用面板',
	'components.userIndicator.logout': '退出登录',

	// 错误页面
	'components.error.goLogin': '去登录',
	'components.error.backHome': '回到首页',

	// 加载组件
	'components.loading.text': '正在加载中...',

	// 策略页面
	'policies.title': '策略列表',
	'policies.description': '定义允许设备和用户在网络中进行连接的策略',
	'policies.lastSyncTime': '上次同步时间',
	'policies.policyGroup': '策略组',
	'policies.addPolicy': '添加策略',
	'policies.addPolicyNew': '添加策略(新)',
	'policies.allPolicies': '所有策略',
	'policies.totalCount': '策略总数',
	'policies.syncRequired': '当前策略已经修改，需要同步才能生效',
	'policies.syncNow': '立即生效',
	'policies.getACLPolicyFailed': '获取访问控制策略失败, 请稍后重试',
	'policies.saveFailed': '保存失败, 请稍后重试',
	'policies.saveSuccess': '保存成功',
	'policies.syncSuccess': '同步成功',
	'policies.syncFailed': '同步失败, 请稍后重试',

	// 策略表格列
	'policies.table.policyName': '策略名称',
	'policies.table.priority': '优先级',
	'policies.table.priorityTooltip': '优先级范围为1-100，默认为1，即最高优先级。',
	'policies.table.policyGroup': '所属策略组',
	'policies.table.source': '源',
	'policies.table.action': '动作',
	'policies.table.actionTooltip': '优先级相同的情况下，拒绝动作优于允许动作。',
	'policies.table.target': '目标',
	'policies.table.status': '状态',

	// 策略动作
	'policies.action.allow': '允许',
	'policies.action.deny': '拒绝',
	'policies.action.enable': '启用',
	'policies.action.disable': '禁用',
	'policies.action.enabled': '启用',
	'policies.action.disabled': '禁用',

	// 策略操作菜单
	'policies.menu.enablePolicy': '启用策略',
	'policies.menu.disablePolicy': '禁用策略',
	'policies.menu.editPolicy': '编辑策略',
	'policies.menu.deletePolicy': '删除策略',

	// 策略资源类型
	'policies.resource.all': '全部',
	'policies.resource.ip': 'IP',
	'policies.resource.ipGroup': '资源组',
	'policies.resource.user': '用户',
	'policies.resource.userGroup': '用户组',
	'policies.resource.autoGroup': '内置用户组',
	'policies.resource.device': '设备',
	'policies.resource.deviceGroup': '设备组',
	'policies.resource.service': '服务',
	'policies.resource.serviceGroup': '服务组',
	'policies.resource.expression': '表达式',
	'policies.resource.l7Expression': '7层表达式',
	'policies.resource.tag': '标签',

	// 内置用户组
	'policies.autoGroup.self': '自已',
	'policies.autoGroup.members': '普通用户',
	'policies.autoGroup.internet': '公网用户',

	// 策略组类型
	'policies.groupType.dynamic': '动态策略组',
	'policies.groupType.static': '静态策略组',

	// 搜索过滤
	'policies.filter.searchPlaceholder': '根据策略名称、描述、源、目标等搜索',
	'policies.filter.query': '查询',
	'policies.filter.user': '用户',
	'policies.filter.status': '状态',

	// 策略删除
	'policies.delete.title': '删除策略',
	'policies.delete.description': '确定要删除策略吗？',
	'policies.delete.policyName': '策略名称',
	'policies.delete.deleteSuccess': '删除成功',
	'policies.delete.deleteFailed': '删除失败',

	// 策略启用/禁用
	'policies.suspend.enableTitle': '启用策略',
	'policies.suspend.disableTitle': '禁用策略',
	'policies.suspend.enableDescription': '确定要启用策略吗？',
	'policies.suspend.disableDescription': '确定要禁用策略吗？',
	'policies.suspend.enableSuccess': '启用成功',
	'policies.suspend.disableSuccess': '禁用成功',
	'policies.suspend.enableFailed': '启用失败',
	'policies.suspend.disableFailed': '禁用失败',

	// 策略编辑
	'policies.edit.title': '编辑策略',
	'policies.edit.name': '策略名称',
	'policies.edit.description': '描述',
	'policies.edit.priority': '优先级',
	'policies.edit.source': '源',
	'policies.edit.target': '目标',
	'policies.edit.action': '动作',
	'policies.edit.editSuccess': '编辑成功',
	'policies.edit.editFailed': '编辑失败',

	// 策略新建
	'policies.new.title': '新建策略',
	'policies.new.createSuccess': '创建成功',
	'policies.new.createFailed': '创建失败',
	'policies.new.duplicateName': '策略{name}名称重复',
	'policies.new.duplicatePolicy': '策略和已有策略{name}重复',
	'policies.new.nameRequired': '名称不能为空',
	'policies.new.policyName': '策略名称',
	'policies.new.priority': '优先级',
	'policies.new.priorityTooltip': '优先级范围为1-100，默认为1，即最高优先级。',
	'policies.new.status': '状态',
	'policies.new.enabled': '启用',
	'policies.new.disabled': '禁用',
	'policies.new.description': '策略描述',
	'policies.new.policyGroup': '所属策略组',
	'policies.new.source': '源',
	'policies.new.target': '目标',
	'policies.new.action': '动作',

	// 策略组
	'policies.group.title': '策略组',
	'policies.group.name': '组名',
	'policies.group.alias': '别名',
	'policies.group.description': '描述',
	'policies.group.type': '类型',
	'policies.group.createTime': '创建时间',
	'policies.group.addGroup': '添加策略组',
	'policies.group.editGroup': '编辑策略组',
	'policies.group.deleteGroup': '删除策略组',
	'policies.group.createSuccess': '创建成功',
	'policies.group.editSuccess': '编辑成功',
	'policies.group.deleteSuccess': '删除成功',
	'policies.group.createFailed': '创建失败',
	'policies.group.editFailed': '编辑失败',
	'policies.group.deleteFailed': '删除失败',
	'policies.group.nameRequired': '名称不能为空',
	'policies.group.codeRequired': '编码不能为空',
	'policies.group.codeCannotStartWithDash': '编码不能以-开头',
	'policies.group.codeInvalidFormat': '编码只能包含字母、数字和\'-\'',
	'policies.group.code': '编码',
	'policies.group.codeTooltip': '新建完成后不可修改',
	'policies.group.remark': '备注',
	'policies.group.parentGroup': '上级策略组',
	'policies.group.pleaseSelect': '请选择',
	'policies.group.getFailed': '获取策略组失败',
	'policies.group.deleteConfirmation': '策略组将被删除，删除后将无法给该策略组分配策略。',
	'policies.group.deleteInputConfirm': '输入',
	'policies.group.deleteInputConfirmSuffix': '以确认删除',
	'policies.group.policyCount': '策略数',
	'policies.group.viewPolicies': '查看策略',
	'policies.group.addSubGroup': '新建下级策略组',

	// IP组
	'policies.ipGroup.title': 'IP组',
	'policies.ipGroup.name': '组名',
	'policies.ipGroup.description': '描述',
	'policies.ipGroup.ipList': 'IP列表',
	'policies.ipGroup.addGroup': '添加IP组',
	'policies.ipGroup.editGroup': '编辑IP组',
	'policies.ipGroup.deleteGroup': '删除IP组',

	// 表达式
	'policies.expression.title': '表达式',
	'policies.expression.name': '表达式名称',
	'policies.expression.content': '表达式内容',
	'policies.expression.addExpression': '添加表达式',
	'policies.expression.editExpression': '编辑表达式',
	'policies.expression.deleteExpression': '删除表达式',
	'policies.expression.description': '表达式是一组由逻辑运算符连接的条件，用于描述请求上下文中的属性是否包含指定的值。表达式的值可以是字符串、数字、布尔值、列表或者字典。',
	'policies.expression.editPropertyDefinition': '编辑属性定义',
	'policies.expression.preview': '预览',
	'policies.expression.serialNumber': '序号',
	'policies.expression.property': '属性',
	'policies.expression.operation': '操作',
	'policies.expression.value': '值',
	'policies.expression.nameRequired': '名称不能为空',
	'policies.expression.nameCannotContainPrefix': '名称不能包含前缀',
	'policies.expression.duplicateName': '名称和已有表达式重复',

	// 通用操作
	'policies.common.save': '保存',
	'policies.common.cancel': '取消',
	'policies.common.confirm': '确定',
	'policies.common.delete': '删除',
	'policies.common.edit': '编辑',
	'policies.common.add': '添加',
	'policies.common.close': '关闭',
	'policies.common.loading': '加载中...',
	'policies.common.noData': '暂无数据',
	'policies.common.required': '必填项',
	'policies.common.optional': '可选项',
	'policies.common.preview': '预览',
	'policies.common.search': '搜索',
	'policies.common.searchByName': '根据名称搜索',
	'policies.common.searchByIpOrMemo': '根据IP或备注搜索',
	'policies.common.notSelected': '未选中',
	'policies.common.pleaseSelect': '请选择',
	'policies.common.apply': '应用',

	// 标签管理
	'policies.tagowners.title': '标签',
	'policies.tagowners.description': '定义可以应用于设备的标签，以及允许分配每个标签的用户列表',
	'policies.tagowners.name': '名称',
	'policies.tagowners.users': '用户',
	'policies.tagowners.nameRequired': '名称不能为空',
	'policies.tagowners.nameCannotContainPrefix': '名称不能包含前缀',
	'policies.tagowners.nameExists': '名称已存在',

	// 主机别名管理
	'policies.hosts.title': '资源组',
	'policies.hosts.description': 'IP别名允许您为 IP 地址或 IP 范围定义一个人性化的名称，以提高访问规则的可读性',
	'policies.hosts.addAlias': '添加IP别名',
	'policies.hosts.alias': '别名',
	'policies.hosts.ipOrRange': 'IP或IP段或别名',

	// 自动审批规则
	'policies.autoapprovers.title': '自动审批规则',
	'policies.autoapprovers.description': '定义无需管理控制台批准即可执行某些操作的用户列表，其中包括将一组指定的路由宣告为子网路由或出口节点',
	'policies.autoapprovers.subnetRoute': '子网路由',
	'policies.autoapprovers.userOrTag': '用户或标签',
	'policies.autoapprovers.exitNode': '出口节点',
	'policies.autoapprovers.user': '用户',
	'policies.autoapprovers.tag': '标签',
	'policies.autoapprovers.device': '设备',

	// 编辑策略组
	'policies.editGroup.title': '编辑策略的策略组',
	'policies.editGroup.description': '策略{name}的策略组',
	'policies.editGroup.placeholder': '请选择策略组',
	'policies.editGroup.success': '编辑策略的策略组成功',
	'policies.editGroup.failed': '编辑策略的策略组失败, 请稍后重试',

	// 从策略组移除策略
	'policies.removeFromGroup.title': '从策略组删除策略',
	'policies.removeFromGroup.confirmMessage': '确定把策略 {policyName} 从策略组 {groupName} 中删除吗？',
	'policies.removeFromGroup.successTitle': '操作成功',
	'policies.removeFromGroup.successMessage': '策略{policyName}已从策略组{groupName}中删除',
	'policies.removeFromGroup.failedTitle': '操作失败',
	'policies.removeFromGroup.failedMessage': '策略{policyName}从策略组{groupName}中删除失败',

	// 用户组管理
	'policies.groups.title': '用户组',
	'policies.groups.preview': '预览',
	'policies.groups.description': '用户组为一组用户定义一个简称，然后您可以在ACL规则中使用这个简称，而不用明确列出每个用户。您对组成员的任何更改都会自动传播到所有引用该组的规则中。',
	'policies.groups.name': '名称',
	'policies.groups.users': '用户',
	'policies.groups.allUsers': '全部用户',
	'policies.groups.specificUsers': '指定用户',
	'policies.groups.nameRequired': '名称不能为空',
	'policies.groups.nameCannotContainPrefix': '名称不能包含前缀',
	'policies.groups.nameExists': '名称已存在',
	'policies.groups.usersRequired': '用户不能为空',

	// 设备管理
	'devices.title': '设备列表',
	'devices.description': '管理接入网络和待审批的设备',
	'devices.dataExport': '数据导出',
	'devices.dataImport': '数据导入',
	'devices.deviceGroup': '设备组',
	'devices.tags': '标签',
	'devices.deploy': '部署',
	'devices.totalCount': '设备总数',
	'devices.endMessage': '---- 到底了 ----',

	// 设备表格
	'devices.table.device': '设备',
	'devices.table.deviceGroup': '所属设备组',
	'devices.table.ip': 'IP',
	'devices.table.version': '版本',
	'devices.table.lastOnlineTime': '上次在线时间',
	'devices.table.online': '在线',
	'devices.table.meshEnabled': 'Mesh模式开启',
	'devices.table.meshDisabled': 'Mesh模式关闭',
	'devices.table.meshEnabledTooltip': 'Mesh模式开启，设备之间可以通过零信任组网互相通信（访问控制允许的情况下）',
	'devices.table.meshDisabledTooltip': 'Mesh模式关闭，设备之间无法通过零信任组网互相通信, 只能通过子网路由的模式被访问',

	// 设备过滤器
	'devices.filter.search': '根据设备名称、用户、IP、版本号、标签等搜索',
	'devices.filter.query': '查询',
	'devices.filter.selectOS': '选择系统',
	'devices.filter.operatingSystem': '操作系统',
	'devices.filter.selectMeshMode': '选择Mesh模式',
	'devices.filter.meshMode': 'Mesh模式',
	'devices.filter.enable': '开启',
	'devices.filter.disable': '关闭',
	'devices.filter.selectDeviceGroup': '请选择设备组',
	'devices.filter.deviceGroup': '设备组',
	'devices.filter.apply': '应用',

	// 设备额外功能
	'devices.connectorGroup': '连接器组',
	'devices.searchPlaceholder': '根据用户、设备名称、IP、版本号、标签等搜索',
	'devices.status': '状态',
	'devices.all': '全部',
	'devices.meshMode': 'mesh模式',
	'devices.enabled': '已开启',
	'devices.disabled': '未开启',
	'devices.allDevices': '所有设备',

	// 设备详情
	'devices.detail.allowAccess': '允许接入',
	'devices.detail.viewLogs': '查看日志',
	'devices.detail.modifyDeviceName': '修改设备名称',
	'devices.detail.enableKeyExpiry': '启用密钥过期',
	'devices.detail.disableKeyExpiry': '禁用密钥过期',
	'devices.detail.viewConfigLogs': '查看配置日志',
	'devices.detail.editRouteSettings': '编辑路由设置',
	'devices.detail.setRejectRoutes': '设置拒绝接受路由',
	'devices.detail.editRelayNode': '编辑中继节点',
	'devices.detail.editMeshMode': '编辑Mesh模式',
	'devices.detail.editAccessControlTags': '编辑访问控制标签',
	'devices.detail.forceKeyExpiry': '密钥强制过期',
	'devices.detail.deleteDevice': '删除设备',
	'devices.detail.editDeviceGroup': '编辑设备组',
	'devices.detail.tags': '标签',
	'devices.detail.user': '用户',
	'devices.detail.tagsTooltip': '打上标签后，设备的权限由访问控制中的标签维护',
	'devices.detail.userTooltip': '默认为设备的接入者',
	'devices.detail.status': '状态',
	'devices.detail.deviceGroup': '设备组',
	'devices.detail.previewSubnetRouteFailed': '预览子网路由失败，请稍后重试',
	'devices.detail.getDeviceRouteFailed': '获取设备路由信息失败，请稍后重试',
	'devices.detail.getDeviceFailed': '获取设备失败，请稍后重试',
	'devices.detail.getRelayRouteFailed': '获取中继路由信息失败，请稍后重试',
	'devices.detail.getDNSFailed': '获取设备DNS信息失败，请稍后重试',
	'devices.detail.deviceDetailTitle': '设备详情',
	'devices.detail.deviceDetailDesc': '设备和网络详情，供排查网络问题时查看。',
	'devices.detail.routeSettings': '路由设置',
	'devices.detail.connector': '连接器',
	'devices.detail.setRoute': '设置路由',
	'devices.detail.connectorDesc': '连接器将分散在各处的物理网络联结成一个可以互相访问的网络。',
	'devices.detail.announceRoute': '宣告路由',
	'devices.detail.noAnnounceRoute': '这台设备没有宣告任何路由',
	'devices.detail.excludeRoute': '排除路由',
	'devices.detail.noExcludeRoute': '这台设备没有任何排除路由',
	'devices.detail.preview': '预览',
	'devices.detail.noPreviewRoute': '这台设备没有宣告任何路由',
	'devices.detail.rejectRoute': '拒绝接受路由',
	'devices.detail.setRejectRoute': '设置拒绝接受路由',
	'devices.detail.noRejectRoute': '这台设备没有拒绝任何路由',
	'devices.detail.rejectRouteDesc': '设备的拒绝接受路由设置后,将不再接受宣告路由设置里的路由宣告。',
	'devices.detail.networkSettings': '网络设置',
	'devices.detail.networkSettingsDesc': '设备的网络设置可以在多个维度上进行，优先级从高到低为 设备>设备组>用户>用户组>全局，设备上的设置优先级最高，全局最低。例如，优先使用设备上设置的DNS服务器，如果设备上未设置，则依次使用设备组，用户，用户组，全局上的DNS服务器设置。',
	'devices.detail.securityRiskBanner': '该设备存在安全风险',
	'devices.detail.riskLevelHigh': '高危',
	'devices.detail.riskLevelMedium': '中危',
	'devices.detail.riskLevelLow': '低危',
	'devices.detail.creator': '创建人',
	'devices.detail.deviceName': '设备名',
	'devices.detail.hostname': '主机名',
	'devices.detail.os': '操作系统',
	'devices.detail.version': '版本号',
	'devices.detail.createdAt': '创建时间',
	'devices.detail.lastSeen': '最近上线时间',
	'devices.detail.endpoint': '端点信息',
	'devices.detail.keyExpiry': '密钥过期时间',
	'devices.detail.preferredRelay': '首选中继',
	'devices.detail.editDomainTitle': '修改当前设备自定义域名解析',
	'devices.detail.editDnsTitle': '修改当前设备DNS服务器',
	'devices.detail.editRelayMapTitle': '修改当前设备中继路由',
	'devices.detail.saveDnsSuccess': '保存DNS信息成功',
	'devices.detail.saveDnsFailed': '保存DNS信息失败，请稍后重试',
	'devices.detail.saveRelayMapSuccess': '保存中继路由信息成功',
	'devices.detail.saveRelayMapFailed': '保存中继路由信息失败，请稍后重试',
	'devices.detail.deviceGroupLabel': '设备组: {group}',
	'devices.detail.userLabel': '用户: {user}',
	'devices.detail.userGroupLabel': '用户组: {group}',
	'devices.detail.riskRegistryModified': '系统注册表被篡改',
	'devices.detail.riskPasswordShort': '系统密码太短',
	'devices.detail.riskAutoUpdateOff': '系统未开启自动更新',
	'devices.detail.currentDevice': '当前设备',

	// 设备组管理
	'devices.group.title': '设备组列表',
	'devices.group.createGroup': '新建设备组',
	'devices.group.searchPlaceholder': '根据设备组名称、编码、备注查询',
	'devices.group.query': '查询',

	// 设备操作
	'devices.delete.title': '删除设备',
	'devices.delete.description': '这台设备将从网络中删除，同时该设备所提供服务将在网络中不可用。再次添加该设备需要重新认证。',
	'devices.delete.confirmText': '输入',
	'devices.delete.confirmAction': '确认删除',
	'devices.delete.success': '删除设备成功',
	'devices.delete.failed': '删除设备失败，请稍后重试',

	'devices.rename.title': '修改设备名称',
	'devices.rename.description': '显示在控制台、客户端的设备名称',
	'devices.rename.autoGenerate': '根据设备信息自动生成',
	'devices.rename.deviceName': '设备名称',
	'devices.rename.deviceDescription': '描述',
	'devices.rename.success': '重命名设备成功',
	'devices.rename.failed': '重命名设备失败',

	'devices.expiry.title': '这台设备的密钥将被强制过期。',
	'devices.expiry.description': '设备密钥过期后，该设备将无法与网络内的其他设备进行通信，如需恢复，请重新对该设备进行身份认证。',
	'devices.expiry.success': '强制过期设备成功',
	'devices.expiry.failed': '强制过期设备失败，请稍后重试',

	'devices.deploy.title': '部署设备',

	// 数据导出导入
	'devices.dataExport.title': '导出记录',
	'devices.dataExport.exportDevice': '导出设备',
	'devices.dataExport.searchPlaceholder': '根据文件名、导出参数查询',
	'devices.dataExport.fileName': '文件名',
	'devices.dataExport.download': '下载',
	'devices.dataExport.operator': '操作人',
	'devices.dataExport.unknown': '未知',
	'devices.dataExport.recordCount': '数据条数',
	'devices.dataExport.exportTime': '导出时间',
	'devices.dataExport.exportParams': '导出参数',
	'devices.dataExport.none': '无',
	'devices.dataImport.title': '导入记录',
	'devices.dataImport.importDevice': '导入设备',
	'devices.dataImport.searchPlaceholder': '根据文件名、导入参数查询',
	'devices.dataImport.fileName': '文件名',
	'devices.dataImport.download': '下载',
	'devices.dataImport.totalLines': '数据行数',
	'devices.dataImport.errorLines': '错误行数',
	'devices.dataImport.createLines': '创建行数',
	'devices.dataImport.updateLines': '更新行数',
	'devices.dataImport.isFinished': '是否完成',
	'devices.dataImport.yes': '是',
	'devices.dataImport.no': '否',
	'devices.dataImport.operator': '操作人',
	'devices.dataImport.unknown': '未知',
	'devices.dataImport.importTime': '导入时间',

	// 设备组操作
	'devices.groupAdd.title': '新建设备组',
	'devices.groupAdd.name': '名称',
	'devices.groupAdd.nameRequired': '名称不能为空',
	'devices.groupAdd.code': '编码',
	'devices.groupAdd.codeTooltip': '新建完成后不可修改',
	'devices.groupAdd.codeRequired': '编码不能为空',
	'devices.groupAdd.codeCannotStartWithDash': '编码不能以-开头',
	'devices.groupAdd.codeInvalidFormat': '编码只能包含字母、数字和\'-\'',
	'devices.groupAdd.remarks': '备注',
	'devices.groupAdd.type': '类型',
	'devices.groupAdd.staticGroup': '静态设备组',
	'devices.groupAdd.dynamicGroup': '动态设备组',
	'devices.groupAdd.devices': '设备',
	'devices.groupAdd.input': '输入',
	'devices.groupAdd.expressionEmpty': '表达式不能为空',
	'devices.groupAdd.triggerParamError': '触发参数错误',
	'devices.groupAdd.paramCombination': '参数组合',
	'devices.groupAdd.paramCombinationHelp': '运算符支持 and(与)、or (或)、or (或)、not (非)、()(括号)，例如：1 and 2，1 or 2, not 1,  1 or (not 1)',
	'devices.groupAdd.dynamicGroupAttributeEmpty': '动态设备组属性为空, 请前往',
	'devices.groupAdd.settings': '设置',
	'devices.groupAdd.createSuccess': '创建设备组成功',
	'devices.groupAdd.createFailed': '创建设备组失败',

	'devices.groupEdit.title': '编辑设备组',
	'devices.groupEdit.updateSuccess': '更新设备组成功',
	'devices.groupEdit.updateFailed': '更新设备组失败',

	'devices.groupDel.title': '删除设备组',
	'devices.groupDel.description': '设备组将被删除，删除后将无法给该设备组分配设备。',
	'devices.groupDel.confirmText': '输入',
	'devices.groupDel.confirmSuffix': '以确认删除',
	'devices.groupDel.deleteSuccess': '删除设备组成功',
	'devices.groupDel.deleteFailed': '删除设备组失败，请稍后重试',

	'devices.groupDns.title': '网络设置',
	'devices.groupDns.getRelayMapFailed': '获取中继路由信息失败，请稍后重试',
	'devices.groupDns.getDnsConfigFailed': '获取设备DNS信息失败，请稍后重试',
	'devices.groupDns.saveSuccess': '保存成功',
	'devices.groupDns.saveFailed': '保存失败，请稍后重试',
	'devices.groupDns.saveRelayMapFailed': '保存中继路由信息失败，请稍后重试',

	// DNS管理组件相关
	'devices.dnsManager.title': 'DNS服务器',
	'devices.dnsManager.description': '设置设备使用的DNS服务器',
	'devices.dnsManager.restrictedDomain': '限定域',
	'devices.dnsManager.globalDNS': '全局DNS服务器',
	'devices.dnsManager.overrideLocalDNS': '覆盖本地DNS',
	'devices.dnsManager.overrideLocalDNSDescription': '启用该选项后，客户端将忽略本地 DNS 设置并始终使用全局DNS服务器。<br />禁用该选项时，客户端优先使用本地 DNS 设置，仅在需要时使用全局DNS服务器。',
	'devices.dnsManager.noGlobalDNSConfigured': '您还没有设置全局DNS服务器',
	'devices.dnsManager.localDNSSettings': '本地DNS设置',
	'devices.dnsManager.addDNSServer': '添加DNS服务器',
	'devices.dnsManager.ipDescription': '使用 IPv4 或 IPv6 地址解析域名',
	'devices.dnsManager.applyToSpecificDomain': '应用于特定域名',
	'devices.dnsManager.splitDNSDescription': '该DNS服务器将仅用于某些域名',
	'devices.dnsManager.domain': '域名',
	'devices.dnsManager.domainUsageDescription': '仅在以下域名中使用此DNS服务器。例如，如果您的域名是 example.com，则此DNS服务器将仅用于 example.com 和其子域名（例如，www.example.com）',
	'devices.dnsManager.validation.ipRequired': '请输入IP地址',
	'devices.dnsManager.validation.cannotUseDefaultIP': '不能使用默认IP地址***************',
	'devices.dnsManager.validation.invalidIP': '请输入正确的IP地址',
	'devices.dnsManager.validation.ipExists': '该IP地址已存在',
	'devices.dnsManager.validation.domainRequired': '请输入域名',
	'devices.dnsManager.validation.invalidDomainFormat': '域名应包含字母数字小写字符、连字符和句点',
	'devices.dnsManager.editDNSServer': '编辑DNS服务器',

	// 页面标题相关
	'pages.overview': '总览',
	'pages.dashboard': '看板',
	'pages.deviceMonitor': '设备监控',
	'pages.physicalInterface': '物理网口',
	'pages.deviceManagement': '设备管理',
	'pages.networkTopology': '网络拓扑',
	'pages.networks': '网络',
	'pages.devices': '设备',
	'pages.deviceDetail': '设备详情',
	'pages.deviceLogs': '设备日志',
	'pages.deviceNetworkLogs': '设备网络日志',
	'pages.deviceGroup': '设备组',
	'pages.deviceImport': '设备导入',
	'pages.deviceImportDetail': '设备导入详情',
	'pages.deviceExport': '设备导出',
	'pages.settings': '设置',
	'pages.users': '用户',
	'pages.userDetail': '用户详情',
	'pages.userGroup': '用户组',
	'pages.userGroupDetail': '用户组详情',
	'pages.userImport': '用户导入',
	'pages.userImportDetail': '用户导入详情',
	'pages.userExport': '用户导出',
	'pages.userExportDetail': '用户导出详情',
	'pages.settingsUserImport': '用户导入',
	'pages.sangforUserImport': '深信服用户导入',
	'pages.oidcImport': 'OIDC导入',
	'pages.resources': '资源',
	'pages.policies': '策略',
	'pages.accessPolicies': '访问策略',
	'pages.policyGroup': '策略组',
	'pages.accessControls': '策略',
	'pages.sangforPolicyImport': '深信服策略导入',
	'pages.policyImport': '策略导入',
	'pages.policyImportDetail': '策略导入详情',
	'pages.policyExport': '策略导出',
	'pages.policyExportDetail': '策略导出详情',
	'pages.networkAccessPolicy': '网络准入策略',
	'pages.deviceAccessPolicy': '设备准入策略',
	'pages.baselineCheckDetail': '基线检测项配置详情',
	'pages.baselineCheck': '基线检测项配置',
	'pages.appCheck': '应用检测项配置',
	'pages.appCheckDetail': '应用检测项配置详情',
	'pages.processCheck': '进程检测项配置',
	'pages.processCheckDetail': '进程检测项配置详情',
	'pages.services': '服务',
	'pages.serviceDetail': '服务详情',
	'pages.serviceGroup': '服务组',
	'pages.serviceGroupDetail': '服务组详情',
	'pages.autoDiscoveredServices': '自动发现服务列表',
	'pages.sangforServiceImport': '深信服服务导入',
	'pages.serviceImport': '服务导入',
	'pages.applicationSettings': '应用设置',
	'pages.applicationServices': '应用服务',
	'pages.applicationGroup': '应用组',
	'pages.applicationGroupDetail': '应用组详情',
	'pages.networkServices': '网络服务',
	'pages.systemServices': '系统服务',
	'pages.systemServiceGroup': '系统服务组',
	'pages.systemServiceGroupDetail': '系统服务组详情',
	'pages.routingPool': '路由池',
	'pages.networkServiceGroup': '网络服务组',
	'pages.networkServiceGroupDetail': '网络服务组详情',
	'pages.routingTable': '路由表',
	'pages.logs': '日志',
	'pages.configLogs': '配置日志',
	'pages.networkLogs': '网络日志',
	'pages.dnsLogs': 'DNS日志',
	'pages.serviceLogs': '服务日志',
	'pages.logPushConfig': '日志推送配置',
	'pages.pushConfigDetail': '推送配置详情',
	'pages.userGuide': '用户引导',
	'pages.deviceNetworkingGuide': '设备组网引导',
	'pages.vpnReplacementGuide': '替换VPN引导',
	'pages.intranetPenetrationGuide': '内网穿透引导',
	'pages.selectNetwork': '选择网络',
	'pages.trialDescription': '体验说明',
	'pages.personalTrial': '个人体验',
	'pages.teamTrial': '团队体验',
	'pages.error': '出错了',
	'pages.download': '下载',
	'pages.applicationPanel': '应用面板',
	'pages.notFound': '找不到咯',
	'pages.notFoundDescription': '这里什么也没有~',

	// 路由相关
	'router.noPermission': '没有权限',
	'router.notLoggedIn': '您还没有登录，请先去登录',

	// 简单布局相关
	'menu.applicationPanel': '应用面板',
	'menu.adminConsole': '管理控制台',
	'tooltip.userManual': '查看用户手册',

	// 许可证相关
	'license.expired.title': '授权已过期',
	'license.expired.license': '授权',
	'license.expired.status': '已过期',
	'license.expired.clickToActivate': '点击此处激活',
	'license.expired.expireTime': '过期时间',
	'license.notActivated.title': '软件未激活',
	'license.notActivated.software': '软件',
	'license.notActivated.status': '未激活',
	'license.notActivated.clickToActivate': '点击此处激活',

	// 页脚相关
	'footer.basedOn': '基于',

	// 下载组件相关
	'download.authFailed': '认证失败，请稍后重试',
	'download.getPackageInfoFailed': '获取软件包信息失败，请稍后重试',
	'download.zeroTrustAccessUrl': '您的零信任接入地址为',
	'download.version': '版本号',
	'download.download': '下载',
	'download.chipVersion': '芯片版',
	'download.howToCheckChipType': '如何确定芯片类型',
	'download.macChipStep1': '1. 在左上角，点击',
	'download.macChipStep1Suffix': '-"关于本机"',
	'download.macChipStep2': '2. 在"概览"页的"芯片"或"处理器"中，查看是"Intel"还是"Apple"',
	'download.supportedVersions': '适用于 {version} 或更高版本',
	'download.scanQROrClick': '扫描二维码或点击下方链接下载',
	'download.installClient': '安装适用于{platform}的{appName}客户端',
	'download.requiresVersion': '需要 {version} 或更高版本',
	'download.authorizeAuth': '授权认证',
	'download.authAfterInstall': '安装APP后点击「授权认证」按钮进行认证',
	'download.authFailedTitle': '授权认证失败 ?',
	'download.troubleshootMethods': '排查方法：',
	'download.confirmAppInstalled': '请确认是否安装{appName}APP',
	'download.confirmSystemBrowser': '请确认是否用系统浏览器打开此页面',
	'download.viewAuthCode': '查看认证码',
	'download.comingSoon': '即将上线',
	'download.downloadWindowsClient': '下载Windows客户端',
	'download.oneClickInstall': '使用命令行一键安装',
	'download.manualInstall': '手动安装',
	'download.macChipStep1Full': '1. 在左上角，点击',
	'download.macChipStep1About': '-"关于本机"',
	'download.macChipStep2Full': '2. 在"概览"页的"芯片"或"处理器"中，查看是"Intel"还是"Apple"',
	'download.authAfterInstallFull': '安装APP后点击「授权认证」按钮进行认证',
	'download.authFailedBannerTitle': '授权认证失败 ?',
	'download.troubleshootMethodsFull': '排查方法：',
	'download.confirmAppInstalledFull': '请确认是否安装{appName}APP',
	'download.confirmSystemBrowserFull': '请确认是否用系统浏览器打开此页面',
	'download.macChipStep1Complete': '1. 在左上角，点击"',
	'download.macChipStep1End': '"-"关于本机"',
	'download.macChipStep2Complete': '2. 在"概览"页的"芯片"或"处理器"中，查看是"Intel"还是"Apple"',
	'download.windowsSystemStep1': '1. 选择【我的电脑】，右键点击【属性】',
	'download.windowsSystemStep2': '2. 在"设备规格"或"系统"项，查看系统类型是"64位操作系统"还是"32位操作系统"',
	'download.windowsSystemStep3': '3. 目前约95%的电脑都是64位操作系统，不确定系统类型且电脑相对较旧的用户可以选择下载【32位操作系统版本】',
	'download.howToCheckSystemType': '如何确定系统类型',
	'download.download64BitVersion': '下载64位操作系统版本',
	'download.download32BitVersion': '下载32位操作系统版本',
	'download.mostUsersChoose': '约95%的用户选择',

	'devices.editGroup.title': '编辑设备的设备组',
	'devices.editGroup.description': '设备{name}的设备组',
	'devices.editGroup.placeholder': '请选择设备组',
	'devices.editGroup.updateSuccess': '编辑设备的设备组成功',
	'devices.editGroup.updateFailed': '编辑设备的设备组失败, 请稍后重试',

	'devices.removeFromGroup.title': '从设备组删除设备',
	'devices.removeFromGroup.description': '确定把设备 {deviceName} 从设备组 {groupName} 中删除吗？',
	'devices.removeFromGroup.successTitle': '操作成功',
	'devices.removeFromGroup.successContent': '设备{deviceName}已从设备组{groupName}中删除',
	'devices.removeFromGroup.failedTitle': '操作失败',
	'devices.removeFromGroup.failedContent': '设备{deviceName}从设备组{groupName}中删除失败',

	'devices.editMesh.title': '修改设备{deviceName}的Mesh模式',
	'devices.editMesh.description': 'Mesh 模式是设备通过广播方式加入零信任组网，开启后设备可以路由到其它所有设备也可以被其它所有设备访问（访问控制策略允许的情况下）',
	'devices.editMesh.enableMesh': '启用Mesh模式',
	'devices.editMesh.warning': '关闭Mesh模式后设备有可能连不上零信任组网，请慎重操作，如有问题请联系管理员',
	'devices.editMesh.updateSuccess': '修改设备{deviceName}的Mesh模式成功',
	'devices.editMesh.updateFailed': '修改设备{deviceName}的Mesh模式失败，请稍后重试',

	'devices.editRelay.title': '修改设备{deviceName}的中继节点',
	'devices.editRelay.description': '设备中继节点',
	'devices.editRelay.placeholder': '请选择',
	'devices.editRelay.auto': '自动',
	'devices.editRelay.updateSuccess': '修改首选中继成功',
	'devices.editRelay.updateFailed': '修改首选中继失败，请稍后重试',

	// 路由管理
	'devices.addRoute.title': '添加设备{deviceName}的子网路由',
	'devices.addRoute.type': '类型',
	'devices.addRoute.advertisedRoute': '宣告路由',
	'devices.addRoute.excludeRoute': '排除路由',
	'devices.addRoute.subnetRoute': '子网路由，CIDR格式或IP段',
	'devices.addRoute.placeholder': '**********/24或者**********-************或**********-255',
	'devices.addRoute.remark': '备注',
	'devices.addRoute.enableImmediately': '立即开启',
	'devices.addRoute.confirm': '确定',
	'devices.addRoute.routeExists': '添加子网路由失败，子网路由{route}已经存在',
	'devices.addRoute.addSuccess': '添加子网路由成功',
	'devices.addRoute.addFailed': '添加子网路由失败，请稍后重试',

	'devices.editRoute.title': '编辑设备{deviceName}的子网路由',
	'devices.editRoute.advertisedRoutes': '宣告路由',
	'devices.editRoute.excludeRoutes': '排除路由',
	'devices.editRoute.addRoute': '添加路由',
	'devices.editRoute.batchEdit': '批量编辑',
	'devices.editRoute.search': '搜索',
	'devices.editRoute.searchPlaceholder': '搜索路由',
	'devices.editRoute.enabled': '已启用',
	'devices.editRoute.disabled': '已禁用',
	'devices.editRoute.enableSuccess': '启用路由成功',
	'devices.editRoute.enableFailed': '启用路由失败，请稍后重试',
	'devices.editRoute.disableSuccess': '禁用路由成功',
	'devices.editRoute.disableFailed': '禁用路由失败，请稍后重试',
	'devices.editRoute.deleteSuccess': '删除路由成功',
	'devices.editRoute.deleteFailed': '删除路由失败，请稍后重试',
	'devices.editRoute.updateSuccess': '更新路由成功',
	'devices.editRoute.updateFailed': '更新路由失败，请稍后重试',

	'devices.aclTag.title': '编辑设备{deviceName}的访问控制标签',
	'devices.aclTag.description': '根据需要给设备打标签，实施不同的访问控制策略',
	'devices.aclTag.noTags': '这台设备没有分配标签',
	'devices.aclTag.selectTags': '选择标签',
	'devices.aclTag.updateSuccess': '编辑访问控制标签成功',
	'devices.aclTag.updateFailed': '编辑访问控制标签失败，请稍后重试',
	'devices.aclTag.getPolicyFailed': '获取访问控制策略失败, 请稍后重试',

	'devices.logs.title': '日志',
	'devices.logs.getDeviceFailed': '获取设备失败，请稍后重试',

	'devices.addRouteNew.getServicesFailed': '获取网络服务失败，请稍后重试',

	// 数据导入详细组件
	'devices.dataImportDetail.title': '设备导入',
	'devices.dataImportDetail.import': '导入',
	'devices.dataImportDetail.templateDownload': '模板下载',
	'devices.dataImportDetail.uploadText': '点击上传文件或拖拽文件到这里',
	'devices.dataImportDetail.uploadSubText': '支持csv文件',
	'devices.dataImportDetail.uploadError': '请上传csv文件',
	'devices.dataImportDetail.importSuccess': '导入成功',
	'devices.dataImportDetail.importFailed': '导入失败',
	'devices.dataImportDetail.updatedDevices': '更新的设备',
	'devices.dataImportDetail.errorRows': '错误行',
	'devices.dataImportDetail.lineNumber': '行号',
	'devices.dataImportDetail.errorMessage': '错误信息',
	'devices.dataImportDetail.noErrorRows': '没有错误行',
	'devices.dataImportDetail.fields.id': 'ID',
	'devices.dataImportDetail.fields.deviceName': '设备名',
	'devices.dataImportDetail.fields.customDeviceName': '自定义设备名',
	'devices.dataImportDetail.fields.autoNaming': '自动命名',
	'devices.dataImportDetail.fields.description': '描述',
	'devices.dataImportDetail.fields.deviceGroup': '设备组',
	'devices.dataImportDetail.fields.disableKeyExpiry': '禁用密钥过期',
	'devices.dataImportDetail.fields.keyExpiryTime': '密钥过期时间',
	'devices.dataImportDetail.fields.deviceRoutes': '设备路由',
	'devices.dataImportDetail.fields.rejectRoutes': '拒绝接受路由',
	'devices.dataImportDetail.fields.relayNode': '中继节点',
	'devices.dataImportDetail.fields.meshMode': 'Mesh模式',

	// useTable 操作菜单
	'devices.menu.viewRecentLogs': '查看最近日志',
	'devices.menu.editRouteSettings': '编辑路由设置',
	'devices.menu.editRouteSettingsNew': '编辑路由设置(新)',
	'devices.menu.serviceReceptionRange': '服务接收范围设置',
	'devices.menu.setRejectRoutes': '设置拒绝接受路由',
	'devices.menu.editRelayNode': '编辑中继节点',
	'devices.menu.editMeshMode': '编辑Mesh模式',
	'devices.menu.editAccessControlTags': '编辑访问控制标签',
	'devices.menu.forceKeyExpiry': '密钥强制过期',
	'devices.menu.deleteDevice': '删除设备',
	'devices.menu.editDeviceGroup': '编辑设备组',
	'devices.menu.removeFromGroup': '从本组删除',
	'devices.menu.enableRemoteDesktop': '启用远程桌面',
	'devices.menu.disableRemoteDesktop': '禁用远程桌面',
	'devices.menu.remoteDesktopUserNotEnabled': '该设备所属用户远程桌面功能未开启，请在用户详情中启用',
	'devices.menu.remoteDesktopNotEnabled': '远程桌面功能未开启，请在设置/设备/远程桌面中启用',
	'devices.menu.renameDevice': '修改设备名称',
	'devices.menu.enableKeyExpiry': '启用密钥过期',
	'devices.menu.disableKeyExpiry': '禁用密钥过期',
	'devices.menu.viewConfigLogs': '查看配置日志',

	// 服务管理模块
	'services.table.serviceName': '服务名称',
	'services.table.serviceType': '服务类型',
	'services.table.protocolPort': '协议/端口',
	'services.table.serviceGroup': '所属服务组',
	'services.table.serviceNodes': '服务节点',
	'services.type.systemDaemon': '系统服务',
	'services.type.webApp': '网站应用',
	'services.type.remoteDesktop': '远程桌面',
	'services.routeMode.direct': '直连模式',
	'services.routeMode.forward': '路由模式',
	'services.filter.searchPlaceholder': '根据服务名称或描述搜索',
	'services.filter.query': '查询',
	'services.filter.serviceType': '服务类型',
	'services.filter.source': '来源',
	'services.filter.protocol': '协议',
	'services.filter.serviceGroup': '服务组',
	'services.filter.selectServiceGroup': '选择服务组',
	'services.filter.all': '全部',
	'services.filter.apply': '应用',
	'services.error.loadServicesFailed': '加载服务失败',

	// 服务组管理
	'services.group.table.groupName': '服务组名称',
	'services.group.table.serviceCount': '服务数',
	'services.group.table.createdTime': '创建时间',

	// 服务操作
	'services.delete.success': '删除服务成功',
	'services.delete.failed': '删除服务失败，请稍后重试',
	'services.delete.title': '删除服务',
	'services.delete.confirmText': '确定要删除这个服务吗？',
	'services.create.success': '创建服务成功',
	'services.create.failed': '创建服务失败',
	'services.edit.success': '编辑服务成功',
	'services.edit.failed': '编辑服务失败',

	// 服务创建指南
	'services.guide.connectorMismatch': '连接器不匹配',
	'services.guide.connectorMismatchContent': '子网节点IP地址不在连接器广播范围内, 不匹配的子网节点：',
	'services.guide.ipPortOccupied': '服务IP地址或端口已被其他服务占用',
	'services.guide.remoteDesktop.title': '远程桌面',
	'services.guide.remoteDesktop.content': '允许其他设备通过该服务连接到设备的远程桌面服务',
	'services.guide.systemService.title': '系统服务',
	'services.guide.systemService.content': '允许其他设备通过该服务连接到的系统服务',
	'services.guide.webApp.title': '网站应用',
	'services.guide.webApp.content': '允许其他设备通过该服务连接到的网站应用服务',

	// 设备服务表格
	'services.devices.table.service': '服务',
	'services.devices.table.port': '端口',
	'services.devices.table.protocol': '协议',
	'services.devices.table.device': '设备',
	'services.devices.table.user': '用户',
	'services.devices.table.os': '操作系统',
	'services.devices.table.lastOnline': '上次在线时间',
	'services.devices.status.online': '在线',
	'services.devices.error.loadDevicesFailed': '获取设备列表失败, 请稍后重试',

	// 应用程序表格
	'services.application.table.name': '名称',
	'services.application.table.category': '分类',
	'services.application.table.icon': '图标',
	'services.application.table.urlMasquerade': '启用地址伪装',
	'services.application.export.name': '名称',
	'services.application.export.description': '描述',
	'services.application.export.category': '资源分类',
	'services.application.export.iconType': '图标类型(0: URL 1: Name)',
	'services.application.export.icon': '图标',
	'services.application.export.url': 'URL',
	'services.application.export.urlMasquerade': '启用地址伪装',
	'services.application.delete.success': '删除应用成功',
	'services.application.delete.failed': '删除应用失败，请稍后重试',

	// 应用程序编辑
	'services.application.edit.error.loadCategoryFailed': '获取分类失败',
	'services.application.edit.error.loadFailed': '获取失败',
	'services.application.edit.success.updateSuccess': '更新成功',
	'services.application.edit.error.updateFailed': '更新失败',
	'services.application.edit.form.name': '名称',
	'services.application.edit.form.nameRequired': '请输入名称',
	'services.application.edit.form.namePlaceholder': '请输入名称',
	'services.application.edit.form.category': '分类',
	'services.application.edit.form.categoryRequired': '请选择分类',
	'services.application.edit.form.categoryPlaceholder': '请选择分类',
	'services.application.edit.form.inputCategory': '输入分类',
	'services.application.edit.error.createCategoryFailed': '创建分类失败',
	'services.application.edit.form.icon': '图标',
	'services.application.edit.form.iconRequired': '请选择图标类型',
	'services.application.edit.form.iconPlaceholder': '请选择图标类型',
	'services.application.edit.form.iconName': '图标',
	'services.application.edit.form.iconUpload': '上传',
	'services.application.edit.error.fileFormat': '只允许上传 JPG/PNG 格式的文件!',
	'services.application.edit.error.fileSize': '上传的文件必须小于 2MB!',
	'services.application.edit.error.uploadFailed': '上传失败',
	'services.application.edit.error.selectIcon': '请选择图标',
	'services.application.edit.form.url': 'URL',
	'services.application.edit.form.urlRequired': '请输入URL',
	'services.application.edit.form.urlPlaceholder': '请输入URL',
	'services.application.edit.form.urlMasquerade': '启用地址伪装',

	// 应用程序新建
	'services.application.new.success.createSuccess': '创建成功',
	'services.application.new.error.createFailed': '创建失败',

	// 应用程序导入
	'services.application.import.success.importSuccess': '导入成功',
	'services.application.import.error.importFailed': '导入失败',
	'services.application.import.error.checkFileFormat': '请检查文件格式是否正确',
	'services.application.import.table.resourceDescription': '资源描述',
	'services.application.import.table.resourceCategory': '资源分类',
	'services.application.import.table.iconType': '图标类型',
	'services.application.import.table.iconTypeUrl': 'URL',
	'services.application.import.table.iconTypeName': '名称',
	'services.application.import.table.isValid': '是否有效',
	'services.application.import.table.yes': '是',

	// 服务组件
	'services.components.type.remoteDesktop': '远程桌面',
	'services.components.type.systemService': '系统服务',
	'services.components.type.all': '全部',
	'services.components.lastSeen.apply': '应用',

	// 应用程序分组排序
	'services.application.groupSort.error.loadCategoryFailed': '获取分类失败',
	'services.application.groupSort.success.saveSuccess': '保存成功',
	'services.application.groupSort.error.saveFailed': '保存失败',
	'services.application.groupSort.error.nameRequired': '名称不能为空',
	'services.application.groupSort.success.modifySuccess': '修改成功',
	'services.application.groupSort.error.modifyFailed': '修改失败',
	'services.application.groupSort.success.deleteSuccess': '删除成功',
	'services.application.groupSort.error.deleteFailed': '删除失败',
	'services.application.groupSort.form.namePlaceholder': '请输入名称',
	'services.application.groupSort.success.createSuccess': '创建成功',
	'services.application.groupSort.error.createCategoryFailed': '创建分类失败',
	'services.application.groupSort.button.confirm': '确定',

	// 从服务组中移除服务
	'services.removeFromGroup.success.operationSuccess': '操作成功',
	'services.removeFromGroup.success.serviceRemovedFromGroup': '服务{serviceName}已从服务组{serviceGroupName}中删除',
	'services.removeFromGroup.error.operationFailed': '操作失败',
	'services.removeFromGroup.error.serviceRemoveFromGroupFailed': '服务{serviceName}从服务组{serviceGroupName}中删除失败',

	// 服务导入
	'services.import.breadcrumb.services': '服务',
	'services.import.breadcrumb.dataImport': '数据导入',
	'services.import.title.dataImport': '数据导入',
	'services.import.description': '将已有的数据导入到本系统中',
	'services.import.sangforVpn': '深信服VPN',

	// 服务组页面
	'services.group.filter.queryPlaceholder': '根据服务组名称、编码、备注查询',
	'services.group.filter.query': '查询',
	'services.group.breadcrumb.allServices': '所有服务',
	'services.group.breadcrumb.serviceGroups': '服务组',

	// 设备服务页面
	'services.devices.status.all': '全部',
	'services.devices.status.offline': '离线',
	'services.devices.breadcrumb.autoDiscoveredServices': '自动发现服务',
	'services.devices.filter.searchPlaceholder': '根据用户、设备名称、IP、版本号等搜索',
	'services.devices.filter.status': '状态',

	// 应用程序主页面
	'services.application.filter.searchPlaceholder': '根据应用名称，描述，URL搜索',
	'services.application.breadcrumb.application': '应用',
	'services.application.index.title.applicationPanel': '应用面板',
	'services.application.index.description.applicationPanel': '查看和管理应用面板',
	'services.application.index.title.application': '应用',
	'services.application.index.description.application': '查看和管理应用',
	'services.application.index.button.dataImport': '数据导入',
	'services.application.index.button.dataExport': '数据导出',
	'services.application.index.button.categoryManagement': '分类管理',
	'services.application.index.button.newApplication': '新建应用',
	'services.application.index.filter.category': '分类',
	'services.application.index.stats.totalApplications': '应用总数',
	'services.application.index.endMessage': '---- 到底了 ----',
	'services.application.table.yes': '是',
	'services.application.table.no': '否',
	'services.application.dropdown.editApplication': '编辑应用',
	'services.application.dropdown.deleteApplication': '删除应用',
	'services.application.export.filename': '应用列表.csv',
	'services.application.import.templateFilename': '应用导入模板.csv',
	'services.application.import.invalidDataFilename': '无效数据.csv',
	'services.application.import.title': '应用导入',
	'services.application.import.button.import': '导入',
	'services.application.import.upload.dragMainText': '点击上传文件或拖拽文件到这里',
	'services.application.import.upload.dragSubText': '支持csv文件',
	'services.application.import.button.templateDownload': '模板下载',
	'services.application.import.button.invalidDataDownload': '无效数据下载',
	'services.application.import.stats.totalData': '数据总数',
	'services.application.import.stats.validData': '有效条数',
	'services.application.import.stats.invalidData': '无效条数',
	'services.application.import.warning': '只会导入有效的应用，如应用已存在，则会覆盖旧数据',

	// 日志模块
	'logs.index.button.logPushConfig': '日志推送配置',
	'logs.index.tab.configLog': '配置日志',
	'logs.index.tab.networkLog': '网络日志',
	'logs.index.tab.dnsLog': 'DNS日志',
	'logs.index.tab.servicesLog': '服务日志',
	'logs.index.notification.enableNetworkLogSuccess': '启用网络日志成功',
	'logs.index.notification.enableNetworkLogFailed': '启用网络日志失败，请稍后重试',
	'logs.index.notification.disableNetworkLogSuccess': '禁用网络日志成功',
	'logs.index.notification.disableNetworkLogFailed': '禁用网络日志失败，请稍后重试',
	'logs.index.notification.enableDnsLogSuccess': '启用DNS日志成功',
	'logs.index.notification.enableDnsLogFailed': '启用DNS日志失败，请稍后重试',
	'logs.index.notification.disableDnsLogSuccess': '禁用DNS日志成功',
	'logs.index.notification.disableDnsLogFailed': '禁用DNS日志失败，请稍后重试',

	// 日志表格
	'logs.table.column.time': '时间',
	'logs.table.column.action': '操作',
	'logs.table.column.operator': '操作者',
	'logs.table.column.target': '目标',
	'logs.table.error.loadLogsFailed': '获取日志列表失败, 请稍后重试',
	'logs.table.error.loadEventsFailed': '获取日志操作列表失败, 请稍后重试',

	// DNS日志
	'logs.dnsLog.modal.enable': '启用',
	'logs.dnsLog.modal.disable': '禁用',
	'logs.dnsLog.modal.title': 'DNS日志',
	'logs.dnsLog.modal.content': '确定要{action}DNS日志吗？',
	'logs.dnsLog.modal.okText': '确定',
	'logs.dnsLog.modal.cancelText': '取消',
	'logs.dnsLog.table.column.user': '用户',
	'logs.dnsLog.table.column.device': '设备',
	'logs.dnsLog.table.column.domain': '域名',
	'logs.dnsLog.table.column.type': '类型',
	'logs.dnsLog.table.column.result': '解析结果',

	// 网络流量日志
	'logs.networkFlowLog.modal.enable': '启用',
	'logs.networkFlowLog.modal.disable': '禁用',
	'logs.networkFlowLog.modal.title': '网络日志',
	'logs.networkFlowLog.modal.content': '确定要{action}网络日志吗？',
	'logs.networkFlowLog.modal.okText': '确定',
	'logs.networkFlowLog.modal.cancelText': '取消',
	'logs.networkFlowLog.table.column.user': '用户',
	'logs.networkFlowLog.table.column.device': '设备',
	'logs.networkFlowLog.table.column.summary': '摘要',
	'logs.networkFlowLog.table.column.source': '源',
	'logs.networkFlowLog.table.column.target': '目标',
	'logs.networkFlowLog.table.error.loadTrafficFailed': '获取流量失败, 请稍后重试',
	'logs.networkFlowLog.table.error.loadLogTypesFailed': '获取日志操作类型失败, 请稍后重试',
	'logs.networkFlowLog.description.networkDevice': '网络设备',
	'logs.networkFlowLog.description.recordTime': '记录时间',
	'logs.networkFlowLog.description.timeWindow': '时间窗口',
	'logs.networkFlowLog.link.viewDevice': '查看设备',

	// 服务日志
	'logs.servicesLog.table.column.user': '用户',
	'logs.servicesLog.table.column.device': '设备',
	'logs.servicesLog.table.column.service': '服务',

	// 日志推送配置
	'logs.pushConfig.breadcrumb.logs': '日志',
	'logs.pushConfig.breadcrumb.pushConfig': '推送配置',
	'logs.pushConfig.filter.placeholder': '根据名称或描述查询',
	'logs.pushConfig.filter.label': '查询',

	// 日志推送配置表格
	'logs.pushConfig.table.column.name': '名称',
	'logs.pushConfig.table.column.status': '状态',
	'logs.pushConfig.table.column.statistics': '统计',
	'logs.pushConfig.table.status.enabled': '启用',
	'logs.pushConfig.table.status.disabled': '禁用',

	// 日志推送配置删除
	'logs.pushConfig.del.success': '删除日志推送配置成功',
	'logs.pushConfig.del.failed': '删除日志推送配置失败，请稍后重试',

	// 日志推送配置新建
	'logs.pushConfig.new.success': '新建成功',
	'logs.pushConfig.new.failed': '新建失败',
	'logs.pushConfig.new.form.name': '名称',
	'logs.pushConfig.new.form.nameRequired': '名称不能为空',

	// 日志推送配置编辑
	'logs.pushConfig.edit.success': '更新成功',
	'logs.pushConfig.edit.failed': '更新失败',
	'logs.pushConfig.edit.form.name': '名称',
	'logs.pushConfig.edit.form.nameRequired': '名称不能为空',

	// 日志推送配置更多文本
	'logs.pushConfig.index.title': '日志推送配置',
	'logs.pushConfig.index.button.newConfig': '新建配置',
	'logs.pushConfig.table.dropdown.editConfig': '编辑配置',
	'logs.pushConfig.table.dropdown.deleteConfig': '删除配置',

	// 日志推送配置编辑更多文本
	'logs.pushConfig.edit.title': '编辑推送配置',
	'logs.pushConfig.edit.form.code': '编码',
	'logs.pushConfig.edit.form.codeTooltip': '新建完成后不可修改',
	'logs.pushConfig.edit.form.status': '状态',
	'logs.pushConfig.edit.form.statusEnabled': '启用',
	'logs.pushConfig.edit.form.statusDisabled': '禁用',
	'logs.pushConfig.edit.form.description': '备注',
	'logs.pushConfig.edit.section.pushDestination': '推送目的',
	'logs.pushConfig.edit.link.pushTargetConfig': '推送目标配置',
	'logs.pushConfig.edit.button.add': '添加',
	'logs.pushConfig.edit.confirm.deleteTitle': '确认是否删除该项目',
	'logs.pushConfig.edit.confirm.deleteContent': '此操作将不可逆',
	'logs.pushConfig.edit.section.pushEvents': '推送事件',
	'logs.pushConfig.edit.link.pushEventConfig': '推送事件配置',

	// 日志推送配置新建更多文本
	'logs.pushConfig.new.title': '新建推送配置',
	'logs.pushConfig.new.form.codeRequired': '编码不能为空',
	'logs.pushConfig.new.form.codeStartError': '编码不能以-开头',
	'logs.pushConfig.new.form.codeFormatError': '编码只能包含字母、数字和\'-\'',

	// 日志推送配置详情更多文本
	'logs.pushConfig.detail.section.statistics': '统计数据',
	'logs.pushConfig.detail.section.pushDestination': '推送目的',
	'logs.pushConfig.detail.section.pushEvents': '推送事件',
	'logs.pushConfig.detail.dataTransform': '数据转换配置',

	// 日志推送配置删除更多文本
	'logs.pushConfig.del.title': '删除日志推送配置',
	'logs.pushConfig.del.content': '日志推送配置将被删除，删除后将无法恢复，确认删除吗？',
	'logs.pushConfig.del.confirmText': '输入 {name} 以确认删除',

	// 推送元数据
	'logs.pushConfig.pushMeta.column.event': '事件',
	'logs.pushConfig.pushMeta.column.target': '目标',
	'logs.pushConfig.pushMeta.column.time': '时间',
	'logs.pushConfig.pushMeta.column.successCount': '成功次数',
	'logs.pushConfig.pushMeta.column.failureCount': '失败次数',
	'logs.pushConfig.pushMeta.range.all': '全部',
	'logs.pushConfig.pushMeta.range.today': '今日',

	// 数据转换管理
	'logs.pushConfig.transformManage.title': '数据转换',
	'logs.pushConfig.transformManage.noData': '暂无数据',

	// 表单验证消息
	'logs.pushConfig.validation.required': '{field}不能为空',
	'logs.pushConfig.validation.emailFormat': '{field}格式不正确, 请填写正确的邮箱地址',
	'logs.pushConfig.validation.urlFormat': '{field}格式不正确, 请填写正确的URL地址',
	'logs.pushConfig.validation.phoneFormat': '{field}格式不正确, 请填写正确的手机号码',
	'logs.pushConfig.validation.ipFormat': '{field}格式不正确, 请填写正确的IP地址',
	'logs.pushConfig.validation.idCardFormat': '{field}格式不正确, 请填写正确的身份证号码',
	'logs.pushConfig.validation.numberFormat': '{field}格式不正确, 请填写正确的数字',
	'logs.pushConfig.validation.integerFormat': '{field}格式不正确, 请填写正确的整数',
	'logs.pushConfig.validation.decimalFormat': '{field}格式不正确, 请填写正确的小数',
	'logs.pushConfig.validation.jsonFormat': '{field}格式不正确, 请填写正确的JSON格式',
	'logs.pushConfig.validation.arrayFormat': '{field}格式不正确, 请填写正确的数组格式',
	'logs.pushConfig.validation.invalidFormat': '{field}格式不正确',
	'logs.pushConfig.validation.minLength': '{field}长度不能小于{minLength}',
	'logs.pushConfig.validation.maxLength': '{field}长度不能大于{maxLength}',

	// 用户管理模块
	'users.breadcrumb.users': '所有用户',
	'users.breadcrumb.userGroups': '用户组',
	'users.breadcrumb.dataImport': '数据导入',
	'users.breadcrumb.dataExport': '数据导出',

	// 用户列表页面
	'users.index.title': '用户',
	'users.index.description': '管理用户账户、权限和访问控制',
	'users.index.button.addUser': '添加用户',
	'users.index.button.batchOperation': '批量操作',
	'users.index.button.dataImport': '数据导入',
	'users.index.button.dataExport': '数据导出',
	'users.index.filter.searchPlaceholder': '根据用户名称或显示名搜索',
	'users.index.filter.userGroup': '用户组',
	'users.index.filter.userGroupPlaceholder': '请选择用户组',
	'users.index.filter.userType': '用户类型',
	'users.index.filter.userTypePlaceholder': '请选择类型',
	'users.type.temporary': '临时用户',
	'users.type.regular': '普通用户',
	'users.table.user': '用户',
	'users.table.role': '角色',
	'users.table.userGroups': '所属用户组',
	'users.table.joinTime': '加入时间',
	'users.table.lastSeen': '上次在线时间',
	'users.table.status': '状态',
	'users.action.removeFromFilter': '从搜索条件中移除',
	'users.action.addToFilter': '添加到搜索条件',
	'users.action.editRole': '编辑用户角色',
	'users.action.viewDevices': '查看用户设备',
	'users.action.viewLogs': '查看最近日志',
	'users.action.editUser': '编辑用户信息',
	'users.action.resetPassword': '重置密码',
	'users.action.resetPasswordConfirm': '重置密码将会为用户生成新的密码',
	'users.action.resetPasswordSuccess': '重置密码成功',
	'users.action.resetPasswordFailed': '重置密码失败，请稍后再试',
	'users.action.unlockAccount': '账号解锁',
	'users.action.editUserGroups': '编辑用户组',
	'users.action.enableUser': '启用用户',
	'users.action.disableUser': '禁用用户',
	'users.action.deleteUser': '删除用户',
	'users.action.setExpiration': '设置有效期',
	'users.action.removeMFADevice': '解除MFA设备',
	'users.error.fetchUserListFailed': '获取用户列表失败, 请稍后重试',
	'users.removeMfaDevice.confirmText': '输入 {loginName} 确认解除MFA设备',
	'users.dataExport.filter.searchPlaceholder': '用户名称或显示名',
	'users.dataExport.filter.search': '查询',
	'users.dataExport.filter.statusPlaceholder': '请选择状态',
	'users.dataExport.filter.status': '状态',
	'users.local.allUsers': '所有用户',
	'users.local.totalUsers': '用户总数 {count}',
	'users.local.selectedUsers': '已选中 {count} 个用户',
	'users.local.notJoinedUsers': '未加入的用户数：{count}',
	'users.local.batchAddToGroup': '批量添加到组',
	'users.local.batchDelete': '批量删除',
	'users.local.batchDisable': '批量禁用',
	'users.local.batchEnable': '批量启用',
	'users.local.batchEditRole': '批量编辑角色',
	'users.local.batchSetExpiration': '批量设置有效期',
	'users.local.endOfList': '---- 到底了 ----',

	// 数据导入相关
	'users.dataImport.import': '导入',
	'users.dataImport.newUsers': '需要新建的用户',
	'users.dataImport.updateUsers': '更新的用户',
	'users.dataImport.errorRows': '错误行',
	'users.dataImport.rowNumber': '行号',
	'users.dataImport.errorMessage': '错误信息',
	'users.dataImport.noErrors': '没有错误行',
	'users.dataImport.uploadCsvOnly': '请上传csv文件',
	'users.dataImport.dragMainText': '点击上传文件或拖拽文件到这里',
	'users.dataImport.dragSubText': '支持csv文件',
	'users.dataImport.downloadTemplate': '模板下载',

	// 用户详情相关
	'users.detail.device': '设备',
	'users.detail.version': '版本',
	'users.detail.lastOnlineTime': '上次在线时间',
	'users.detail.updateAvailable': '有可更新的版本',
	'users.detail.updateMessage': '最新版本 1.40.0 已经发布. 快去阅读发行注记看看有什么新功能吧！',
	'users.detail.fetchDeviceListFailed': '获取设备列表失败, 请稍后重试',
	'users.detail.deviceListTitle': '用户拥有的设备列表',

	// 模板编辑器相关
	'users.add.templateEditor.invalidJson': '表达式模板不是合法的 JSON',
	'users.add.templateEditor.saveSuccess': '保存成功',
	'users.add.templateEditor.title': '编辑用户模板',
	'users.add.templateEditor.save': '保存',
	'users.add.templateEditor.cancel': '取消',

	// 用户组添加相关
	'users.groupAdd.name': '名称',
	'users.groupAdd.nameRequired': '名称不能为空',
	'users.groupAdd.code': '编码',
	'users.groupAdd.codeHint': '新建完成后不可修改',
	'users.groupAdd.codeRequired': '编码不能为空',
	'users.groupAdd.codeNoStartDash': '编码不能以-开头',
	'users.groupAdd.codeFormat': '编码只能包含字母、数字和\'-\'',
	'users.groupAdd.input': '输入',

	// 用户编辑相关
	'users.editUser.fetchNetworkInfoFailed': '获取网络信息失败',
	'users.editUser.loginNameRequired': '账号不能为空',
	'users.editUser.displayNameRequired': '显示名称不能为空',
	'users.editUser.loginName': '账号',
	'users.editUser.displayName': '显示名称',
	'users.editUser.accountExpiry': '账号有效期',
	'users.editUser.expiryTime': '到期时间',
	'users.editUser.forceMFA': '强制启用MFA',
	'users.editUser.role': '角色',
	'users.editUser.admin': '管理员',
	'users.editUser.adminDescription': '可以查看管理控制台并管理、设备和用户网络设置。',
	'users.editUser.user': '普通用户',
	'users.editUser.userDescription': '只能使用设备和网络，不能使用管理控制台。',
	'users.editUser.enableRDP': '启用远程桌面',
	'users.editUser.autoGrant': '自动授权',
	'users.editUser.autoGrantDescription': '启用远程桌面后，该用户的新设备将自动启用远程桌面',

	// 域名管理相关
	'domainManager.title': '自定义域名解析',
	'domainManager.description': '设置自定义域名解析',
	'domainManager.noDomainConfigured': '您还没有设置自定义域名解析',
	'domainManager.addCustomDomain': '添加自定义域名解析',
	'domainManager.aRecord': 'A记录',
	'domainManager.domain': '域名',
	'domainManager.enterDomain': '请输入域名',
	'domainManager.validation.ipRequired': '请输入IP地址',
	'domainManager.validation.cannotUseDefaultIP': '不能使用默认IP地址***************',
	'domainManager.validation.invalidIP': '请输入正确的IP地址',
	'domainManager.validation.domainRequired': '请输入域名',
	'domainManager.validation.invalidDomainFormat': '域名应包含字母数字小写字符、连字符和句点',
	'domainManager.validation.domainExists': '域名已存在',
	'domainManager.validation.cnameRequired': '请输入CNAME',
	'domainManager.validation.cnameFormat': 'CNAME应包含字母数字小写字符、连字符和句点',
	'domainManager.validation.recordExists': '记录已存在',
	'domainManager.validation.circularReference': '不能循环引用',
	'domainManager.name': '名称',
	'domainManager.type': '类型',
	'domainManager.value': '值',
	'domainManager.example': '示例',
	'domainManager.ipv4Address': 'IPv4地址',
	'domainManager.cnameTarget': '目标，例如：www.example.com',
	'domainManager.editCustomDomain': '编辑自定义域名解析',
	'domainManager.nameFormatDescription': '名称支持的格式（优先级从上往下依次递增）',
	'domainManager.nameFormat.wildcard': '全通配',
	'domainManager.nameFormat.wildcardDesc': '匹配任意',
	'domainManager.nameFormat.suffixMatch': '后缀匹配',
	'domainManager.nameFormat.suffixMatchDesc': '匹配以 abc 结尾的主机名',
	'domainManager.nameFormat.prefixMatch': '前缀匹配',
	'domainManager.nameFormat.prefixMatchDesc': '匹配以 abc 开头的主机名',
	'domainManager.nameFormat.exactMatch': '精确匹配',
	'domainManager.nameFormat.exactMatchDesc': '仅匹配 abc',
	'domainManager.nameFormat.wildcardSubdomain': '通配子域',
	'domainManager.nameFormat.wildcardSubdomainDesc': '匹配任意前缀如 *.abc',
	'domainManager.nameFormat.suffixSubdomain': '后缀子域',
	'domainManager.nameFormat.suffixSubdomainDesc': '匹配 xabc.d 类似',
	'domainManager.nameFormat.prefixSubdomain': '前缀子域',
	'domainManager.nameFormat.prefixSubdomainDesc': '匹配 abcx.d 类似',
	'domainManager.nameFormat.exactDomain': '精确域名',
	'domainManager.nameFormat.exactDomainDesc': '完整匹配',

	// 用户删除相关
	'users.del.confirmMessage': '删除 {displayName}({loginName}) 会删除他的所有设备。 删除宣告了子网路由或作为出口节点的设备可能会影响网络的其他用户使用。',
	'users.del.confirmText': '输入 {loginName} 确认删除',
	'users.del.confirmMessagePrefix': '删除',
	'users.del.confirmMessageSuffix': '会删除他的所有设备。 删除宣告了子网路由或作为出口节点的设备可能会影响网络的其他用户使用。',
	'users.del.confirmTextPrefix': '输入',
	'users.del.confirmTextSuffix': '确认删除',

	// 用户组添加/编辑扩展
	'users.groupAdd.description': '备注',
	'users.groupAdd.type': '类型',
	'users.groupAdd.staticGroup': '静态用户组',
	'users.groupAdd.dynamicGroup': '动态用户组',
	'users.groupAdd.builtinUsers': '内置用户',
	'users.groupAdd.expressionRequired': '表达式不能为空',
	'users.groupAdd.triggerParamError': '触发参数错误',
	'users.groupAdd.operatorHelp': '运算符支持 and(与)、or (或)、or (或)、not (非)、()(括号)，例如：1 and 2，1 or 2, not 1,  1 or (not 1)',
	'users.groupAdd.paramCombination': '参数组合',
	'users.groupAdd.dynamicGroupEmptyHint': '动态用户组属性为空, 请前往',
	'users.groupAdd.settings': '设置',
	'users.groupAdd.expertMode': '专家模式',
	'users.groupAdd.otherUsers': '其他用户',
	'users.groupAdd.otherUsersHint': '内置用户以外的其他用户，请直接输入，以逗号分隔。',

	// MFA编辑相关
	'users.editMfa.description': '编辑用户 {displayName}({loginName}) 的MFA设置',
	'users.editMfa.forceMfaEnabled': '强制启用MFA',

	// 远程桌面编辑相关
	'users.editRdp.description': '编辑用户 {displayName}({loginName}) 的远程桌面设置',
	'users.editRdp.enableRdp': '启用远程桌面',
	'users.editRdp.autoGrant': '自动授权',
	'users.editRdp.autoGrantHint': '启用远程桌面后，该用户的新设备将自动启用远程桌面',

	// 用户暂停/恢复相关
	'users.suspend.modalTitle': '{action}用户{displayName}({loginName})',
	'users.suspend.modalDescription': '{action} {displayName} ({loginName}) 将 {operation} 他拥有的所有设备的接入权限。',
	'users.suspend.restore': '恢复',
	'users.suspend.suspend': '暂停',

	// 用户解锁相关
	'users.unlock.notLocked': '该账号未被锁定',
	'users.unlock.passwordLocked': '该账号密码输入错误次数过多被锁定',
	'users.unlock.mfaLocked': '该账号MFA验证码输入错误次数过多被锁定',
	'users.unlock.device': '设备',
	'users.unlock.unlockTime': '解锁时间',
	'users.unlock.unlockAll': '全部解锁',
	'users.unlock.unlockDevice': '解锁设备',

	// 数据导出扩展相关
	'users.dataExport.filter.all': '全部',
	'users.dataExport.filter.enabled': '启用',
	'users.dataExport.filter.disabled': '禁用',
	'users.dataExport.filter.groupPlaceholder': '请选择用户组',
	'users.dataExport.filter.group': '用户组',
	'users.dataExport.filter.typePlaceholder': '请选择类型',
	'users.dataExport.filter.type': '用户类型',
	'users.dataExport.filter.temporaryUser': '临时用户',
	'users.dataExport.filter.normalUser': '普通用户',
	'users.dataExport.noPermission': '没有权限',
	'users.dataExport.modal.title': '用户导出',
	'users.dataExport.modal.export': '导出',
	'users.dataExport.fetchListFailed': '获取用户列表失败, 请稍后重试',
	'users.dataExport.deleteSuccess': '删除导出记录成功',
	'users.dataExport.deleteFailed': '删除导出记录失败，请稍后重试',
	'users.dataExport.deleteTitle': '删除导出记录{fileName}',
	'users.dataExport.deleteConfirm': '输入 {fileName} 确认删除',
	'users.dataExport.detail.title': '导出详情',

	// 数据导入扩展相关
	'users.dataImport.table.operator': '操作人',
	'users.dataImport.table.unknown': '未知',
	'users.dataImport.table.importTime': '导入时间',
	'users.dataImport.fetchListFailed': '获取用户列表失败, 请稍后重试',
	'users.dataImport.deleteSuccess': '删除导入记录成功',
	'users.dataImport.deleteFailed': '删除导入记录失败，请稍后重试',
	'users.dataImport.deleteTitle': '删除导入记录{fileName}',
	'users.dataImport.deleteConfirm': '输入 {fileName} 确认删除',
	'users.dataImport.detail.title': '导入详情',
	'users.dataImport.detail.recordDetail': '导入记录详情',
	'users.dataImport.detail.fileName': '文件名',
	'users.dataImport.detail.fileUrl': '文件地址',
	'users.dataImport.detail.totalLines': '数据行数',
	'users.dataImport.detail.errorLines': '错误行数',
	'users.dataImport.detail.createLines': '创建行数',
	'users.dataImport.detail.updateLines': '更新行数',
	'users.dataImport.detail.isFinished': '是否完成',
	'users.dataImport.detail.errorRows': '错误行',
	'users.dataImport.detail.errorRowsDescription': '用户导入的错误行',
	'users.dataImport.detail.lineNumber': '行号',
	'users.dataImport.detail.errorMessage': '错误信息',
	'users.dataImport.detail.importDetails': '导入详情',
	'users.dataImport.detail.importDetailsDescription': '用户导入每条数据的详情',
	'users.dataImport.detail.isSuccess': '是否成功',
	'users.dataImport.detail.message': '信息',

	// 直接添加用户相关
	'users.addDirect.loginNameRequired': '账号不能为空',
	'users.addDirect.loginNameFormat': '账号格式不正确',
	'users.addDirect.passwordRequired': '密码不能为空',
	'users.addDirect.passwordLength': '密码长度不能小于8位',
	'users.addDirect.passwordStrength': '密码必须包含数字，字符和特殊符号',
	'users.addDirect.displayNameRequired': '显示名称不能为空',
	'users.addDirect.loginName': '账号',
	'users.addDirect.displayName': '显示名称',
	'users.addDirect.password': '密码',
	'users.addDirect.passwordStrengthLabel': '密码强度',
	'users.addDirect.generatePassword': '生成',
	'users.addDirect.accountExpiry': '账号有效期',
	'users.addDirect.expiryTime': '到期时间',
	'users.addDirect.forceMFA': '强制启用MFA',
	'users.addDirect.userGroups': '用户组',
	'users.addDirect.selectUserGroups': '请选择用户组',
	'users.addDirect.role': '角色',
	'users.addDirect.admin': '管理员',
	'users.addDirect.adminDescription': '可以查看管理控制台并管理、设备和用户网络设置。',
	'users.addDirect.user': '普通用户',
	'users.addDirect.userDescription': '只能使用设备和网络，不能使用管理控制台。',
	'users.index.filter.search': '搜索用户',
	'users.index.filter.group': '用户组',
	'users.index.filter.role': '角色',
	'users.index.filter.status': '状态',
	'users.index.filter.all': '全部',
	'users.index.filter.active': '活跃',
	'users.index.filter.suspended': '已暂停',
	'users.index.filter.expired': '已过期',

	// 用户表格
	'users.table.column.name': '姓名',
	'users.table.column.email': '邮箱',
	'users.table.column.role': '角色',
	'users.table.column.group': '用户组',
	'users.table.column.status': '状态',
	'users.table.column.lastLogin': '最后登录',
	'users.table.column.expiresAt': '过期时间',
	'users.table.column.actions': '操作',
	'users.table.status.active': '活跃',
	'users.table.status.suspended': '已暂停',
	'users.table.status.expired': '已过期',
	'users.table.status.locked': '已锁定',
	'users.table.dropdown.viewDetails': '查看详情',
	'users.table.dropdown.editUser': '编辑用户',
	'users.table.dropdown.editRole': '编辑角色',
	'users.table.dropdown.editGroup': '编辑用户组',
	'users.table.dropdown.resetPassword': '重置密码',
	'users.table.dropdown.editMFA': '编辑MFA',
	'users.table.dropdown.editRDP': '编辑RDP',
	'users.table.dropdown.editExpiresAt': '编辑过期时间',
	'users.table.dropdown.suspend': '暂停用户',
	'users.table.dropdown.unlock': '解锁用户',
	'users.table.dropdown.deleteUser': '删除用户',

	// 批量操作
	'users.batch.title': '批量操作',
	'users.batch.selected': '已选择 {count} 个用户',
	'users.batch.addToGroup': '添加到用户组',
	'users.batch.removeFromGroup': '从用户组移除',
	'users.batch.editRole': '编辑角色',
	'users.batch.editMFA': '编辑MFA',
	'users.batch.editRDP': '编辑RDP',
	'users.batch.editExpiresAt': '编辑过期时间',
	'users.batch.suspend': '暂停用户',
	'users.batch.delete': '删除用户',

	// 用户添加
	'users.add.title': '添加用户',
	'users.add.form.name': '姓名',
	'users.add.form.email': '邮箱',
	'users.add.form.role': '角色',
	'users.add.form.group': '用户组',
	'users.add.form.expiresAt': '过期时间',
	'users.add.button.save': '保存',
	'users.add.button.cancel': '取消',
	'users.add.success': '用户添加成功',
	'users.add.failed': '用户添加失败',

	// 用户编辑
	'users.edit.title': '编辑用户',
	'users.edit.success': '用户更新成功',
	'users.edit.failed': '用户更新失败',

	// 用户删除
	'users.delete.title': '删除用户',
	'users.delete.content': '确定要删除用户 {name} 吗？此操作不可撤销。',
	'users.delete.success': '用户删除成功',
	'users.delete.failed': '用户删除失败',

	// 用户暂停
	'users.suspend.title': '暂停用户',
	'users.suspend.content': '确定要暂停用户 {name} 吗？',
	'users.suspend.success': '用户暂停成功',
	'users.suspend.failed': '用户暂停失败',

	// 用户解锁
	'users.unlock.title': '解锁用户',
	'users.unlock.content': '确定要解锁用户 {name} 吗？',
	'users.unlock.success': '用户解锁成功',
	'users.unlock.failed': '用户解锁失败',

	// 用户组管理
	'users.group.title': '用户组',
	'users.group.description': '管理用户组和组成员',
	'users.group.button.addGroup': '添加用户组',
	'users.group.table.column.name': '组名',
	'users.group.table.column.description': '描述',
	'users.group.table.column.memberCount': '成员数量',
	'users.group.table.column.actions': '操作',

	// 用户组添加
	'users.groupAdd.title': '添加用户组',
	'users.groupAdd.form.name': '组名',
	'users.groupAdd.form.description': '描述',
	'users.groupAdd.success': '用户组添加成功',
	'users.groupAdd.failed': '用户组添加失败',

	// 用户组编辑
	'users.groupEdit.title': '编辑用户组',
	'users.groupEdit.success': '用户组更新成功',
	'users.groupEdit.failed': '用户组更新失败',

	// 用户组删除
	'users.groupDelete.title': '删除用户组',
	'users.groupDelete.content': '确定要删除用户组 {name} 吗？此操作不可撤销。',
	'users.groupDelete.success': '用户组删除成功',
	'users.groupDelete.failed': '用户组删除失败',

	// 数据导入导出基础
	'users.dataImport.description': '批量导入用户数据',
	'users.dataExport.description': '导出用户数据',

	// 用户暂停/解锁
	'users.suspend.enable': '启用用户',
	'users.suspend.disable': '禁用用户',
	'users.suspend.enableSuccess': '用户启用成功',
	'users.suspend.disableSuccess': '用户禁用成功',
	'users.suspend.enableFailed': '用户启用失败',
	'users.suspend.disableFailed': '用户禁用失败',

	// 用户密码重置
	'users.password.title': '重置密码',
	'users.password.success': '密码重置成功',
	'users.password.failed': '密码重置失败',

	// 用户角色编辑
	'users.editRole.title': '编辑角色',
	'users.editRole.success': '角色更新成功',
	'users.editRole.failed': '角色更新失败',

	// 用户MFA编辑
	'users.editMFA.title': '编辑MFA',
	'users.editMFA.success': 'MFA设置更新成功',
	'users.editMFA.failed': 'MFA设置更新失败',

	// 用户RDP编辑
	'users.editRDP.title': '编辑RDP',
	'users.editRDP.success': 'RDP设置更新成功',
	'users.editRDP.failed': 'RDP设置更新失败',

	// 用户过期时间编辑
	'users.editExpiresAt.title': '编辑过期时间',
	'users.editExpiresAt.success': '过期时间更新成功',
	'users.editExpiresAt.failed': '过期时间更新失败',

	// 通用按钮和操作
	'users.button.save': '保存',
	'users.button.cancel': '取消',
	'users.button.confirm': '确认',
	'users.button.query': '查询',
	'users.button.reset': '重置',
	'users.button.close': '关闭',

	// 通用消息
	'users.message.loading': '加载中...',
	'users.message.noData': '暂无数据',
	'users.message.operationSuccess': '操作成功',
	'users.message.operationFailed': '操作失败',

	// 用户组删除
	'users.groupDel.title': '删除用户组',
	'users.groupDel.content': '确定要删除用户组 {name} 吗？删除后该组下的所有用户将被移除。',
	'users.groupDel.confirmText': '输入 {name} 以确认删除',
	'users.groupDel.success': '用户组删除成功',
	'users.groupDel.failed': '用户组删除失败',

	// 数据导出
	'users.dataExport.title': '数据导出',
	'users.dataExport.button.export': '导出用户',
	'users.dataExport.table.column.fileName': '文件名',
	'users.dataExport.table.column.status': '状态',
	'users.dataExport.table.column.createTime': '创建时间',
	'users.dataExport.table.column.fileSize': '文件大小',
	'users.dataExport.table.column.actions': '操作',
	'users.dataExport.status.processing': '处理中',
	'users.dataExport.status.completed': '已完成',
	'users.dataExport.status.failed': '失败',
	'users.dataExport.action.download': '下载',
	'users.dataExport.action.delete': '删除',
	'users.dataExport.delete.title': '删除导出记录',
	'users.dataExport.delete.content': '确定要删除此导出记录吗？',
	'users.dataExport.delete.success': '导出记录删除成功',
	'users.dataExport.delete.failed': '导出记录删除失败',

	// 数据导入
	'users.dataImport.title': '数据导入',
	'users.dataImport.button.import': '导入用户',
	'users.dataImport.table.column.fileName': '文件名',
	'users.dataImport.table.column.status': '状态',
	'users.dataImport.table.column.createTime': '创建时间',
	'users.dataImport.table.column.totalCount': '总数',
	'users.dataImport.table.column.successCount': '成功数',
	'users.dataImport.table.column.failedCount': '失败数',
	'users.dataImport.table.column.actions': '操作',
	'users.dataImport.status.processing': '处理中',
	'users.dataImport.status.completed': '已完成',
	'users.dataImport.status.failed': '失败',
	'users.dataImport.action.viewDetails': '查看详情',
	'users.dataImport.action.delete': '删除',
	'users.dataImport.delete.title': '删除导入记录',
	'users.dataImport.delete.content': '确定要删除此导入记录吗？',
	'users.dataImport.delete.success': '导入记录删除成功',
	'users.dataImport.delete.failed': '导入记录删除失败',
	'users.dataImport.template.filename': '用户导入模板',
	'users.dataImport.success': '导入成功',
	'users.dataImport.failed': '导入失败',

	// MFA设备移除
	'users.removeMfaDevice.title': '移除MFA设备',
	'users.removeMfaDevice.content': '确定要移除用户 {name} 的MFA设备吗？',
	'users.removeMfaDevice.success': 'MFA设备移除成功',
	'users.removeMfaDevice.failed': 'MFA设备移除失败',

	// 密码重置
	'users.passwordReset.title': '重置密码',
	'users.passwordReset.content': '确定要重置用户 {name} 的密码吗？',
	'users.passwordReset.success': '密码重置成功',
	'users.passwordReset.failed': '密码重置失败',

	// MFA编辑
	'users.mfaEdit.title': '编辑MFA设置',
	'users.mfaEdit.form.enabled': '启用MFA',
	'users.mfaEdit.form.disabled': '禁用MFA',
	'users.mfaEdit.success': 'MFA设置更新成功',
	'users.mfaEdit.failed': 'MFA设置更新失败',

	// RDP编辑
	'users.rdpEdit.title': '编辑RDP设置',
	'users.rdpEdit.form.enabled': '启用RDP',
	'users.rdpEdit.form.disabled': '禁用RDP',
	'users.rdpEdit.success': 'RDP设置更新成功',
	'users.rdpEdit.failed': 'RDP设置更新失败',

	// 用户主页面额外
	'users.index.totalCount': '用户总数',
	'users.index.selectedCount': '已选中',
	'users.index.users': '个用户',
	'users.index.filter.statusPlaceholder': '请选择状态',
	'users.index.filter.rolePlaceholder': '请选择角色',
	'users.index.filter.groupPlaceholder': '请选择用户组',
	'users.index.filter.temporary': '请选择临时用户',
	'users.index.batch.enable': '批量启用',
	'users.index.batch.disable': '批量禁用',
	'users.index.batch.delete': '批量删除',
	'users.index.batch.editRole': '批量编辑角色',
	'users.index.batch.editGroup': '批量编辑用户组',
	'users.index.batch.editMFA': '批量编辑MFA',
	'users.index.batch.editRDP': '批量编辑RDP',
	'users.index.batch.editExpiresAt': '批量编辑过期时间',

	// 用户组页面
	'users.group.totalCount': '用户组总数',
	'users.group.filter.search': '根据用户组名称、编码、备注查询',

	// 用户角色和字段
	'users.role.admin': '管理员',
	'users.role.user': '普通用户',
	'users.role.superAdmin': '企业管理员',
	'users.role.unknown': '未知',
	'users.field.userGroup': '用户组',
	'users.field.loginName': '账号',
	'users.field.displayName': '显示名称',
	'users.field.password': '密码',
	'users.field.temporary': '账号有效期',
	'users.field.expiredAt': '到期时间',
	'users.field.mfaEnabled': '强制启用MFA',
	'users.field.groups': '用户组',
	'users.field.role': '角色',

	// 用户状态
	'users.status.enabled': '启用',
	'users.status.disabled': '禁用',

	// 角色描述
	'users.role.admin.description': '可以查看管理控制台并管理、设备和用户网络设置。',
	'users.role.user.description': '只能使用设备和网络，不能使用管理控制台。',
	'users.role.networkAdmin': '网络管理员',
	'users.role.networkAdmin.description': '可以查看管理控制台并管理 ACL 和网络设置。 无法管理设备或用户。',
	'users.role.itAdmin': 'IT管理员',
	'users.role.itAdmin.description': '可以查看管理控制台并管理设备和用户。 无法管理 ACL 或网络设置。',
	'users.role.billingAdmin': '帐单管理员',
	'users.role.billingAdmin.description': '可以查看管理控制台和管理帐单。',
	'users.role.auditor': '审计师',
	'users.role.auditor.description': '可以查看管理控制台',

	// 编辑角色
	'users.editRole.description': '编辑用户的角色',

	// 编辑MFA和RDP
	'users.editMFA.description': '编辑下列用户的MFA设置',
	'users.editRDP.description': '编辑下列用户的远程桌面设置',
	'users.field.rdpEnabled': '启用远程桌面',
	'users.field.rdpAutoGrant': '自动授权',
	'users.field.rdpAutoGrant.description': '启用远程桌面后，该用户的新设备将自动启用远程桌面',

	// 批量暂停
	'users.batch.suspend.confirm': '你确定要{action}下列用户吗？',
	'users.batch.suspend.description': '将{action}下列用户的设备接入权限。',
	'users.batch.suspend.pause': '暂停',
	'users.batch.suspend.resume': '恢复',

	// 本地化页面额外
	'users.index.titleLocal': '用户列表',
	'users.temporary.yes': '临时用户',
	'users.temporary.no': '非临时用户',

	// 表格列标题
	'users.table.column.user': '用户',
	'users.table.column.roleLocal': '角色',
	'users.table.column.groupLocal': '所属用户组',
	'users.table.column.createdAtLocal': '加入时间',
	'users.table.column.lastSeenLocal': '上次在线时间',
	'users.table.column.statusLocal': '状态',

	// 密码重置
	'users.passwordReset.description': '重置密码将会为用户生成新的密码',

	// 通用
	'common.all': '全部',
	'common.yes': '是',
	'common.no': '否',
	'common.saveSuccess': '保存成功',
	'common.saveFailed': '保存失败',

	// 用户详情页面
	'users.detail.getUserFailed': '获取用户信息失败，请稍后重试',
	'users.detail.getRelayConfigFailed': '获取中继服务器配置失败，请稍后重试',
	'users.detail.getDNSConfigFailed': '获取DNS配置失败，请稍后重试',
	'users.detail.viewDevices': '查看用户设备',
	'users.detail.viewRecentLogs': '查看最近日志',
	'users.detail.editUserInfo': '编辑用户信息',
	'users.detail.editUserGroup': '编辑用户组',
	'users.detail.editUserRole': '编辑用户角色',
	'users.detail.unlockAccount': '账号解锁',
	'users.detail.enableUser': '启用用户',
	'users.detail.disableUser': '禁用用户',
	'users.detail.deleteUser': '删除用户',
	'users.detail.setExpiry': '设置有效期',
	'users.detail.removeMFADevice': '解除MFA设备',
	'users.detail.onlineStatus': '在线状态',
	'users.detail.online': '在线',
	'users.detail.offline': '离线',
	'users.detail.unassigned': '未分配',
	'users.detail.notFilled': '未填写',
	'users.detail.accountDetails': '账号详情',
	'users.detail.accountAttributes': '用户账号属性',
	'users.detail.deviceList': '设备列表',
	'users.detail.networkSettings': '网络设置',
	'users.detail.saveDNSSuccess': '保存DNS配置成功',
	'users.detail.saveDNSFailed': '保存DNS配置失败，请稍后重试',
	'users.detail.saveRelaySuccess': '保存中继路由信息成功',
	'users.detail.saveRelayFailed': '保存中继路由信息失败，请稍后重试',

	// 用户添加页面额外
	'users.add.successTitle': '添加成功',
	'users.add.successMessage': '添加用户成功',
	'users.add.generate': '生成',

	// 表单验证
	'form.validation.required': '不能为空',
	'form.validation.minLength': '长度不能小于{length}',
	'form.validation.maxLength': '长度不能大于{length}',
	'form.validation.invalidFormat': '格式不正确',
	'form.validation.invalidEmail': '格式不正确,请填写正确的邮箱地址',
	'form.validation.invalidIPv4': '格式不正确,请填写正确的IPv4地址',
	'form.validation.invalidIPv6': '格式不正确,请填写正确的IPv6地址',

	// 用户组编辑
	'users.group.edit.title': '编辑用户组',
	'users.group.edit.success': '修改用户组成功',
	'users.group.edit.failed': '修改用户组失败',
	'users.group.edit.getFailed': '获取用户组失败',
	'users.group.field.name': '名称',
	'users.group.field.code': '编码',
	'users.group.field.code.help': '新建完成后不可修改',
	'users.group.field.description': '备注',
	'users.group.field.type': '类型',
	'users.group.field.expressionsCombo': '参数组合',
	'users.group.field.expressionsCombo.help': '运算符支持 and(与)、or (或)、or (或)、not (非)、()(括号)，例如：1 and 2，1 or 2, not 1,  1 or (not 1)',
	'users.group.type.static': '静态用户组',
	'users.group.type.dynamic': '动态用户组',
	'users.group.builtinUsers': '内置用户',
	'users.group.validation.nameRequired': '名称不能为空',
	'users.group.validation.codeRequired': '编码不能为空',
	'users.group.validation.codeInvalidStart': '编码不能以-开头',
	'users.group.validation.codeInvalidFormat': '编码只能包含字母、数字和\'-\'',
	'users.group.validation.expressionRequired': '表达式不能为空',
	'users.group.validation.parameterError': '触发参数错误',
	'users.group.dynamic.emptyAttributes': '动态用户组属性为空, 请前往',
	'users.group.dynamic.goToSettings': '设置',

	// 批量添加到用户组
	'users.batch.addToGroup.title': '添加用户到用户组',
	'users.batch.addToGroup.description': '下列用户即将添加到用户组：',
	'users.batch.addToGroup.selectPlaceholder': '请选择用户组',
	'users.batch.addToGroup.success': '添加用户到用户组成功',
	'users.batch.addToGroup.failed': '添加用户到用户组失败, 请稍后重试',

	// 编辑用户角色（单个用户）
	'users.editRole.singleTitle': '编辑用户{user}的角色',
	'users.editRole.singleDescription': '编辑用户 {user} 的角色',
	'users.editRole.singleSuccess': '编辑用户角色成功',
	'users.editRole.singleFailed': '编辑用户角色失败，请稍后重试',

	// 编辑用户过期时间
	'users.editExpiry.title': '修改用户{user}的有效期',
	'users.editExpiry.warning': '修改有效期后，用户将在新的有效期内可以登录系统，过期后将无法登录系统',
	'users.editExpiry.success': '修改成功',
	'users.editExpiry.successMessage': '用户{user}的有效期已修改',
	'users.editExpiry.failed': '修改失败',

	// 批量编辑用户过期时间
	'users.batch.editExpiry.title': '批量修改用户的有效期',
	'users.batch.editExpiry.description': '批量设置下列用户的有效期',
	'users.batch.editExpiry.success': '修改成功',
	'users.batch.editExpiry.successMessage': '用户的有效期已修改',
	'users.batch.editExpiry.failed': '修改失败',

	// 添加用户到组
	'users.addToGroup.title': '添加用户到用户组',
	'users.addToGroup.description': '添加用户 {user} 到用户组',
	'users.addToGroup.getACLFailed': '获取访问控制策略失败, 请稍后重试',

	// 从组中移除用户
	'users.removeFromGroup.title': '从用户组删除用户',
	'users.removeFromGroup.confirm': '确定把用户 {user} 从用户组 {group} 中删除吗？',
	'users.removeFromGroup.success': '操作成功',
	'users.removeFromGroup.successMessage': '用户{user}已从用户组{group}中删除',
	'users.removeFromGroup.failed': '操作失败',
	'users.removeFromGroup.failedMessage': '用户{user}从用户组{group}中删除失败',

	// 编辑用户组
	'users.editUserGroup.title': '编辑用户的用户组',
	'users.editUserGroup.description': '用户{user}的用户组',
	'users.editUserGroup.selectPlaceholder': '请选择用户组',
	'users.editUserGroup.success': '编辑用户的用户组成功',
	'users.editUserGroup.failed': '编辑用户的用户组失败, 请稍后重试',

	// 用户组DNS设置
	'users.groupDNS.title': '{group}网络设置',
	'users.groupDNS.getRelayFailed': '获取中继路由信息失败，请稍后重试',
	'users.groupDNS.getDNSFailed': '获取设备DNS信息失败，请稍后重试',
	'users.groupDNS.saveRelayFailed': '保存中继路由信息失败，请稍后重试',

	// 数据导出
	'users.dataExport.fileName': '文件名',
	'users.dataExport.operator': '操作人',
	'users.dataExport.recordCount': '数据条数',
	'users.dataExport.exportTime': '导出时间',
	'users.dataExport.exportParams': '导出参数',

	// 批量删除用户
	'users.batch.delete.warning': '即将批量删除下列用户',

	// 批量启用用户
	'users.batch.enable': '批量启用',

	// 数据导入
	'users.dataImport.fileName': '文件名',
	'users.dataImport.totalLines': '数据行数',
	'users.dataImport.errorLines': '错误行数',
	'users.dataImport.createdLines': '创建行数',
	'users.dataImport.updatedLines': '更新行数',
	'users.dataImport.completed': '是否完成',

	// 用户组表格
	'users.group.table.name': '名称',
	'users.group.table.description': '备注',
	'users.group.table.type': '类型',
	'users.group.table.userCount': '用户数',
	'users.group.table.createdAt': '创建时间',

	// 用户组操作
	'users.group.actions.viewUsers': '查看用户',
	'users.group.actions.edit': '编辑用户组',
	'users.group.actions.networkSettings': '网络设置',
	'users.group.actions.delete': '删除用户组',

	// 本地用户管理页面
	'users.index.titleLocalPage': '用户管理',
	'users.index.descriptionLocal': '管理接入网络的用户及其权限',
	'users.search.placeholder': '根据用户信息搜索',
	'users.filter.status': '用户状态',
	'users.filter.role': '角色',
	'users.filter.type': '类型',

	// 用户状态扩展
	'users.status.temporary': '临时用户',
	'users.status.expired': '已过期',
	'users.status.notJoined': '尚末加入',
	'users.status.online': '在线',
	'users.status.expiryDate': '有效期',

	// 用户操作扩展
	'users.actions.editRole': '编辑用户角色',
	'users.actions.viewDevices': '查看用户设备',
	'users.actions.viewLogs': '查看最近日志',
	'users.actions.editUser': '编辑用户信息',
	'users.actions.resetPassword': '重置密码',
	'users.actions.unlockAccount': '账号解锁',
	'users.actions.editUserGroup': '编辑用户组',
	'users.actions.removeFromGroup': '从本组删除',
	'users.actions.mfaSettings': 'MFA设置',
	'users.actions.enableUser': '启用用户',
	'users.actions.disableUser': '禁用用户',
	'users.actions.deleteUser': '删除用户',
	'users.actions.setExpiry': '设置有效期',
	'users.actions.removeMFADevice': '解除MFA设备',

	// 用户错误消息
	'users.error.getUserListFailed': '获取用户列表失败, 请稍后重试',

	// 筛选器
	'users.filter.selectUserGroups': '请选择用户组',

	// 数据导出/导入搜索
	'users.dataExport.searchPlaceholder': '根据文件名、导出参数查询',
	'users.dataImport.searchPlaceholder': '根据文件名查询',

	// 密码重置
	'users.password.copyInstruction': '请在下方复制用户新的密码。',
	'users.password.expiryWarning': '该密码将于XXXXXXX到期。如果您之后想继续使用认证密钥，则需要生成一个新密钥。',

	// 资源管理
	'resources.title': '资源列表',
	'resources.description': '管理接入网络的资源组',
	'resources.tab.preview': '预览',
	'resources.tab.resourceGroup': '资源组',
	'resources.search.placeholder': '根据IP或备注搜索',
	'resources.searchByName': '根据名称搜索',
	'resources.preview.helpText': '资源组和关联的资源组下属所有IP预览',
	'resources.empty.title': '未选中资源组',
	'resources.empty.description': '请选择资源组。',
	'resources.button.batchEdit': '批量编辑',

	// 资源表格
	'resources.table.index': '序号',
	'resources.table.memo': '备注',

	// IP输入
	'resources.ip.helpText': '请输入IP或IP段，如：**********或**********/24或**********-**********00或**********-100',
	'resources.ip.placeholder': '请输入IP或IP段',
	'resources.memo.placeholder': '请输入备注',

	// 资源验证
	'resources.validation.ipRequired': '请输入IP',
	'resources.validation.ipInvalid': 'IP格式不对',
	'resources.validation.nameExists': '名称已存在',
	'resources.validation.nameRequired': '名称不能为空',
	'resources.validation.ipListError': 'IP 列表有错误，请检查',
	'resources.validation.contentRequired': '内容不能为空',
	'resources.validation.emptyLine': '第{line}行为空行',
	'resources.validation.ipFormatError': '第{line}行IP格式错误',
	'resources.validation.ipDuplicate': '第{line1}行与第{line2}行IP重复',

	// 资源新建
	'resources.new.title': '新建资源组',
	'resources.field.name': '名称',
	'resources.field.description': '描述',
	'resources.field.associatedGroups': '关联资源组',

	// 批量编辑
	'resources.batchEdit.title': '批量编辑资源组',
	'resources.batchEdit.helpText': '每行一个IP，格式为IP或IP段，描述信息，用逗号分隔。IP或IP段，如：**********或**********/24或**********-**********00或**********-100',

	// 预览功能
	'resources.preview.searchPlaceholder': '根据IP搜索',

	// 资源组操作
	'resources.action.moveToTop': '置顶',
	'resources.action.moveUp': '上移',
	'resources.action.moveDown': '下移',
	'resources.action.moveToBottom': '置底',
	'resources.action.copyAdd': '复制添加',
	'resources.action.copySuffix': '复制',

	// 删除确认
	'resources.delete.confirmTitle': '确定要删除吗',
	'resources.delete.confirmContent': '此修改将不可逆',

	// 设置页面分类
	'settings.category.system': '系统设置',
	'settings.category.network': '网络设置',
	'settings.category.security': '安全设置',
	'settings.category.data': '数据设置',
	'settings.category.rights': '权益中心',

	// 设置菜单项
	'settings.menu.device': '设备',
	'settings.menu.user': '用户',
	'settings.menu.client': '版本发布',
	'settings.menu.service': '服务',
	'settings.menu.flynet': '基础',
	'settings.menu.relay': '中继',
	'settings.menu.connector': '连接器',
	'settings.menu.api': 'API调用',
	'settings.menu.sensitive': '字段脱敏',
	'settings.menu.keys': '密钥管理',
	'settings.menu.schema': '属性',
	'settings.menu.logs': '日志',
	'settings.menu.datasource': '数据源',
	'settings.menu.application': '应用面板',
	'settings.menu.rights': '权益设置',

	// 设备设置
	'settings.device.title': '设备',
	'settings.device.description': '管理零信任组网的设备设置',
	'settings.device.approval.title': '设备审批',
	'settings.device.approval.description': '要求新设备在访问零信任组网之前得到管理员的批准',
	'settings.device.approval.manual': '手动审批新设备',
	'settings.device.mesh.title': 'Mesh模式',
	'settings.device.mesh.description': '默认不开启， 开启后新设备默认加入 Mesh 网络对其它设备可见。',
	'settings.device.mesh.enable': '开启Mesh模式',

	// 用户设置
	'settings.user.title': '用户',
	'settings.user.description': '管理用户设置',
	'settings.user.manualCreate.title': '手动创建账号',
	'settings.user.manualCreate.description': '管理员可以在管理控制台用户管理模块手动创建账号',
	'settings.user.manualCreate.enable': '手动创建账号',

	// 网络基础设置
	'settings.flynet.title': '基础',
	'settings.flynet.description': '管理网络基础设置',
	'settings.flynet.basicInfo.title': '基本信息',
	'settings.flynet.basicInfo.description': '网络基础信息',
	'settings.flynet.networkName': '网络标识',
	'settings.flynet.networkId': '网络ID',

	// API设置
	'settings.api.title': 'API调用',
	'settings.api.description': '管理API调用基础设置',
	'settings.api.whitelist.title': 'API授权白名单',
	'settings.api.whitelist.description': '设置API授权白名单,在白名单内的IP地址可以通过API KEY访问API。',
	'settings.api.editMode.list': '列表编辑',
	'settings.api.editMode.text': '文本编辑',
	'settings.api.notification.success': '设置成功',
	'settings.api.notification.failed': '设置失败',
	'settings.api.ipFormat.title': '支持以下格式：',
	'settings.api.ipFormat.single': '单个IP：**********',
	'settings.api.ipFormat.cidr': 'CIDR格式：**********/24',
	'settings.api.ipFormat.range': 'IP范围：**********-************或**********-255',
	'settings.api.validation.ipRequired': '请输入IP地址',
	'settings.api.validation.ipPlaceholder': '请输入IP地址',

	// 密钥管理
	'settings.keys.title': '密钥',
	'settings.keys.description': '查看和管理您的认证密钥',

	// 数据源设置
	'settings.datasource.title': '数据源',
	'settings.datasource.description': '查看和管理数据源',
	'settings.datasource.importPolicy': '策略数据导入',
	'settings.datasource.importUser': '用户数据导入',

	// 日志设置
	'settings.logs.title': '日志',
	'settings.logs.description': '查看和管理您的日志',

	// 中继设置
	'settings.relay.title': '中继',
	'settings.relay.description': '管理中继服务器设置',
	'settings.relay.saveSuccess': '设置成功',
	'settings.relay.saveFailed': '设置失败',

	// 连接器设置
	'settings.connector.title': '连接器',
	'settings.connector.description': '管理连接器设置',
	'settings.connector.groups.title': '连接器组',
	'settings.connector.groups.description': '用于对连接器进行分组管理',
	'settings.connector.createGroup': '新建组',

	// 字段脱敏设置
	'settings.sensitive.title': '敏感字段',
	'settings.sensitive.description': '设置敏感字段，敏感字段将会被脱敏处理。',
	'settings.sensitive.editMode.list': '列表编辑',
	'settings.sensitive.editMode.text': '文本编辑',
	'settings.sensitive.notification.success': '设置成功',
	'settings.sensitive.notification.failed': '设置失败',
	'settings.sensitive.column.operation': '操作',
	'settings.sensitive.column.path': '路径',
	'settings.sensitive.column.value': '值',
	'settings.sensitive.operation.add': '添加',
	'settings.sensitive.operation.replace': '替换',
	'settings.sensitive.operation.mask': '打码',
	'settings.sensitive.valueType.string': '字符串',
	'settings.sensitive.valueType.number': '数字',
	'settings.sensitive.valueType.boolean': '布尔值',
	'settings.sensitive.valueType.object': '对象',
	'settings.sensitive.valueType.array': '数组',
	'settings.sensitive.valueType.null': '空',
	'settings.sensitive.validation.pathRequired': '第{index}条数据的路径不能为空',
	'settings.sensitive.validation.pathMustStartWithSlash': '第{index}条数据的路径必须以\'/\'开头',
	'settings.sensitive.validation.operationRequired': '第{index}条数据的操作不能为空',
	'settings.sensitive.validation.valueRequired': '第{index}条数据的值不能为空',
	'settings.sensitive.validation.incompleteData': '数据不完整，请检查后重新提交',
	'settings.sensitive.validation.pathRequiredSimple': '路径不能为空',
	'settings.sensitive.validation.pathMustStartWithSlashSimple': '路径必须以 / 开头',

	// 属性设置
	'settings.schema.title': '属性',
	'settings.schema.description': '管理用户、设备的属性设置',
	'settings.schema.attribute.title': '属性配置',
	'settings.schema.attribute.description': '管理用户和设备的属性设置。配置示例:',
	'settings.schema.attribute.invalidJson': '属性配置不是合法的 JSON',
	'settings.schema.attribute.saveConfig': '保存配置',
	'settings.schema.attribute.preview': '属性预览',
	'settings.schema.attribute.userAttributes': '用户属性',
	'settings.schema.attribute.deviceAttributes': '设备属性',
	'settings.schema.aclGroup.invalidJson': '策略组模板不是合法的 JSON',
	'settings.schema.aclGroup.editTitle': '编辑策略组模板',
	'settings.schema.userGroup.invalidJson': '用户组模板不是合法的 JSON',
	'settings.schema.userGroup.editTitle': '编辑用户组模板',
	'settings.schema.attribute.exampleDescription': '属性模板',
	'settings.schema.attribute.input': '输入',
	'settings.schema.attribute.user': '用户',
	'settings.schema.attribute.account': '账号',
	'settings.schema.attribute.attributes': '属性',
	'settings.schema.attribute.nickname': '昵称',
	'settings.schema.attribute.profile': '个人资料',
	'settings.schema.attribute.website': '个人站点',
	'settings.schema.attribute.gender': '性别',
	'settings.schema.admission.invalidJson': '网络准入策略模板不是合法的 JSON',
	'settings.schema.admission.editTitle': '编辑网络准入策略模板',
	'settings.schema.deviceGroup.invalidJson': '设备组模板不是合法的 JSON',
	'settings.schema.deviceGroup.editTitle': '编辑设备组模板',

	// 版本发布设置（已移动到客户端设置部分）

	// 服务设置
	'settings.service.title': '服务',
	'settings.service.description': '管理服务设置',

	// 权益设置
	'settings.rights.title': '权益',
	'settings.rights.description': '管理权益设置',
	'settings.rights.activationWarning': '系统尚未激活，为避免影响服务正常使用，请激活系统。',
	'settings.rights.activate': '激活',
	'settings.rights.licensee': '授权使用方',
	'settings.rights.updateLicense': '更新授权',
	'settings.rights.validUntil': '有效期至',
	'settings.rights.permanent': '永久授权',
	'settings.rights.machineCode': '机器码',

	// 数据源功能
	'settings.datasource.createSuccess': '新建数据源成功',
	'settings.datasource.createFailed': '新建数据源失败',
	'settings.datasource.addTitle': '添加数据源',
	'settings.datasource.field.name': '名称',
	'settings.datasource.field.code': '编码',
	'settings.datasource.field.codeHint': '新建完成后不可修改',
	'settings.datasource.validation.nameRequired': '名称不能为空',
	'settings.datasource.validation.codeRequired': '编码不能为空',

	// 密钥管理功能
	'settings.keys.createTokenSuccess': '创建令牌成功',
	'settings.keys.createTokenFailed': '创建令牌失败，请稍后重试',
	'settings.keys.generateApiToken': '生成API访问令牌',
	'settings.keys.field.description': '描述',
	'settings.keys.field.validity': '有效期',
	'settings.keys.field.expiryDays': '该API访问令牌到期前的天数',
	'settings.keys.field.permanent': '该API访问令牌将永久有效',
	'settings.keys.field.expiryRange': '必须介于 {min} 到 {max} 天之间',
	'settings.keys.keyExpire.title': '密钥过期时间',
	'settings.keys.keyExpire.description': '设置设备在需要重新验证之前可以保持登录的天数',
	'settings.keys.keyExpire.daysLabel': '天数',
	'settings.keys.keyExpire.daysRange': '必须介于 {min} 到 {max} 天之间。',
	'settings.keys.authKey.title': '设备认证密钥',
	'settings.keys.authKey.description': '无需交互式登录即可对设备进行认证',
	'settings.keys.authKey.generateButton': '生成认证密钥',
	'settings.keys.authKey.invalidKeysCount': '{count}个最近失效的授权密钥',
	'settings.keys.apiKey.title': 'API访问令牌',
	'settings.keys.apiKey.description': '使用访问令牌调用API，接口端点地址：{url}',
	'settings.keys.apiKey.sdkInfo': '使用',
	'settings.keys.apiKey.generateSdk': '生成客户端SDK',
	'settings.keys.apiKey.generateButton': '生成API访问令牌',
	'settings.keys.apiKey.invalidKeysCount': '{count}个最近失效的API访问令牌',
	'settings.keys.column.createdAt': '创建时间',
	'settings.keys.column.expiration': '过期时间',
	'settings.keys.column.config': '配置项',
	'settings.keys.config.reusable': '可复用',
	'settings.keys.config.oneTime': '一次性使用',
	'settings.keys.config.ephemeral': '临时设备',
	'settings.keys.config.persistent': '非临时设备',
	'settings.keys.config.preAuthorized': '预授权',
	'settings.keys.config.notPreAuthorized': '未启用预授权',
	'settings.keys.action.revoke': '撤销',
	'settings.keys.status.revoked': '撤销',
	'settings.keys.error.fetchFailed': '获取密钥列表失败，请稍后重试',
	'settings.keys.error.fetchAclFailed': '获取访问控制策略失败, 请稍后重试',
	'settings.keys.error.selectTags': '请选择标签',
	'settings.keys.generateAuthKey': '生成认证密钥',
	'settings.keys.field.reusable': '可复用',
	'settings.keys.field.reusableDesc': '是否允许该密钥进行多次认证',
	'settings.keys.field.ephemeral': '临时设备',
	'settings.keys.field.ephemeralDesc': '通过该密钥认证的设备，离线后将被自动移除',
	'settings.keys.field.preAuthorized': '预授权',
	'settings.keys.field.preAuthorizedDesc': '通过该密钥验证的设备将被自动审批通过',
	'settings.keys.field.tags': '标签',
	'settings.keys.field.tagsDesc': '设备将被打标签。打上标签密钥将永久有效。',
	'settings.keys.field.tagsPlaceholder': '请选择标签',
	'settings.keys.section.deviceSettings': '设备相关设置',
	'settings.keys.section.deviceSettingsDesc': '以下设置将应用于使用该密钥进行认证的设备',
	'settings.keys.result.title': '密钥生成结果',
	'settings.keys.result.warning': '请务必在下方复制您的新密钥。 它不会再完整显示。',
	'settings.keys.result.expireInfo': '该密钥将于{expire} 到期。如果您之后想继续使用认证密钥，则需要生成一个新密钥。',
	'settings.keys.revoke.title': '撤销密钥',
	'settings.keys.revoke.success': '撤销密钥成功',
	'settings.keys.revoke.failed': '撤销密钥失败，请稍后重试',
	'settings.keys.revoke.apiDescription': '撤销该密钥后，已通过认证的设备，认证继续有效，但会阻止您对新设备进行认证。',
	'settings.keys.revoke.authDescription': '撤销该密钥不会取消使用该密钥进行认证的设备的认证，但会阻止您对新设备进行认证。',

	// 设备设置功能
	'settings.device.keyExpiry.title': '密钥过期时间',
	'settings.device.keyExpiry.description': '设置设备在需要重新验证之前可以保持登录的天数',

	// 日志设置功能
	'settings.logs.createSuccess': '新建成功',
	'settings.logs.createFailed': '新建失败',
	'settings.logs.createConfig': '新建配置',
	'settings.logs.allTypesExist': '目前已经存在所有类型的配置，无法新建',
	'settings.logs.field.name': '名称',
	'settings.logs.field.code': '编码',
	'settings.logs.field.codeHint': '新建完成后不可修改',
	'settings.logs.validation.nameRequired': '名称不能为空',
	'settings.logs.validation.codeRequired': '编码不能为空',

	// 连接器设置功能
	'settings.connector.createSuccess': '新建连接器组成功',
	'settings.connector.createFailed': '新建连接器组失败',
	'settings.connector.createTitle': '新建连接器组',
	'settings.connector.field.name': '名称',
	'settings.connector.field.code': '编码',
	'settings.connector.field.codeHint': '新建完成后不可修改',
	'settings.connector.validation.nameRequired': '名称不能为空',
	'settings.connector.validation.nameDuplicate': '名称重复',
	'settings.connector.validation.codeRequired': '编码不能为空',

	// 用户账号模式功能
	'settings.user.schema.title': '账号可用字段',
	'settings.user.schema.description': '设置用户账号可用字段',
	'settings.user.schema.column.name': '名称',
	'settings.user.schema.column.title': '标题',
	'settings.user.schema.column.type': '类型',
	'settings.user.schema.column.enabled': '启用',
	'settings.user.schema.status.enabled': '已启用',
	'settings.user.schema.status.disabled': '未启用',
	'settings.user.schema.preview.title': '预览账号字段',

	// 敏感字段设置
	'settings.user.sensitivePatch.title': '敏感字段',
	'settings.user.sensitivePatch.description': '设置敏感字段，敏感字段将会被脱敏处理。',
	'settings.user.sensitivePatch.listEdit': '列表编辑',
	'settings.user.sensitivePatch.textEdit': '文本编辑',
	'settings.user.sensitivePatch.success': '设置成功',
	'settings.user.sensitivePatch.failed': '设置失败',
	'settings.user.sensitivePatch.validation.incomplete': '数据不完整，请检查后重新提交',
	'settings.user.sensitivePatch.validation.pathRequired': '第{index}条数据的路径不能为空;',
	'settings.user.sensitivePatch.validation.pathFormat': '第{index}条数据的路径必须以\'/\'开头;',
	'settings.user.sensitivePatch.validation.opRequired': '第{index}条数据的操作不能为空;',
	'settings.user.sensitivePatch.validation.valueRequired': '第{index}条数据的值不能为空;',

	// 敏感字段列表编辑
	'settings.user.sensitivePatch.list.operation': '操作',
	'settings.user.sensitivePatch.list.path': '路径',
	'settings.user.sensitivePatch.list.value': '值',
	'settings.user.sensitivePatch.operation.add': '添加',
	'settings.user.sensitivePatch.operation.replace': '替换',
	'settings.user.sensitivePatch.operation.remove': '删除',
	'settings.user.sensitivePatch.operation.mask': '打码',
	'settings.user.sensitivePatch.validation.pathEmpty': '路径不能为空',
	'settings.user.sensitivePatch.validation.pathStartSlash': '路径必须以 / 开头',
	'settings.user.sensitivePatch.valueType.string': '字符串',
	'settings.user.sensitivePatch.valueType.number': '数字',
	'settings.user.sensitivePatch.valueType.boolean': '布尔值',
	'settings.user.sensitivePatch.valueType.object': '对象',
	'settings.user.sensitivePatch.valueType.array': '数组',
	'settings.user.sensitivePatch.valueType.null': '空',

	// 账号字段编辑
	'settings.user.schema.edit.title': '编辑可用字段',
	'settings.user.schema.edit.success': '设置成功',
	'settings.user.schema.edit.failed': '设置失败',

	// 客户端设置
	'settings.client.title': '客户端',
	'settings.client.description': '管理客户端版本和下载',

	// 客户端版本管理
	'settings.client.version.list.title': '版本列表',
	'settings.client.version.list.createVersion': '新建版本',
	'settings.client.version.unknownVersion': '未知版本',
	'settings.client.version.table.index': '序号',
	'settings.client.version.table.versionNumber': '版本号',
	'settings.client.version.table.releaseNotes': '更新内容',
	'settings.client.version.table.forceUpgrade': '强制更新',
	'settings.client.version.table.releaseDate': '发布日期',
	'settings.client.version.table.actions': '操作',
	'settings.client.version.delete.confirmTitle': '确定删除该版本吗？',
	'settings.client.version.delete.confirmContent': '此修改将不可逆',

	// 添加/编辑版本
	'settings.client.addInstall.editTitle': '修改版本',
	'settings.client.addInstall.createTitle': '新建版本',
	'settings.client.addInstall.updateSuccess': '版本修改成功',
	'settings.client.addInstall.updateFailed': '修改版本失败，请稍后重试',
	'settings.client.addInstall.createSuccess': '新建版本成功',
	'settings.client.addInstall.createFailed': '新建版本失败，请稍后重试',
	'settings.client.addInstall.form.versionName': '版本名称',
	'settings.client.addInstall.form.platform': '平台',
	'settings.client.addInstall.form.platformPlaceholder': '请选择平台',
	'settings.client.addInstall.form.releaseDate': '发行日期',
	'settings.client.addInstall.form.appstoreRelease': 'AppStore内发布',
	'settings.client.addInstall.form.forceUpgrade': '强制更新',
	'settings.client.addInstall.form.releaseNotes': '发行说明',
	'settings.client.addInstall.form.packageType': '类型',
	'settings.client.addInstall.form.chipType': '芯片类型',
	'settings.client.addInstall.form.address': '地址',
	'settings.client.addInstall.packageType.installer': '安装包',
	'settings.client.addInstall.packageType.incremental': '增量包',
	'settings.client.addInstall.packageType.upgrade': '升级包',
	'settings.client.addInstall.validation.versionNameRequired': '请填写版本名称',
	'settings.client.addInstall.validation.versionNameFormat': '版本号格式应为 x.y.z（如 1.2.0）',
	'settings.client.addInstall.validation.versionExists': '该平台下该版本号已存在，请更换版本号或前去修改',
	'settings.client.addInstall.validation.platformRequired': '请选择平台',
	'settings.client.addInstall.validation.releaseDateRequired': '请选择发行日期',
	'settings.client.addInstall.validation.releaseNotesRequired': '请填写发行说明',
	'settings.client.addInstall.upload.installerSuccess': '安装包已上传完成',
	'settings.client.addInstall.upload.upgradeSuccess': '升级包已上传完成',
	'settings.client.addInstall.validation.distroRequired': '请选择发行版',
	'settings.client.addInstall.validation.downloadLinkRequired': '请填写包地址',
	'settings.client.addInstall.upload.macosInstallerFolder': '请选择名称为【MacOS安装包_{arch}】的文件夹',
	'settings.client.addInstall.upload.windowsInstallerFolder': '请选择名称为【Windows安装包】的文件夹',
	'settings.client.addInstall.upload.androidInstallerFolder': '请选择名称为【Android安装包】的文件夹',
	'settings.client.addInstall.upload.iosInstallerFolder': '请选择名称为【iOS安装包】的文件夹',
	'settings.client.addInstall.upload.macosUpgradeFolder': '请选择名称为【MacOS升级包_{arch}】的文件夹',
	'settings.client.addInstall.upload.windowsUpgradeFolder': '请选择名称为【Windows升级包】的文件夹',
	'settings.client.addInstall.upload.androidUpgradeFolder': '请选择名称为【Android升级包】的文件夹',
	'settings.client.addInstall.upload.iosUpgradeFolder': '请选择名称为【iOS升级包】的文件夹',
	'settings.client.addInstall.upload.defaultTip': '请勿修改文件，选择对应文件夹直接上传',
	'settings.client.addInstall.upload.failed': '上传失败',
	'settings.client.addInstall.upload.clickToUpload': '点击上传',
	'settings.client.addInstall.upload.invalidFormat': '文件格式不正确',

	// 添加包
	'settings.client.addPackage.title': '新建包',
	'settings.client.addPackage.createSuccess': '创建包成功',
	'settings.client.addPackage.createFailed': '创建包失败，请稍后重试',
	'settings.client.addPackage.form.packageType': '包类型',
	'settings.client.addPackage.form.distro': '发行版',
	'settings.client.addPackage.form.chipType': '芯片类型',
	'settings.client.addPackage.form.downloadLink': '包地址',
	'settings.client.addPackage.packageType.installer': '安装包',
	'settings.client.addPackage.packageType.incremental': '增量包',
	'settings.client.addPackage.packageType.upgrade': '升级地址',
	'settings.client.addPackage.validation.downloadLinkRequired': '包地址不能为空',
	'settings.client.addPackage.validation.distroRequired': '发行版不能为空',
	'settings.client.addPackage.validation.packageTypeRequired': '包类型不能为空',

	// Flynet 网络设置页面
	'settings.flynet.page.title': 'Flynet 网络',
	'settings.flynet.page.description': '管理 Flynet 网络配置和设置',
	'settings.flynet.page.networkName': '网络名称',
	'settings.flynet.page.cancelEdit': '取消修改',
	'settings.flynet.page.saveEdit': '保存修改',
	'settings.flynet.page.zeroTrustSegment': '零信任网段',
	'settings.flynet.page.contactServiceProvider': '若需要修改，请联系服务商',

	// DNS 设置
	'settings.dns.title': '域名解析',
	'settings.dns.description': '管理零信任组网的域名解析',
	'settings.dns.domainName': '域名称',
	'settings.dns.deviceDomain.title': '分配设备域名',
	'settings.dns.deviceDomain.description': '自动为网络中的设备分配域名，这使您可以直接使用设备的名称访问该设备',
	'settings.dns.deviceDomain.enable': '启用分配设备域名',
	'settings.dns.deviceDomain.disable': '禁用分配设备域名',
	'settings.dns.httpsCerts.title': 'HTTPS 证书',
	'settings.dns.httpsCerts.description': '允许用户为他们的设备提供 HTTPS 证书',
	'settings.dns.httpsCerts.enable': '启用HTTPS证书',
	'settings.dns.httpsCerts.disable': '禁用HTTPS证书',
	'settings.dns.dnsServers.title': 'DNS服务器',
	'settings.dns.dnsServers.description': '设置设备使用的DNS服务器',
	'settings.dns.dnsServers.add': '添加DNS服务器',
	'settings.dns.dnsServers.deleteSuccess': '删除DNS服务器成功',
	'settings.dns.dnsServers.deleteFailed': '删除DNS服务器失败，请稍后重试',
	'settings.dns.deviceDomain.enableSuccess': '启用分配设备域名成功',
	'settings.dns.deviceDomain.enableFailed': '启用分配设备域名失败，请稍后重试',
	'settings.dns.httpsCerts.enablePrerequisite': '启用HTTPS前请先启用分配设备域名',
	'settings.dns.httpsCerts.enableSuccess': '启用HTTPS成功',
	'settings.dns.httpsCerts.enableFailed': '启用HTTPS失败，请稍后重试',
	'settings.dns.overrideLocalDns.success': '覆盖本地DNS成功',
	'settings.dns.overrideLocalDns.failed': '覆盖本地DNS失败，请稍后重试',
	'settings.dns.overrideLocalDns.label': '覆盖本地DNS',
	'settings.dns.overrideLocalDns.tooltip': '启用该选项后，客户端将忽略本地 DNS 设置并始终使用全局DNS服务器。',
	'settings.dns.overrideLocalDns.tooltipDisabled': '禁用该选项时，客户端优先使用本地 DNS 设置，仅在需要时使用全局DNS服务器。',
	'settings.dns.globalDnsServers': '全局DNS服务器',
	'settings.dns.globalDnsServers.notSet': '您还没有设置全局DNS服务器',
	'settings.dns.localDnsSettings': '本地DNS设置',
	'settings.dns.splitDomain': '限定域',
	'settings.dns.recordType.a': 'A记录',
	'settings.dns.customDomain.title': '自定义域名解析',
	'settings.dns.customDomain.description': '设置自定义域名解析',
	'settings.dns.customDomain.add': '添加自定义域名解析',
	'settings.dns.customDomain.notSet': '您还没有设置自定义域名解析或启用分配设备域名',
	'settings.dns.customDomain.enableOverrideRequired': '要使用此功能，请启用 "覆盖本地DNS"',
	'settings.dns.customDomain.deleteSuccess': '删除自定义域名解析成功',
	'settings.dns.customDomain.deleteFailed': '删除自定义域名解析失败，请稍后重试',
	'settings.dns.addNameserver.success': '添加DNS服务器成功',
	'settings.dns.addNameserver.failed': '添加DNS服务器失败，请稍后重试',
	'settings.dns.addNameserver.title': '添加DNS服务器',
	'settings.dns.addNameserver.dnsServerTitle': 'DNS服务器',
	'settings.dns.addNameserver.dnsServerDescription': '使用 IPv4 或 IPv6 地址解析域名',
	'settings.dns.addNameserver.applyToSpecificDomain': '应用于特定域名',
	'settings.dns.addNameserver.splitDnsDescription': '该DNS服务器将仅用于某些域名',
	'settings.dns.addNameserver.domainTitle': '域名',
	'settings.dns.addNameserver.domainUsageDescription': '仅在以下域名中使用此DNS服务器。 例如，如果您的域名是 example.com，则此DNS服务器将仅用于 example.com 和其子域名（例如，www.example.com）',
	'settings.dns.validation.ipRequired': '请输入IP地址',
	'settings.dns.validation.cannotUseDefaultIp': '不能使用默认IP地址***************',
	'settings.dns.validation.invalidIp': '请输入正确的IP地址',
	'settings.dns.validation.ipExists': '该IP地址已存在',
	'settings.dns.validation.domainRequired': '请输入域名',
	'settings.dns.validation.invalidDomain': '域名应包含字母数字小写字符、连字符和句点。',
	'settings.dns.editNameserver.success': '编辑DNS服务器成功',
	'settings.dns.editNameserver.failed': '编辑DNS服务器失败，请稍后重试',
	'settings.dns.addSearchdomain.success': '添加域名服务器成功',
	'settings.dns.addSearchdomain.failed': '添加域名服务器失败，请稍后重试',
	'settings.dns.disableDns.title': '禁用分配设备域名',
	'settings.dns.disableDns.success': '禁用分配设备域名成功',
	'settings.dns.disableDns.failed': '禁用分配设备域名失败，请稍后重试',
	'settings.dns.disableDns.description': '您网络中的用户将无法再使用设备名称来访问该设备。',
	'settings.dns.disableDns.warning': '提示：禁用分配设备域名同时会禁用HTTPS证书。',
	'settings.dns.disableHttps.title': '禁用颁发HTTPS证书',
	'settings.dns.disableHttps.success': '禁用颁发HTTPS证书成功',
	'settings.dns.disableHttps.failed': '禁用颁发HTTPS证书失败，请稍后重试',
	'settings.dns.disableHttps.description': '禁用颁发HTTPS证书后，您网络中的用户将无法使用HTTPS访问该设备。',
	'settings.dns.addNameserver.ipPlaceholder': '请输入IP地址',
	'settings.dns.addSearchdomain.title': '添加自定义域名解析',
	'settings.dns.addSearchdomain.domainTitle': '域名',
	'settings.dns.addSearchdomain.domainPlaceholder': '请输入域名',
	'settings.dns.addSearchdomain.nameColumn': '名称',
	'settings.dns.addSearchdomain.typeColumn': '类型',
	'settings.dns.addSearchdomain.valueColumn': '值',
	'settings.dns.addSearchdomain.namePlaceholder': '名称',
	'settings.dns.addSearchdomain.ipv4Placeholder': 'IPv4地址',
	'settings.dns.validation.domainExists': '域名已存在',
	'settings.dns.domainNameTitle': '域名称',
	'settings.dns.domainNameDescription': '注册DNS与颁发TLS证书时的唯一名称',
	'settings.dns.modifyDomainName': '修改域名称',
	'settings.dns.editSearchdomain.title': '编辑自定义域名解析',
	'settings.dns.editSearchdomain.success': '编辑域名服务器成功',
	'settings.dns.editSearchdomain.failed': '编辑域名服务器失败，请稍后重试',
	'settings.dns.editSearchdomain.domainTitle': '域名',
	'settings.dns.editSearchdomain.domainPlaceholder': '域名',
	'settings.dns.editSearchdomain.nameColumn': '名称',
	'settings.dns.editSearchdomain.typeColumn': '类型',
	'settings.dns.editSearchdomain.valueColumn': '值',
	'settings.dns.editSearchdomain.namePlaceholder': '名称',
	'settings.dns.editSearchdomain.ipv4Placeholder': 'IPv4地址',
	'settings.dns.editSearchdomain.cnamePlaceholder': '目标，例如：www.example.com',
	'settings.dns.validation.cnameRequired': '请输入CNAME',
	'settings.dns.validation.invalidCname': 'CNAME应包含字母数字小写字符、连字符和句点',
	'settings.dns.validation.recordExists': '记录已存在',
	'settings.dns.validation.circularReference': '不能循环引用',
	'settings.dns.editNameserver.title': '编辑DNS服务器',
	'settings.dns.editNameserver.dnsServerTitle': 'DNS服务器',
	'settings.dns.editNameserver.dnsServerDescription': '使用 IPv4 或 IPv6 地址解析域名',
	'settings.dns.editNameserver.applyToSpecificDomain': '应用于特定域名',
	'settings.dns.editNameserver.splitDnsDescription': '该DNS服务器将仅用于某些域名',
	'settings.dns.editNameserver.domainTitle': '域名',
	'settings.dns.editNameserver.domainUsageDescription': '仅在以下域名中使用此DNS服务器。 例如，如果您的域名是 example.com，则此DNS服务器将仅用于 example.com 和其子域名（例如，www.example.com）。',
	'settings.dns.addSearchdomain.cnamePlaceholder': '目标，例如：www.example.com',

	// MFA 设置
	'settings.mfa.title': 'MFA',
	'settings.mfa.description': '管理MFA设置',
	'settings.mfa.sectionTitle': 'MFA',
	'settings.mfa.sectionDescription': '通过在身份认证过程中添加两个或多个不同的身份验证因素来提高身份验证的安全性',
	'settings.mfa.enableMfa': '启用MFA',

	// 通用组件
	'components.common.yes': '是',
	'components.common.no': '否',

	// 仪表板功能
	'dashboard.deviceManagement': '设备管理',
	'dashboard.networkTopology': '网络拓扑',
	'dashboard.physicalPorts': '物理网口',
	'dashboard.deviceMonitoring': '设备监控',

	// 策略管理功能
	'policies.resourceGroup': '资源组',
	'policies.expressions': '表达式',
	'policies.tags': '标签',

	// 策略选择器
	'policies.selector.all': '全部',
	'policies.selector.device': '设备',
	'policies.selector.deviceGroup': '设备组',
	'policies.service': '服务',

	// 策略组管理
	'policies.group.input': '输入',
	'policies.group.groupType': '类型',
	'policies.group.static': '静态策略组',
	'policies.group.dynamic': '动态策略组',
	'policies.group.expertMode': '专家模式',

	// 用户选择器
	'policies.user.fetchFailed': '获取用户列表失败, 请稍后重试',

	// 搜索过滤器组件
	'common.filter': '过滤器',
	'common.apply': '应用',

	// 服务管理
	'services.group.title': '服务组',
	'services.field.name': '服务名称',
	'services.field.type': '服务类型',
	'services.field.source': '服务来源',
	'services.field.status': '服务状态',
	'services.field.network': '加入网络',
	'services.field.site': '所属站点',
	'services.field.protocol': '服务协议',
	'services.field.port': '服务端口',
	'services.field.accessAddress': '服务访问地址',
	'services.field.physicalAddress': '服务物理地址',
	'services.field.createTime': '服务创建时间',
	'services.field.discoveryTime': '服务发现时间',
	'services.field.deviceGroup': '服务运行所在设备设备组',
	'services.field.customTags': '自定义标签',

	// 服务组件（新增）
	'services.components.source.config': '系统配置',
	'services.components.source.detect': '自动发现',

	// 服务操作
	'services.action.edit': '编辑服务',
	'services.action.delete': '删除服务',
	'services.action.removeFromGroup': '从本组删除',
	'services.action.selectDevice': '选择设备',

	// 服务编辑
	'services.edit.title': '编辑服务',
	'services.field.selectServiceGroup': '选择服务组',

	// 服务验证
	'services.validation.nameRequired': '名称不能为空',
	'services.validation.nameFormat': "名称只能包含字母、数字和'-'",

	// 服务删除（详细）
	'services.delete.titleWithName': '删除服务{name}',
	'services.delete.warning': '服务将被删除，删除后将无法访问服务？',
	'services.delete.confirmInput': '输入 {name} 以确认删除',

	// 策略标签（新增）
	'policies.label.resourceGroup': '资源组',
	'policies.label.user': '用户',
	'policies.label.userGroup': '用户组',
	'policies.label.builtinUserGroup': '内置用户组',
	'policies.label.device': '设备',
	'policies.label.deviceGroup': '设备组',
	'policies.label.service': '服务',
	'policies.label.serviceGroup': '服务组',
	'policies.label.expression': '表达式',
	'policies.label.l7Expression': '7层表达式',
	'policies.label.tag': '标签',

	// 内置用户组
	'policies.builtinGroup.self': '自已',
	'policies.builtinGroup.members': '普通用户',
	'policies.builtinGroup.internet': '公网用户',

	// 策略错误和同步
	'policies.error.fetchFailed': '获取访问控制策略失败, 请稍后重试',
	'policies.sync.success': '同步成功',
	'policies.sync.failed': '同步失败, 请稍后重试',

	// 策略工具提示
	'policies.tooltip.dynamicGroup': '动态策略组',

	// 端口相关
	'policies.port.label': '端口',
	'policies.port.formatDescription': '端口支持以下格式：',
	'policies.port.allPorts': '所有端口：*',
	'policies.port.singlePort': '单个端口：22或80',
	'policies.port.rangePort': '连续端口：1-65535或100-20000',
	'policies.port.multiplePorts': '多个端口：22,80,443或100-200,300-400或22,3389-3399',
	'policies.port.required': '端口不能为空',
	'policies.port.invalidFormat': '端口格式不正确',

	// IP相关
	'policies.ip.cidrSupport': '支持CIDR格式',
	'policies.ip.placeholder': '请输入IP',
	'policies.ip.required': 'IP不能为空',

	// 设备相关
	'policies.device.placeholder': '请选择设备',
	'policies.device.noDevices': '暂无设备',
	'policies.device.fetchFailed': '获取设备列表失败, 请稍后重试',

	// 用户相关
	'policies.user.placeholder': '请选择用户',
	'policies.user.noUsers': '暂无用户',
	'policies.user.orEnterUser': '或输入用户',
	'policies.user.required': '用户不能为空',

	// 服务相关
	'policies.service.placeholder': '请选择服务',

	// 域名相关
	'policies.domain.placeholder': '请输入域名',
	'policies.domain.required': '域名不能为空',

	// 服务组相关
	'policies.serviceGroup.placeholder': '请选择服务组',

	// IP相关扩展
	'policies.ip.multiSupport': '支持CIDR格式，多个以逗号分隔',

	// 表达式相关
	'policies.expression.placeholder': '请选择表达式',
	'policies.expression.layer7': '七层网络',

	// 资源组相关
	'policies.resourceGroup.placeholder': '请选择资源组',

	// 域名相关扩展
	'policies.domain.label': '域名',

	// 数据导入导出基础
	'policies.dataExport.export': '导出策略',
	'policies.dataExport.modal.title': '策略导出',
	'policies.dataExport.modal.export': '导出',

	// 用户认证密钥组件
	'userAuthKey.title': '认证码',
	'userAuthKey.description': '该密钥有效期为10分钟，且只能使用一次',
	'userAuthKey.createFailed': '创建密钥失败，请稍后重试',
	'userAuthKey.refresh': '刷新',
	'userAuthKey.generateTime': '生成时间',
	'userAuthKey.scanInstruction': '请使用App扫码接入网络',

	// 用户设备组件
	'userDevice.fetchFailed': '获取设备列表失败，请稍后重试',
	'userDevice.deviceList': '设备列表',
	'userDevice.viewUserDevices': '查看该用户的设备列表',

	// 用户模态选择器组件
	'userModal.selectUser': '选择用户',
	'userModal.searchPlaceholder': '根据用户信息搜索',
	'userModal.role': '角色',
	'userModal.totalUsers': '用户总数',
	'userModal.normalUser': '普通用户',
	'userModal.admin': '管理员',

	// 用户选择器组件
	'userSelector.fetchFailed': '获取用户列表失败, 请稍后重试',
	'userSelector.operator': '操作者',
	'userSelector.noUsers': '暂无用户',

	// 时间范围组件
	'timeRange.manualSelect': '手动选择时间',
	'timeRange.quickSelect': '快捷选择',
	'timeRange.last5Minutes': '最近5分钟',
	'timeRange.last15Minutes': '最近15分钟',
	'timeRange.last30Minutes': '最近30分钟',
	'timeRange.last1Hour': '最近1小时',
	'timeRange.last3Hours': '最近3小时',
	'timeRange.last6Hours': '最近6小时',
	'timeRange.last24Hours': '最近24小时',
	'timeRange.last3Days': '最近3天',
	'timeRange.last7Days': '最近7天',
	'timeRange.last30Days': '最近30天',
	'timeRange.last90Days': '最近90天',

	// 服务层 - Flynet 服务
	'services.flynet.queryNetworkError': '查询网络异常，请稍后再试',
	'services.flynet.enableMachineAuthSuccess': '启用新设备审批成功',
	'services.flynet.enableMachineAuthFailed': '启用新设备审批失败，请稍后重试',
	'services.flynet.disableMachineAuthSuccess': '禁用新设备审批成功',
	'services.flynet.disableMachineAuthFailed': '禁用新设备审批失败，请稍后重试',
	'services.flynet.updateMeshSuccess': '更新Mesh模式成功',
	'services.flynet.updateMeshFailed': '更新Mesh失败，请稍后重试',
	'services.flynet.updateAppPanelSuccess': '更新应用面板状态成功',
	'services.flynet.updateAppPanelFailed': '更新应用面板状态失败，请稍后重试',
	'services.flynet.updateKeyExpireSuccess': '更新密钥过期时间成功',
	'services.flynet.updateKeyExpireFailed': '更新密钥过期时间失败，请稍后重试',
	'services.flynet.updateNetworkNameSuccess': '更新网络名称成功',
	'services.flynet.updateNetworkNameFailed': '更新网络名称失败，请稍后重试',
	'services.flynet.updateRdpSettingsSuccess': '更新远程桌面设置成功',
	'services.flynet.updateRdpSettingsFailed': '更新远程桌面设置失败，请稍后重试',
	'services.flynet.updateSuccess': '更新成功',
	'services.flynet.updateFailed': '更新失败，请稍后重试',

	// 服务层 - Device 服务
	'services.device.enable': '启用',
	'services.device.disable': '禁用',
	'services.device.keyExpireSuccess': '密钥过期成功',
	'services.device.keyExpireFailed': '密钥过期失败，请稍后重试',
	'services.device.meshModeSuccess': 'Mesh模式成功',
	'services.device.meshModeFailed': 'Mesh模式失败，请稍后重试',
	'services.device.fetchDeviceListFailed': '获取设备列表失败，请稍后重试',
	'services.device.queryEmptyData': '查询异常，返回空数据',
	'services.device.fetchDeviceFailed': '获取设备失败，请稍后重试',
	'services.device.approveDeviceSuccess': '审批设备成功',
	'services.device.approveDeviceFailed': '审批设备失败，请稍后重试',
	'services.device.updateRdpSettingsSuccess': '修改远程桌面设置成功',
	'services.device.updateRdpSettingsFailed': '修改远程桌面设置失败，请稍后重试',

	// 服务层 - User 服务
	'services.user.fetchUserFormFailed': '获取用户表单失败',

	// 服务层 - File 服务
	'services.file.uploadSuccess': '上传成功',
	'services.file.uploadFailed': '文件上传失败',

	// 下载页面
	'download.title': '下载{name}',
	'download.description': '安装应用并登录以开始使用{name}',

	// 引导页面
	'guide.title': '选择您的用途',
	'guide.description': '根据您的用途，我们将为您提供不同的操作引导。',
	'guide.mesh.title': 'Mesh网络',
	'guide.mesh.content': '组建您的私有网络，把位于不同物理位置的设备联接成一个互通的虚拟逻辑网络。',
	'guide.vpn.title': '零信任组网接入',
	'guide.vpn.content': '取代传统VPN的全新零信任访问模式，通过严格的身份认证和授权校验来保护资源。',

	// 日志推送配置详情
	'logs.pushConfig.detail.loadFailed': '获取推送配置详情失败',

	// DNS日志页面
	'logs.dnsLog.description': 'DNS日志记录了用户设备的域名解析记录',
	'logs.dnsLog.switch.label': '开启DNS日志',
	'logs.dnsLog.banner.title': '未开启DNS日志',
	'logs.dnsLog.banner.content': '请前往 {link} 处 开启 后再查看日志',
	'logs.dnsLog.banner.linkText': '设置/数据设置/日志',
	'logs.dnsLog.filter.user': '用户',
	'logs.dnsLog.filter.device': '设备',
	'logs.dnsLog.filter.type': '类型',
	'logs.dnsLog.filter.domain': '域名',
	'logs.dnsLog.filter.all': '全部',
	'logs.dnsLog.button.query': '查询',
	'logs.dnsLog.tooltip.addToFilter': '添加到搜索条件',
	'logs.dnsLog.tooltip.removeFromFilter': '从搜索条件中移除',

	// 网络流量日志页面
	'logs.networkFlowLog.description': '网络日志记录了用户对网络的访问行为，包括源IP地址、目标IP地址、协议、访问时间、访问的设备信息',
	'logs.networkFlowLog.switch.label': '开启网络日志',
	'logs.networkFlowLog.banner.title': '未开启网络日志',
	'logs.networkFlowLog.banner.content': '请前往 {link} 处开启 后再查看日志',
	'logs.networkFlowLog.banner.linkText': '设置/数据设置/日志',
	'logs.networkFlowLog.filter.user': '用户',
	'logs.networkFlowLog.filter.device': '设备',
	'logs.networkFlowLog.filter.protocol': '协议类型',
	'logs.networkFlowLog.filter.sourceIp': '源IP',
	'logs.networkFlowLog.filter.targetIp': '目标IP',
	'logs.networkFlowLog.filter.all': '全部',
	'logs.networkFlowLog.button.query': '查询',
	'logs.networkFlowLog.tooltip.addToFilter': '添加到搜索条件',
	'logs.networkFlowLog.tooltip.removeFromFilter': '从搜索条件中移除',
	'logs.networkFlowLog.traffic.exit': '出口流量',
	'logs.networkFlowLog.traffic.subnet': '子网流量',
	'logs.networkFlowLog.traffic.virtual': '虚拟流量',
	'logs.networkFlowLog.traffic.physical': '物理流量',

	// 服务日志页面
	'logs.servicesLog.description': '服务日志记录了用户对服务的访问记录',
	'logs.servicesLog.filter.user': '用户',
	'logs.servicesLog.filter.device': '设备',
	'logs.servicesLog.filter.keywords': '关键字',
	'logs.servicesLog.filter.service': '服务',
	'logs.servicesLog.filter.servicePlaceholder': '请选择服务',
	'logs.servicesLog.button.query': '查询',
	'logs.servicesLog.tooltip.addToFilter': '添加到搜索条件',
	'logs.servicesLog.tooltip.removeFromFilter': '从搜索条件中删除',

	// 审计日志页面
	'logs.auditLog.description': '检查过去 90 天内对网络配置设置所做的更改日志',
	'logs.auditLog.banner.title': '正在寻找网络流量日志？',
	'logs.auditLog.banner.description': '我们不会记录任何有关网络流量的信息，因为它可能包含一些敏感信息。',
	'logs.auditLog.filter.timeRange': '时间范围',
	'logs.auditLog.filter.keywords': '关键字',
	'logs.auditLog.filter.action': '操作',
	'logs.auditLog.filter.actionPlaceholder': '请选择操作',
	'logs.auditLog.filter.target': '目标',
	'logs.auditLog.button.query': '查询',
	'logs.auditLog.tooltip.addToFilter': '添加到搜索条件',
	'logs.auditLog.tooltip.removeFromFilter': '从搜索条件中移除',
	'services.application.delete.title': '删除应用 {name}',
	'services.application.delete.description': '应用 {name} {description} 将被删除，删除后将无法使用该应用。',
	'services.application.delete.confirmText': '输入 {name} 以确认删除',

	// 应用程序开关
	'services.application.switch.title': '开启应用面板',
	'services.application.switch.description': '开启后将在用户信息里显示应用面板，并且可以通过控制台配置应用面板。',
	'services.application.switch.applicationPanel': '应用面板',
	'services.application.edit.title': '编辑应用',
	'services.application.edit.form.description': '描述',
	'services.application.edit.form.descriptionPlaceholder': '请输入描述',
	'services.application.new.title': '新建应用',
	'services.application.groupSort.title': '分类管理',
	'services.application.groupSort.button.moveToTop': '移到最前面',
	'services.application.groupSort.button.moveUp': '上移',
	'services.application.groupSort.button.moveDown': '下移',
	'services.application.groupSort.button.moveToBottom': '移到最后面',
	'services.application.groupSort.confirmDelete': '确定是否要删除此分类？',
	'services.application.groupSort.sortWarning': '提示：点击确定按钮后排序才能生效',
	'services.application.import.validation.nameRequired': '名称不能为空',
	'services.application.import.validation.urlRequired': 'URL不能为空',
	'services.application.import.validation.categoryRequired': '分类不能为空',
	'services.application.import.validation.iconRequired': '图标不能为空',
	'services.application.import.validation.iconTypeError': '图标类型错误,应为 0: URL 1: Name',
	'services.application.import.validation.duplicateUrl': '地址信息重复',

	// 服务主页面
	'services.index.title': '服务列表',
	'services.index.button.dataImport': '数据导入',
	'services.index.button.application': '应用',
	'services.index.button.serviceGroup': '服务组',
	'services.index.button.newService': '新建服务',
	'services.index.description': '管理设备上运行的服务',
	'services.index.totalServices': '服务总数',
	'services.index.endMessage': '---- 到底了 ----',

	// 服务详情
	'services.detail.error.loadDetailFailed': '获取服务详情失败,请稍后重试',
	'services.detail.nodes': '节点',
	'services.detail.subnetNodes': '子网节点',

	// 服务组操作
	'services.group.create.success': '创建服务组成功',
	'services.group.create.failed': '创建服务组失败',
	'services.group.edit.success': '编辑服务组成功',
	'services.group.edit.failed': '编辑服务组失败',
	'services.group.delete.success': '删除服务组成功',
	'services.group.delete.failed': '删除服务组失败',

	// 服务编辑
	'services.edit.error.loadServiceFailed': '获取服务信息失败，请稍后重试',
	'services.edit.error.selectDevice': '请选择设备',
	'services.edit.error.addNodes': '请添加节点',
	'services.edit.error.addConnector': '请添加连接器',
	'services.edit.error.addSubnetNodes': '请添加子网节点',
	'services.edit.success.updateService': '更新服务成功',
	'services.edit.error.updateServiceFailed': '更新服务失败，请稍后重试',
	'services.edit.mode.direct': '直连模式',
	'services.edit.mode.forward': '路由模式',

	// 错误消息
	'devices.error.getDeviceListFailed': '获取设备列表失败, 请稍后重试',
	'devices.error.getUserListFailed': '获取用户列表失败, 请稍后重试',

	// 设备组表格
	'devices.group.table.name': '名称',
	'devices.group.table.description': '备注',
	'devices.group.table.type': '类型',
	'devices.group.table.deviceCount': '设备数',
	'devices.group.table.createdTime': '创建时间',
	'devices.group.type.static': '静态设备组',
	'devices.group.type.dynamic': '动态设备组',

	'devices.batchEditRoute.title': '批量编辑设备{deviceName}的子网路由',
	'devices.batchEditRoute.description': '每行一个子网路由, 每行三个字段:是否立即启用,子网网段(CIDR格式),描述信息。',
	'devices.batchEditRoute.example': '例如:true, **********/24, 办公一区',
	'devices.batchEditRoute.warning': '修改后可能造成部分网络不可访问，请谨慎操作。',
	'devices.batchEditRoute.advertisedRoutes': '宣告路由',
	'devices.batchEditRoute.excludeRoutes': '排除路由',
	'devices.batchEditRoute.updateSuccess': '批量编辑子网路由成功',
	'devices.batchEditRoute.updateFailed': '批量编辑子网路由失败，请稍后重试',

	'devices.rangeAccept.title': '设置服务接收范围',
	'devices.rangeAccept.serviceGroups': '服务组',
	'devices.rangeAccept.networkServices': '网络服务',
	'devices.rangeAccept.acceptServices': '接收服务',
	'devices.rangeAccept.excludeServices': '排除服务',
	'devices.rangeAccept.getServicesFailed': '获取网络服务失败',

	'devices.editRejectRoute.title': '修改设备{deviceName}的拒绝接受路由',
	'devices.editRejectRoute.description': '设备的拒绝接受路由设置后,将不再接受宣告路由设置里的路由宣告',
	'devices.editRejectRoute.placeholder': '请输入拒绝接受路由, 格式为CIDR(例如:**********/24), 可输入多个, 以逗号分隔',
	'devices.editRejectRoute.updateSuccess': '修改拒绝接受路由成功',
	'devices.editRejectRoute.updateFailed': '修改拒绝接受路由失败，请稍后重试',

	'logs.switch.title': '日志采集',
	'logs.switch.subtitle': '管理是否启用相关日志的采集',
	'logs.destination.title': '推送目标配置',
	'logs.destination.subtitle': '推送目标配置',
	'logs.destination.new': '新建配置',
	'logs.destination.name': '名称',
	'logs.destination.type': '目标类型',
	'logs.destination.status': '状态',
	'logs.destination.enabled': '启用',
	'logs.destination.disabled': '禁用',
	'logs.destination.edit': '编辑',
	'logs.destination.delete': '删除',
	'logs.destination.createTitle': '新建推送目标配置',
	'logs.destination.createSuccess': '新建推送目标成功',
	'logs.destination.createFailed': '新建推送目标失败',
	'logs.destination.editTitle': '编辑推送目标配置',
	'logs.destination.editSuccess': '编辑推送目标成功',
	'logs.destination.editFailed': '编辑推送目标失败',
	'logs.destination.deleteTitle': '删除推送目标配置',
	'logs.destination.deleteConfirm': '推送目标配置将被删除，删除后将无法恢复，确认删除吗？',
	'logs.destination.deleteInput': '输入 {name} 以确认删除',
	'logs.destination.deleteSuccess': '删除推送目标成功',
	'logs.destination.deleteFailed': '删除推送目标失败',
	'logs.destination.nameRequired': '名称不能为空',
	'logs.destination.code': '编码',
	'logs.destination.codeHint': '新建完成后不可修改',
	'logs.destination.codeRequired': '编码不能为空',
	'logs.destination.codeStartError': '编码不能以-开头',
	'logs.destination.codeFormatError': '编码只能包含字母、数字和\'-\'',
	'logs.destination.remark': '备注',
	'fieldGroups.title': '字段组',
	'fieldGroups.noFields': '暂无字段',
	'fieldGroups.key': '键',
	'fieldGroups.name': '名称',
	'fieldGroups.type': '类型',
	'fieldGroups.default': '默认值',
	'fieldGroups.placeholder': '占位符',
	'fieldGroups.extra': '扩展信息',
	'fieldGroups.rules': '校验规则',
	'fieldGroups.addTitle': '新建字段组',
	'fieldGroups.editTitle': '编辑字段组',
	'fieldGroups.inputName': '请输入名称',
	'fieldGroups.inputLabel': '请输入标签',
	'fieldGroups.disabled': '禁用',
	'fieldGroups.enabled': '启用',
	'fieldGroups.fieldList': '字段列表',
	'fieldGroups.addField': '新增字段',
	'fieldGroups.fieldName': '字段名',
	'fieldGroups.inputFieldName': '请输入字段名',
	'fieldGroups.label': '标签',
	'fieldGroups.selectType': '请选择类型',
	'fieldGroups.input': '输入框',
	'fieldGroups.select': '下拉选择',
	'fieldGroups.radio': '单项选择',
	'fieldGroups.checkbox': '多项选择',
	'fieldGroups.inputNumber': '数字输入框',
	'fieldGroups.switch': '开关',
	'fieldGroups.date': '日期选择',
	'fieldGroups.time': '时间选择',
	'fieldGroups.datetime': '日期时间选择',
	'fieldGroups.textarea': '多行文本框',
	'fieldGroups.keyvalues': '键值对',
	'fieldGroups.inputDefault': '请输入默认值',
	'fieldGroups.inputPlaceholder': '请输入占位符',
	'fieldGroups.inputExtra': '请输入扩展信息',
	'fieldGroups.readonly': '只读',
	'fieldGroups.hidden': '隐藏',
	'fieldGroups.preview': '预览',
	'fieldGroups.list': '列表',
	'fieldGroups.optionListTitle': '字段选项列表（下拉列表，单选，多选等使用）',
	'fieldGroups.addOption': '添加选项',
	'fieldGroups.optionLabel': '标签',
	'fieldGroups.optionValue': '值',
	'fieldGroups.ruleListTitle': '字段规则列表',
	'fieldGroups.addRule': '添加规则',
	'fieldGroups.required': '必填',
	'fieldGroups.whitespace': '允许空格',
	'fieldGroups.format': '格式',
	'fieldGroups.pattern': '正则',
	'fieldGroups.minLength': '最小长度',
	'fieldGroups.maxLength': '最大长度',
	'fieldGroups.formatEmail': '电子邮件',
	'fieldGroups.formatPhone': '电话号码',
	'fieldGroups.formatIdCard': '身份证',
	'fieldGroups.formatNumber': '数字',
	'fieldGroups.formatInteger': '整数',
	'fieldGroups.formatFloat': '浮点数',
	'fieldGroups.formatArray': 'JSON数组',
	'logs.event.title': '推送事件配置',
	'logs.event.new': '新建配置',
	'logs.event.subtitle': '推送事件配置',
	'logs.event.name': '事件名称',
	'logs.event.type': '事件类型',
	'logs.event.type.audit': '配置日志',
	'logs.event.type.network': '网络日志',
	'logs.event.type.dns': 'DNS日志',
	'logs.event.type.service': '服务日志',
	'logs.event.type.device': '设备日志',
	'logs.event.format': '日志格式',
	'logs.event.status': '状态',
	'logs.event.enabled': '启用',
	'logs.event.disabled': '禁用',
	'logs.event.edit': '编辑',
	'logs.event.delete': '删除',
	'logs.event.createTitle': '新建订阅事件配置',
	'logs.event.editTitle': '编辑订阅事件配置',
	'logs.event.ok': '确定',
	'logs.event.cancel': '取消',
	'logs.event.nameRequired': '名称不能为空',
	'logs.event.remark': '备注',
	'logs.event.code': '编码',
	'logs.event.codeHint': '新建完成后不可修改',
	'logs.event.codeRequired': '编码不能为空',
	'logs.event.codeStartError': '编码不能以-开头',
	'logs.event.codeFormatError': '编码只能包含字母、数字和\'-\'',
	'logs.event.logType': '日志类型',
	'logs.event.createSuccess': '新建成功',
	'logs.event.createFailed': '新建失败',
	'logs.event.deleteTitle': '删除日志推送事件',
	'logs.event.deleteSuccess': '删除日志推送事件成功',
	'logs.event.deleteFailed': '删除日志推送事件失败，请稍后重试',
	'logs.event.deleteConfirm': '日志推送事件将被删除，删除后将无法恢复，确认删除吗？',
	'logs.event.deleteInput': '输入 {name} 以确认删除'
}
