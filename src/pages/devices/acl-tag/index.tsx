import { FC, useState, useCallback, useContext, useEffect } from 'react';
import { Typography, Modal, Card, Select, Notification } from '@douyinfe/semi-ui';
import { UserProfileContext } from '@/hooks/useUserProfile';
import { Machine } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";
import { flylayerClient } from '@/services/core';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { useLocale } from '@/locales';

const { Paragraph } = Typography;

interface Props {
    close: () => void,
    success?: () => void,
    record: Machine
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const userProfile = useContext(UserProfileContext)
    const flynet = useContext(FlynetGeneralContext);
    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);

    const [tags, setTags] = useState<Array<string>>([]);

    const [selectedTags, setSelectedTags] = useState<Array<string>>(props.record.tags || []);

    const handleOk = () => {

        setLoading(true)
        flylayerClient.tagMachine({
            machineId: props.record.id,
            tags: selectedTags
        }).then((_res) => {
            props.success && props.success();
            Notification.success({ content: formatMessage({ id: 'devices.aclTag.updateSuccess' }), position: "bottomRight" })
        }).catch((err) => {
            console.error(err);
            Notification.error({ content: formatMessage({ id: 'devices.aclTag.updateFailed' }), position: "bottomRight" })

        }).finally(() => setLoading(false))
    };
    const handleCancel = () => {
        props.close();
    };

    // 获取访问控制策略
    const getACLPolicy = useCallback(() => {
        
        flylayerClient.getACLPolicy({
            flynetId: flynet.id
        }).then(res => {
            if (res.policy) {
                
                const tags: string[] = [];
                const userMail = userProfile?.identity?.traits?.email;
                const aclPolicy = res.policy;
                if(res.policy && res.policy.tagowners ){
                    Object.keys(aclPolicy.tagowners).forEach(key => {
                        aclPolicy.tagowners[key].values.map((item) => {
                            const itemVal = item.kind.value + ''
                            if (itemVal == userMail) {
                                tags.push(key)
                            }
                        })
                    })
                }
                
                setTags(tags);

            }

        }).catch(err => {
            console.error(err);

            Notification.error({ content: formatMessage({ id: 'devices.aclTag.getPolicyFailed' }), position: "bottomRight" })
        })
    }, [])

    useEffect(() => {
        getACLPolicy();
    }, [])

    return <>
        <Modal
            width={500}
            title={`编辑设备${props.record.givenName}的访问控制标签`}
            visible={true}
            onOk={handleOk}
            onCancel={handleCancel}
            maskClosable={false}
            closeOnEsc={true}
            okButtonProps={{ loading }}
        >
            <Paragraph className='mb20'>{formatMessage({ id: 'devices.aclTag.description' })}</Paragraph>
            {selectedTags.length == 0 ? <Card className='mb20'>{formatMessage({ id: 'devices.aclTag.noTags' })}</Card> : ''}

            <Select insetLabel={formatMessage({ id: 'devices.aclTag.selectTags' })} multiple
                value={selectedTags}
                onChange={(val) => {
                    setSelectedTags(val as string[])
                }}
                className='mb20'>
                {tags.map((item, index) => {
                    return <Select.Option key={index} value={item}>{item}</Select.Option>
                })}
            </Select>

        </Modal></>
}

export default Index;