import { FC, useState } from 'react'
import { Typography, Modal, Notification, Switch, Space, Banner } from '@douyinfe/semi-ui';

import { Machine } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";
import { flylayerClient } from '@/services/core';
import { useLocale } from '@/locales';

const { Paragraph } = Typography;

interface Props {
    close: () => void,
    success?: () => void,
    record: Machine
}


const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const [saveLoading, setSaveLoading] = useState(false);

    const [meshEnabled, setMeshEnabled] = useState(props.record.meshEnabled);
    const [meshInitEnabled, _setMeshInitEnabled] = useState(props.record.meshEnabled);

    const handleOk = () => {
        setSaveLoading(true);
        flylayerClient.setMachineMeshEnabled({
            machineId: props.record.id,
            meshEnabled
        }).then(() => {
            Notification.success({ content: `修改设备${props.record.givenName}的Mesh模式成功`, position: "bottomRight" })
            props.success && props.success();
            props.close();
        }).catch(err => {
            console.error(err);
            Notification.error({ content: `修改设备${props.record.givenName}的Mesh模式失败，请稍后重试`, position: "bottomRight" })
        }).finally(() => {
            setSaveLoading(false);
        })
    };

    const handleCancel = () => {
        props.close();
    };

    return <>
        <Modal
            width={600}
            title={`修改设备${props.record.givenName}的Mesh模式`}
            visible={true}
            onOk={handleOk}
            onCancel={handleCancel}
            maskClosable={false}
            closeOnEsc={true}
            okButtonProps={{ loading: saveLoading }}
        >
            <Paragraph className='mb20' type='tertiary'>
                {formatMessage({ id: 'devices.editMesh.description' })}
            </Paragraph>
            <div>
                <Space className='mb20'>
                    <Switch

                        checked={meshEnabled}
                        size='large'
                        onChange={(checked) => setMeshEnabled(checked)}
                    ></Switch>
                    <Paragraph type='tertiary'>{formatMessage({ id: 'devices.editMesh.enableMesh' })}</Paragraph>
                </Space>
            </div>
            {!meshEnabled && meshInitEnabled &&
                <Banner className='mb20' type='warning' closeIcon={null}
                    description={formatMessage({ id: 'devices.editMesh.warning' })}
                ></Banner>
            }

        </Modal>
    </>
}

export default Index;