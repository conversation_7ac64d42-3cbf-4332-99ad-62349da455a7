import { FC, useState } from 'react'
import { Typography, Modal, TagInput, Notification } from '@douyinfe/semi-ui';

import { Machine } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";
import { flylayerClient } from '@/services/core';
import { useLocale } from '@/locales';

const { Paragraph } = Typography;

interface Props {
    close: () => void,
    success?: () => void,
    record: Machine
}


const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const [saveLoading, setSaveLoading] = useState(false);

    const [rejectRoutes, setRejectRoutes] = useState(props.record.rejectRoutes);

    const handleOk = () => {
        setSaveLoading(true);
        flylayerClient.setMachineRejectRoutes({
            machineId: props.record.id,
            routes: rejectRoutes
        }).then((_res) => {
            props.success && props.success();
            Notification.success({ content: formatMessage({ id: 'devices.editRejectRoute.updateSuccess' }), position: "bottomRight" })
        }).catch((err) => {
            console.error(err);
            Notification.error({ content: formatMessage({ id: 'devices.editRejectRoute.updateFailed' }), position: "bottomRight" })

        }).finally(() => setSaveLoading(false)
        )
    };

    const handleCancel = () => {
        props.close();
    };

    return <>
        <Modal
            width={500}
            title={`修改设备${props.record.givenName}的拒绝接受路由`}
            visible={true}
            onOk={handleOk}
            onCancel={handleCancel}
            maskClosable={false}
            closeOnEsc={true}
            okButtonProps={{ loading: saveLoading }}
        >
            <Paragraph className='mb20'>{formatMessage({ id: 'devices.editRejectRoute.description' })}</Paragraph>
            <TagInput
                value={rejectRoutes}
                onChange={(value) => setRejectRoutes(value)}
                placeholder=''
                style={{ width: '100%' }}
                addOnBlur
                className='mb10' />
                <Paragraph type='tertiary'>
                {formatMessage({ id: 'devices.editRejectRoute.placeholder' })}
                </Paragraph>
        </Modal>
    </>
}

export default Index;