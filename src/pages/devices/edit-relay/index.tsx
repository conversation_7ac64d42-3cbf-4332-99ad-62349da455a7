import { FC, useEffect, useState } from 'react'
import { Typo<PERSON>, Modal, Select, Skeleton, Notification } from '@douyinfe/semi-ui';

import { Machine } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";
import { flylayerClient } from '@/services/core';
import { getRelayList } from '@/services/device';
import { useLocale } from '@/locales';

const { Paragraph } = Typography;


interface Props {
    close: () => void,
    success?: () => void,
    record: Machine
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const [saveLoading, setSaveLoading] = useState(false);
    const [loading, setLoading] = useState(true);

    const [relayId, setRelayId] = useState<number>(props.record.relay || 0);
    const [relayList, setRelayList] = useState<Array<{id: number, name: string}>>([]);

    useEffect(() => {
        getRelayList().then(res => {
            setRelayList(res)
            setLoading(false)
        })
    }, [])

    const handleOk = () => {
        setSaveLoading(true)
        flylayerClient.setMachineRelay({
            machineId: props.record.id,
            relay: relayId
        }).then((_res) => {
            props.success && props.success();
            Notification.success({content: formatMessage({ id: 'devices.editRelay.updateSuccess' }),position: "bottomRight"})
        }).catch((err) => {
            console.error(err);
            Notification.error({content: formatMessage({ id: 'devices.editRelay.updateFailed' }),position: "bottomRight"})
            
        }).finally(() => setSaveLoading(false))
    };
    const handleCancel = () => {
        props.close();
    };
    return <>
        <Modal
            width={500}
            title={`修改设备${props.record.givenName}的中继节点`}
            visible={true}
            onOk={handleOk}
            onCancel={handleCancel}
            maskClosable={false}
            closeOnEsc={true}
            okButtonProps={{ loading: saveLoading }}
        >
            <Skeleton loading={loading} placeholder={
                <>
                    <Skeleton.Image  style={{height: 20, marginBottom: 20}}></Skeleton.Image>

                    
                    <Skeleton.Image style={{height: 40}}></Skeleton.Image>
                </>
            }>
            <Paragraph className='mb20'>{formatMessage({ id: 'devices.editRelay.description' })}</Paragraph>
            <Select size='large' style={{width: '100%'}} placeholder={formatMessage({ id: 'devices.editRelay.placeholder' })} value={relayId} onChange={val=>setRelayId(val as number)}>
                <Select.Option value={0}>{formatMessage({ id: 'devices.editRelay.auto' })}</Select.Option>
                {relayList.map(item => <Select.Option key={item.id} value={item.id}>{item.name}</Select.Option>)}
            </Select>
            </Skeleton>
        </Modal>
    </>
}

export default Index;