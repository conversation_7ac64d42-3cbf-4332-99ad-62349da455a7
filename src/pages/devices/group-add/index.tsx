import React, { useState, useContext, useEffect } from 'react'
import { Typography, Form, Modal, Row, Col, Button, Divider, Notification, Popover, Card } from '@douyinfe/semi-ui';
import { getFlynet } from '@/services/flynet';
import { useLocale } from '@/locales';
import pinyin from 'tiny-pinyin';

import styles from './index.module.scss';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { Machine, MachineGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb';
import { GroupType, DynamicGroupMode } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import { Expression } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import { flylayerClient } from '@/services/core';
import TableEmpty from '@/components/table-empty'
import { IconPlus, IconMinusCircle, IconHelpCircle, IconArrowUpRight } from '@douyinfe/semi-icons';
import MachineSelector from '@/components/machine-selector';
import CodeEditor from '@/components/code-editor';

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
const { Paragraph, Text } = Typography;
import Expressions from '@/components/expressions';
import { getRadioEntitlementVal, sanitizeLabel, validateParamCombo } from '@/utils/common';
import { BASE_PATH } from '@/constants/router';
import { AttributeTemplate } from '@/interface/attribute-template';
import { LicenseContext } from '@/hooks/useLicense';

const { Input, RadioGroup, Radio } = Form
interface Props {
    close: () => void,
    success?: (machineGroup?: MachineGroup) => void
}
const Index: React.FC<Props> = (props) => {
    const { formatMessage } = useLocale();
        const license = useContext(LicenseContext);
    const flynet = useContext(FlynetGeneralContext);
    const [formApi, SetFormApi] = useState<FormApi<{
        name: string,
        description: string,
        alias: string,
        type: GroupType,
        expressionsCombo: string,
        advancedDynamicMode: boolean,
        priority: number,
    }>>()

    const entitlementAllowDynamic = getRadioEntitlementVal('dynamic_user_group', license.entitlements);
    

    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);
    const [machines, setMachines] = useState<Machine[]>([]);


    const [expressionsTemplate, setExpressionsTemplate] = useState<AttributeTemplate>();

    const queryFlynet = async () => {
        let res = await getFlynet(flynet.id);
        if (res && res.flynet && res.flynet.attributeTemplate) {

            if (res.flynet.attributeTemplate) {
                const json = JSON.parse(res.flynet.attributeTemplate);
                console.log(json);
                const userProperties = json.properties.input.properties.Device;

                let attributeTemplate: AttributeTemplate = {
                    type: 'object',
                    title: 'properties',
                    description: 'properties',
                    properties: {
                        'input': {
                            type: 'object',
                            description: formatMessage({ id: 'devices.groupAdd.input' }),
                            title: formatMessage({ id: 'devices.groupAdd.input' }),
                            properties: {
                                Device: userProperties
                            }
                        }
                    }

                }
                setExpressionsTemplate(attributeTemplate);
            }
        }
    }
    useEffect(() => {
        queryFlynet();
    }, [])

    const [expressions, setExpressions] = useState<Array<Expression>>([]);

    const [expressionAdvanced, setExpressionAdvanced] = useState(`
package play

import future.keywords.if
    
# 请输入OPA表达式脚本
# 可参见：https://play.openpolicyagent.org/
    
default hello := false
    
hello if input.message == "world"
        
`);


    const [expressionsError, setExpressionsError] = useState(false);

    const [machineSelectorVisible, setMachineSelectorVisible] = useState(false);

    const handleSubmit = async () => {

        await formApi?.validate();

        const values = formApi?.getValues();
        if (!values) {
            return;
        }

        const name = values.name.trim();
        const description = values.description ? values.description.trim() : '';
        const alias = values.alias ? values.alias.trim() : '';
        const type = values.type;
        const advancedDynamicMode = values.advancedDynamicMode;
        const expressionsCombo = values.expressionsCombo;
        const priority = values.priority;


        if (type == GroupType.GROUP_DYNAMIC) {
            if (advancedDynamicMode) {
                if (expressionAdvanced.length == 0) {
                    setExpressionsError(true)
                    return;
                }
            } else {
                if (expressionsError || expressions.length == 0) {
                    setExpressionsError(true)
                    return;
                }
            }
        }

        setLoading(true);


        flylayerClient.createMachineGroup({
            flynetId: flynet.id,
            name: name,
            description: description,
            alias: alias,
            type: type,
            machines: machines,
            priority: priority,
            attrs: advancedDynamicMode ? {
                dynamicGroupMode: DynamicGroupMode.DYNAMIC_GROUP_ADVANCED,
                expressionAdvanced: expressionAdvanced
            } : {
                dynamicGroupMode: DynamicGroupMode.DYNAMIC_GROUP_STANDARD,
                expressions: expressions,
                expressionsCombo: expressionsCombo
            }
        }).then((_res) => {
            Notification.success({
                title: formatMessage({ id: 'devices.groupAdd.createSuccess' }),
                content: formatMessage({ id: 'devices.groupAdd.createSuccess' })
            })

            props.success && props.success();
        }).catch((err) => {
            Notification.error({
                title: formatMessage({ id: 'devices.groupAdd.createFailed' }),
                content: err.message
            })
        }).finally(() => {
            setLoading(false);
        })

    }

    return <>
        <Modal
            width={800}
            title={formatMessage({ id: 'devices.groupAdd.title' })}
            visible={true}
            onCancel={props.close}
            onOk={handleSubmit}
            okButtonProps={{ loading: loading }}
            className='semi-modal'
            maskClosable={false}
        >

            <div className={styles.addService}>
                <Form getFormApi={SetFormApi}
                    allowEmpty
                    initValues={{
                        type: GroupType.GROUP_STATIC,
                        advancedDynamicMode: false,
                        priority: 1,
                    }}
                    onValueChange={(values, changedValue) => {
                        if (changedValue.hasOwnProperty('alias')) {
                            formApi?.setValue('name', sanitizeLabel(pinyin.convertToPinyin(changedValue.alias, '', true)))
                        }
                    }}
                >
                    {({ values }) => (<>
                        <Row gutter={12}>
                            <Col span={12}>
                                <Input field='alias' label={formatMessage({ id: 'devices.groupAdd.name' })} trigger={'blur'} validate={value => {
                                    if (!value) {
                                        return formatMessage({ id: 'devices.groupAdd.nameRequired' });
                                    }
                                    return '';
                                }} />
                            </Col>
                            <Col span={12}>
                                <Input field='name'
                                    label={<>{formatMessage({ id: 'devices.groupAdd.code' })} <Popover content={<div className='p10'>{formatMessage({ id: 'devices.groupAdd.codeTooltip' })}</div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover></>}
                                    trigger={'blur'} validate={value => {
                                        if (!value) {
                                            return formatMessage({ id: 'devices.groupAdd.codeRequired' });
                                        }

                                        // 编码不能以-开头
                                        if (value.trim().startsWith('-')) {
                                            return formatMessage({ id: 'devices.groupAdd.codeCannotStartWithDash' })
                                        }

                                        if (!/^[\dA-Za-z-]+$/.test(value.trim())) {
                                            return formatMessage({ id: 'devices.groupAdd.codeInvalidFormat' });
                                        }
                                        return '';
                                    }}
                                    required />
                            </Col>
                            {/* <Col span={6}>
                                <InputNumber field="priority" min={1} max={100} step={1} label={<>优先级&nbsp;<Popover content={<div className='p10'>优先级范围为1-100，默认为1，即最高优先级。</div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover></>}></InputNumber>
                            </Col> */}
                        </Row>
                        <Row>
                            <Col span={24}>
                                <Input field='description' label={formatMessage({ id: 'devices.groupAdd.remarks' })} />
                            </Col>
                        </Row>
                        <Divider></Divider>
                        {entitlementAllowDynamic && <Row>
                            <Col span={16}>
                                <RadioGroup fieldStyle={{paddingTop:10, paddingBottom:10}} labelPosition='left' field='type' label={formatMessage({ id: 'devices.groupAdd.type' })}>
                                    <Radio value={GroupType.GROUP_STATIC}>{formatMessage({ id: 'devices.groupAdd.staticGroup' })}</Radio>
                                    <Radio value={GroupType.GROUP_DYNAMIC}>{formatMessage({ id: 'devices.groupAdd.dynamicGroup' })}</Radio>
                                </RadioGroup>
                            </Col>
                        </Row>}
                        {values.type == GroupType.GROUP_STATIC && <>
                            <Row className='mb10'>
                                <Col span={20}>
                                    <Text type='tertiary'>{formatMessage({ id: 'devices.groupAdd.devices' })}</Text>
                                </Col>
                                <Col span={4} className={styles.rightColumn}>
                                    <Button
                                        onClick={() => {
                                            setMachineSelectorVisible(true);
                                        }}
                                        icon={<IconPlus></IconPlus>}></Button>
                                </Col>
                            </Row>
                            {
                                machines.length == 0 ? <TableEmpty loading={false}></TableEmpty> :
                                    <>
                                        {machines.map((item, index) => {
                                            return <Row className="mb10" key={index}>
                                                <Col span={20}>
                                                    {item.givenName ? item.givenName : item.name}({item.ipv4})
                                                </Col>
                                                <Col span={4} className={styles.rightColumn}>
                                                    <Button
                                                        type='danger'
                                                        onClick={() => {
                                                            let newMachines = machines.filter((item, i) => i != index);
                                                            setMachines(newMachines);
                                                        }}
                                                        icon={<IconMinusCircle></IconMinusCircle>}></Button>
                                                </Col>
                                            </Row>
                                        })}
                                    </>
                            }
                        </>}
                        {values.type == GroupType.GROUP_DYNAMIC && expressionsTemplate && <>
                            {
                                values.advancedDynamicMode ? <>
                                    <CodeEditor value={expressionAdvanced} height='200px' language='systemverilog' onChange={(value) => setExpressionAdvanced(value || '')} ></CodeEditor>
                                    {expressionsError && <Paragraph type='danger'>{formatMessage({ id: 'devices.groupAdd.expressionEmpty' })}</Paragraph>}
                                </> : <>
                                    <Expressions
                                        expressions={expressions}
                                        onChange={(expressions: Array<Expression>) => {
                                            setExpressions(expressions);
                                            setExpressionsError(false);
                                        }}
                                        onError={() => {
                                            setExpressionsError(true);
                                        }}
                                        attributeTemplate={expressionsTemplate}
                                    ></Expressions>
                                    {expressionsError && <Paragraph type='danger'>{formatMessage({ id: 'devices.groupAdd.triggerParamError' })}</Paragraph>}
                                    <Row>
                                        <Col span={24}>

                                            <Input
                                                validate={ value => (validateParamCombo(value, expressions.length))}

                                                extraText={formatMessage({ id: 'devices.groupAdd.paramCombinationHelp' })}
                                                field='expressionsCombo' label={formatMessage({ id: 'devices.groupAdd.paramCombination' })} />
                                        </Col>
                                    </Row>
                                </>
                            }


                        </>}
                        {values.type == GroupType.GROUP_DYNAMIC && !expressionsTemplate && <Card>
                            <Paragraph style={{ textAlign: 'center' }}>{formatMessage({ id: 'devices.groupAdd.dynamicGroupAttributeEmpty' })}
                                <a className='link-external' target='_blank' href={`${BASE_PATH}/settings/schema`} onClick={(e) => { e.stopPropagation() }}>
                                    {formatMessage({ id: 'devices.groupAdd.settings' })}<IconArrowUpRight />
                                </a>

                            </Paragraph>

                        </Card>}
                    </>)}

                </Form>
            </div>
        </Modal>
        {
            machineSelectorVisible && <MachineSelector
                multi={true}
                value={machines}
                onChange={(value: Machine | Machine[]) => {
                    setMachineSelectorVisible(false)

                    let newMachines = machines.filter(() => true);
                    if (value instanceof Array) {
                        value.forEach((item) => {
                            if (!newMachines.some(u => u.id == item.id)) {
                                newMachines.push(item);
                            }
                        })
                    } else {
                        if (!newMachines.some(u => u.id == value.id)) {
                            newMachines.push(value);
                        }
                    }
                    setMachines(newMachines);
                }}
                close={() => setMachineSelectorVisible(false)}
            ></MachineSelector>
        }
    </>
}

export default Index;
