import React, { useState, useEffect, useContext } from 'react'
import { Typography, Table, Row, Col, Button, Space, Tag, Spin, BackTop } from '@douyinfe/semi-ui';
import { ACLPolicy } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
import { useLocale } from '@/locales';
import { flylayerClient } from '@/services/core';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';

import useTable, { DeviceFilter } from './useTable';
import Deploy from './deploy';
import Del from './del';
import Expiry from './expiry';
import EditRoute from './edit-route';
import EditRouteNew from './edit-route-new';
import Rename from './rename';
import EditRelay from './edit-relay';
import AclTag from './acl-tag';
import EditGroup from './edit-group';
import EditRejectRoute from './edit-reject-route';
import EditMeshEnabled from './edit-mesh-enabled';
import RangeAccept from './range-accept';
import { Location, useLocation, useNavigate } from 'react-router-dom';
import InfiniteScroll from 'react-infinite-scroll-component';
import { getQueryParam } from '@/utils/query';

import SearchFilter from '@/components/search-filter';
import qs from 'query-string';

import Tagowners from '@/pages/policies/tagowners';
import { Machine } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";

import TableEmpty from '@/components/table-empty';
import { BASE_PATH } from '@/constants/router';
const { Title, Paragraph } = Typography;


// 根据URL参数设置过滤参数
const getDeviceFilter = (location: Location): DeviceFilter => {
    const keywords: string = getQueryParam('keywords', location) as string;
    const osQuery = getQueryParam('os', location) as string;
    const connectStatus: string = getQueryParam('connectStatus', location) as string;
    const groups: string = getQueryParam('groups', location) as string;
    const meshEnabled: string = getQueryParam('meshEnabled', location) as string;

    return {
        keywords: keywords || '',
        os: osQuery || '',
        connectStatus: connectStatus == 'online' || connectStatus == 'offline' ? connectStatus : '',
        groups: groups || '',
        meshEnabled: meshEnabled == 'enable' || meshEnabled == 'disable' ? meshEnabled : ''
    }
}
const Index: React.FC = () => {
    const { formatMessage } = useLocale();
    const location = useLocation();
    const navigate = useNavigate();

    const flynet = useContext(FlynetGeneralContext);

    // 访问控制策略
    const [aclPolicy, setACLPolicy] = useState<ACLPolicy>();
    // 过滤参数
    const initFilter: DeviceFilter = getDeviceFilter(location);

    // 过滤参数改变时跳转路由
    const doNavigate = (param: DeviceFilter) => {

        let query = '';
        if (param.keywords || param.os.length > 0 || param.connectStatus || param.groups || param.meshEnabled) {
            query = qs.stringify(param, {
                skipEmptyString: true
            });
        }
        if (query) {
            navigate(`${BASE_PATH}/devices?${query}`)
        } else {
            navigate(`${BASE_PATH}/devices`)
        }
    }

    // 查询参数
    const [search, setSearch] = useState<string>('');
    useEffect(() => {
        // 查询参数从有值变化为无值时，重新加载数据
        if (location.search == '' && search != '') {
            setFilter(initFilter);
        }
        setSearch(location.search);
    }, [location])

    useEffect(() => {
        flylayerClient.getACLPolicy({
            flynetId: flynet.id
        }).then((res) => {
            setACLPolicy(res.policy)
        });
    }, []);
    // 标签弹出框是否可见
    const [tagownersVisible, setTagownersVisible] = useState(false);

    // 是否显示部署弹框
    const [deployVisible, setDeployVisible] = useState(false);
    // 设备列表HOOKS
    const { columns, devices, loading, curDevice, setCurDevice, delVisible, setExpiryVisible, expiryVisible, setDelVisible, renameVisible, setRenameVisible, editRouteVisible, setEditRouteVisible, editAclTagVisible, setEditAclTagVisible, setReloadFlag, filter, setFilter, page: _page, total, addPage, pageSize, handleSort,
        groups: _groups,
        // curGroup,
        // setCurGroup,
        // handleGroupChange,

        removeFromGroupVisible: _removeFromGroupVisible,
        setRemoveFromGroupVisible: _setRemoveFromGroupVisible,
        editGroupVisible,
        setEditGroupVisible,
        editRelayVisiable,
        setEditRelayVisiable,
        editRejectRouteVisible,
        setEditRejectRouteVisible,
        editMeshEnabledVisible,
        setEditMeshEnabledVisible,
        editRouteNewVisible,
        setEditRouteNewVisible,
        filterParams,
        setFilterParams,
        handleFilterChange,
        rangeAcceptVisible,
        setRangeAcceptVisible,
    } = useTable(initFilter);



    return <><div className='general-page'>
        <Row className='mb10'>
            <Col span={20}>
                <Title heading={3}>{formatMessage({ id: 'devices.title' })}</Title>
            </Col>
            <Col span={4}><div className='btn-right-col'>
                <Space>
                    <Button
                        onClick={() => navigate(`${BASE_PATH}/devices/export/`)}
                    >{formatMessage({ id: 'devices.dataExport' })}</Button>
                    <Button
                        onClick={() => navigate(`${BASE_PATH}/devices/import/`)}
                    >{formatMessage({ id: 'devices.dataImport' })}</Button>
                    <Button
                        onClick={() => navigate(`${BASE_PATH}/devices/group/`)}>{formatMessage({ id: 'devices.deviceGroup' })}</Button>
                    <Button onClick={() => setTagownersVisible(true)}>{formatMessage({ id: 'devices.tags' })}</Button>
                    <Button theme='solid'
                        onClick={() => setDeployVisible(true)}>{formatMessage({ id: 'devices.deploy' })}</Button>
                </Space>
            </div></Col>
        </Row>
        <Paragraph type='tertiary' className='mb40'>{formatMessage({ id: 'devices.description' })}</Paragraph>

        <SearchFilter onChange={(val: string, filterParam) => {
            handleFilterChange({ ...filter, [filterParam.name]: val })
            doNavigate({ ...filter, [filterParam.name]: val });

            const newFilterParams = filterParams.map((item) => {
                if (item.name == filterParam.name) {
                    item.value = val;
                }
                return item;
            })
            setFilterParams(newFilterParams);
        }} filterParams={filterParams} className='mb20 search-bar'></SearchFilter>

        <div style={{ height: 20 }} className='mb10' >  <Tag>  {formatMessage({ id: 'devices.totalCount' })} {loading ? <Spin size='small' /> : total}</Tag> </div>
        <InfiniteScroll
            dataLength={devices.length} //This is important field to render the next data
            next={addPage}
            hasMore={devices.length < total}
            loader={<div><Spin></Spin></div>}
            endMessage={
                <div style={{ textAlign: 'center', paddingTop: 16, paddingBottom: 16 }}>
                    {devices.length > pageSize && <Paragraph type='tertiary'>{formatMessage({ id: 'devices.endMessage' })}</Paragraph>}
                </div>
            }
        >
            <Table
                rowKey={(record?: Machine) => record ? record.id + '' : ''}
                onChange={handleSort}
                empty={<TableEmpty loading={loading} />} loading={loading} columns={columns} dataSource={devices} pagination={false} />
        </InfiniteScroll>
        <BackTop style={{ right: 10 }} />
    </div>
        {deployVisible ?
            <Deploy close={() => { setDeployVisible(false) }} /> : null}
        {delVisible && curDevice ?
            <Del record={curDevice} success={() => {
                setDelVisible(false)
                setCurDevice(undefined)
                setReloadFlag(true)
            }} close={() => { setDelVisible(false) }} /> : null}
        {expiryVisible && curDevice ?
            <Expiry record={curDevice} success={() => {
                setExpiryVisible(false)
                setCurDevice(undefined)
                setReloadFlag(true)
            }} close={() => { setExpiryVisible(false) }} /> : null}
        {editRouteVisible && curDevice ?
            <EditRoute record={curDevice} success={() => { setReloadFlag(true) }} close={() => { setEditRouteVisible(false) }} /> : null}
        {renameVisible && curDevice ?
            <Rename record={curDevice}
                success={() => {
                    setRenameVisible(false);
                    setCurDevice(undefined)
                    setReloadFlag(true)
                }}
                close={() => { setRenameVisible(false); setCurDevice(undefined) }} /> : null}
        {editAclTagVisible && curDevice ?
            <AclTag
                success={() => {
                    setEditAclTagVisible(false);
                    setCurDevice(undefined)
                    setReloadFlag(true)
                }}
                record={curDevice} close={() => { setEditAclTagVisible(false); setCurDevice(undefined) }}></AclTag>
            : null}

        {curDevice && editGroupVisible && <EditGroup
            close={() => {
                setCurDevice(undefined)
                setEditGroupVisible(false)
            }}
            record={curDevice}
            success={() => {
                setEditGroupVisible(false)
                setReloadFlag(true)
            }}
        ></EditGroup>
        }
        {curDevice && editRelayVisiable && <EditRelay
            close={() => {
                setCurDevice(undefined)
                setEditRelayVisiable(false)
            }}
            record={curDevice}
            success={() => {
                setEditRelayVisiable(false)
                setCurDevice(undefined)
                setReloadFlag(true)
            }}
        ></EditRelay>}
        {curDevice && editRejectRouteVisible && <EditRejectRoute
            close={() => {
                setCurDevice(undefined)
                setEditRejectRouteVisible(false)
            }}
            record={curDevice}
            success={() => {
                setEditRejectRouteVisible(false)
                setCurDevice(undefined)
                setReloadFlag(true)
            }}
        ></EditRejectRoute>}
        {curDevice && editMeshEnabledVisible && <EditMeshEnabled
            close={() => {
                setCurDevice(undefined)
                setEditMeshEnabledVisible(false)
            }}
            record={curDevice}
            success={() => {
                setEditMeshEnabledVisible(false)
                setCurDevice(undefined)
                setReloadFlag(true)
            }}
        ></EditMeshEnabled>}
        {curDevice && editRouteNewVisible && <EditRouteNew
            close={() => {
                setCurDevice(undefined)
                setEditRouteNewVisible(false)
            }}
            record={curDevice}
            success={() => {
                setEditRouteNewVisible(false)
                setCurDevice(undefined)
                setReloadFlag(true)
            }}
        ></EditRouteNew>}
        {curDevice && rangeAcceptVisible && <RangeAccept
            close={() => {
                setCurDevice(undefined)
                setRangeAcceptVisible(false)
            }}
            record={curDevice}
            success={() => {
                setRangeAcceptVisible(false)
                setCurDevice(undefined)
                setReloadFlag(true)
            }}
        ></RangeAccept>}

        {tagownersVisible && aclPolicy && <Tagowners
            aclPolicy={aclPolicy}
            onChange={(policy) => setACLPolicy(policy)}
            close={() => {
                setTagownersVisible(false);
            }}
        ></Tagowners>}
    </>
}

export default Index;
