import { useEffect, useState, useContext } from 'react';
import { getFlynet } from '@/services/flynet';
import { flylayerClient } from '@/services/core';
import { useLocale } from '@/locales';
import { GroupType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';

import { Typography, Tag, List, Badge, Popover, Divider, Notification, Button, Dropdown, Tooltip, Space } from '@douyinfe/semi-ui';
import { IconMore, IconGlobeStroke, IconArrowUpRight } from '@douyinfe/semi-icons';


import { compare } from 'semver'

import { Timestamp } from "@bufbuild/protobuf";

import { Machine, MachineGroup } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";

import { BASE_PATH } from '@/constants/router';
import { formatIPNVersion, formatDefaultTimestamp } from '@/utils/format';
import styles from './index.module.scss';
import { Flynet } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/flynets_pb";

import DeviceTag from '@/components/device-tag';
import { setMachineKeyExpiry, listMachines, SaveMachineRdpSettings } from '@/services/device';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { useNavigate } from 'react-router-dom';
import Copyable from '@douyinfe/semi-ui/lib/es/typography/copyable';
import { LOCAL_GUIDE_FLAG, getLocalStorage, setLocalStorage } from '@/utils/storage';
import { caseInsensitiveIncludes } from '@/utils/common';
import { VITE_USE_DEVELOP_FEATURE } from '@/utils/service';
const { Title, Paragraph, Text } = Typography;

export type DeviceFilter = {
    keywords: string,
    os: Array<string>,
    connectStatus: 'online' | 'offline' | '',
    group: string,
    meshEnabled: 'enable' | 'disable' | ''
}

const useTable = (initFilter: DeviceFilter) => {
    const { formatMessage } = useLocale();
    const flynetGeneral = useContext(FlynetGeneralContext);
    const [flynet, setFlynet] = useState<Flynet>();

    const navigate = useNavigate();
    // 强制过期弹出框是否可见
    const [expiryVisible, setExpiryVisible] = useState(false);
    // 删除设备弹出框是否可见
    const [delVisible, setDelVisible] = useState(false);
    // 重命名设备弹出框是否可见
    const [renameVisible, setRenameVisible] = useState(false);
    // 编辑中继弹出框是否可见
    const [editRelayVisiable, setEditRelayVisiable] = useState(false);
    // 编辑路由设置弹出框是否可见
    const [editRouteVisible, setEditRouteVisible] = useState(false);

    const [editRouteNewVisible, setEditRouteNewVisible] = useState(false);

    // 设置拒绝接受路由弹出框是否可见
    const [editRejectRouteVisible, setEditRejectRouteVisible] = useState(false);
    // 编辑Mesh模式弹出框是否可见
    const [editMeshEnabledVisible, setEditMeshEnabledVisible] = useState(false);
    // 编辑访问控制标签弹出框是否可见
    const [editAclTagVisible, setEditAclTagVisible] = useState(false);
    // 设备是否正在加载中
    const [loading, setLoading] = useState(true);
    // 设备列表
    const [devices, setDevices] = useState<Machine[]>([]);
    // 设备列表
    const [allDevices, setAllDevices] = useState<Machine[]>([]);

    const [originData, setOriginData] = useState<Array<Machine>>([]);
    // 当前页码
    const [page, setPage] = useState(1);

    const pageSize = 20;

    const [_sortOrder, setSortOrder] = useState<'ascend' | 'descend' | undefined>(undefined);
    const [_sortField, setSortField] = useState<string>('');


    // 服务组列表
    const [groups, setGroups] = useState<MachineGroup[]>([]);
    // 当前选中服务组名称
    const [curGroup, setCurGroup] = useState<MachineGroup>();

    // 过滤后总数据条数
    const [total, setTotal] = useState(0);
    // 当前菜单选中设备
    const [curDevice, setCurDevice] = useState<Machine>();
    // 从设备组中删除弹出框是否可见
    const [removeFromGroupVisible, setRemoveFromGroupVisible] = useState(false);
    // 编辑设备组弹出框是否可见
    const [editGroupVisible, setEditGroupVisible] = useState(false);
    // 服务接收范围设置是否可见
    const [rangeAcceptVisible, setRangeAcceptVisible] = useState(false);
    // 是否重新加载数据
    const [reloadFlag, setReloadFlag] = useState(false);
    // 过滤参数
    const [filterParam, setFilterParam] = useState<DeviceFilter>(initFilter);

    useEffect(() => {
        getFlynet(flynetGeneral.id).then(res => {
            setFlynet(res.flynet)
        })
    }, [])
    const columns = [
        {
            // width: 400,
            title: formatMessage({ id: 'devices.table.device' }),
            dataIndex: 'name',
            sorter: true,
            render: (_field: string, record: Machine, _index: number) => {
                // 设备名称
                const name = record.autoGeneratedName ? record.name : record.givenName
                return (
                    <div>
                        <Title heading={5}>
                            <a onClick={() => setTimeout(() => navigate(`${BASE_PATH}/devices/${record.ipv4}`), 10)}>{name}
                                <Text size='small' type='tertiary'>&nbsp;{record.description}</Text>
                            </a>&nbsp;
                            {record.connected ?
                                <span className='mobile-visible'><Badge dot style={{ backgroundColor: 'var(--semi-color-success)' }} /></span> :
                                <span className='mobile-visible'><Badge dot type='tertiary' /></span>}
                        </Title>
                        {record.tags && record.tags.length > 0 ? <div>
                            {record.tags.map((tag, index) => {
                                return <Tag key={index} size='large' shape='circle' color='white' style={{ marginRight: 4, marginTop: 5, marginBottom: 5 }}>{tag}</Tag>
                            })}
                        </div> : <Paragraph>{record.user?.displayName} <Text type="tertiary" className={styles.loginName} size='small' ellipsis={{ showTooltip: true }}>{record.user?.loginName}</Text></Paragraph>
                        }
                        <DeviceTag record={record}></DeviceTag>
                    </div>
                );
            },
        },
        {
            title: formatMessage({ id: 'devices.table.deviceGroup' }),
            width: 300,
            dataIndex: 'machineGroups',
            render: (_field: string, record: Machine) => {
                return <div><Space style={{ flexWrap: 'wrap' }}>{record.machineGroups.map((g, i) => <Tag key={i} size='large'>{g.alias} ({g.name})</Tag>)}</Space></div>

            }
        },
        {
            width: 230,
            title: formatMessage({ id: 'devices.table.ip' }),
            dataIndex: 'ipV4',
            sorter: true,
            render: (_field: string, record: Machine) => {
                return (<>
                    <div className={styles.ipCopyable} style={{ display: 'flex', alignItems: 'center', marginBottom: 10 }}>

                        <Popover content={<List
                            bordered>
                            {[record.ipv4, record.ipv6].map((val, index) => {
                                return <List.Item key={index}>
                                    <Paragraph copyable>
                                        {val}</Paragraph>
                                </List.Item>
                            })}

                        </List>
                        }>
                            <Text className={styles.ipLine}>{record.ipv4}</Text>
                        </Popover>
                        <Copyable style={{ lineHeight: 1 }} content={record.ipv4}></Copyable>

                    </div>
                    {record.meshEnabled ?
                        <Tooltip content={formatMessage({ id: 'devices.table.meshEnabledTooltip' })}>

                            <Tag
                                color='green'
                                size='large'
                                shape='circle'
                                prefixIcon={<IconGlobeStroke />}
                            >{formatMessage({ id: 'devices.table.meshEnabled' })}</Tag></Tooltip> :
                        <Tooltip
                            content={formatMessage({ id: 'devices.table.meshDisabledTooltip' })}
                        >
                            <Tag
                                size='large'
                                shape='circle'
                                prefixIcon={<IconGlobeStroke />}>{formatMessage({ id: 'devices.table.meshDisabled' })}</Tag>
                        </Tooltip>}

                </>

                );
            },
        },
        {
            width: 200,
            title: formatMessage({ id: 'devices.table.version' }),
            dataIndex: 'os',
            sorter: true,
            render: (field: string, record: Machine) => {
                return (
                    <div className='layout-left-icon'>
                        <div>
                            <Paragraph>{formatIPNVersion(record.clientVersion)}</Paragraph>
                            <Paragraph>{field}</Paragraph>
                        </div>
                    </div>
                );
            },
        },
        {
            width: 200,
            title: formatMessage({ id: 'devices.table.lastOnlineTime' }),
            dataIndex: 'lastSeen',
            sorter: true,
            render: (_field: Timestamp, record: Machine) => {
                if (record.connected) {
                    return <span><Badge dot style={{ backgroundColor: 'var(--semi-color-success)' }} /> {formatMessage({ id: 'devices.table.online' })}</span>
                }

                if (record.lastSeen) {
                    return <><span><Badge dot type='tertiary' /> {formatDefaultTimestamp(record.lastSeen)}</span></>
                }
                return <></>
            }
        },
        {
            title: '',
            width: 100,
            dataIndex: 'operate',
            render: (_: string, record: Machine) => {
                let isExpiry = false;
                if (!record.keyExpiryDisabled) {

                    if (record.expiresAt) {
                        if (record.createdAt && record.createdAt.toDate().getTime() > record.expiresAt.toDate().getTime()) {
                            // 创建时间大于过期时间，说明是旧数据，未过期
                        } else {
                            const expiry = record.expiresAt.toDate()
                            const now = new Date()
                            isExpiry = expiry < now;
                        }
                    }
                }
                return <div className='table-last-col'><Dropdown
                    position='bottomRight'
                    render={
                        <Dropdown.Menu>
                            <Dropdown.Item onClick={() => {
                                setRenameVisible(true);
                                setCurDevice(record);
                            }}>{formatMessage({ id: 'devices.menu.renameDevice' })}</Dropdown.Item>
                            {record.keyExpiryDisabled ?
                                <Dropdown.Item onClick={() => {
                                    setMachineKeyExpiry(record.id, false).then(() => {
                                        setReloadFlag(true)
                                    })
                                }}>{formatMessage({ id: 'devices.menu.enableKeyExpiry' })}</Dropdown.Item> :
                                <Dropdown.Item onClick={() => {
                                    setMachineKeyExpiry(record.id, true).then(() => {
                                        setReloadFlag(true)
                                    })
                                }}>{formatMessage({ id: 'devices.menu.disableKeyExpiry' })}</Dropdown.Item>}

                           {record.os == 'windows' || record.os == 'macOS' ?


                                flynet?.rdpEnabled ? record.user?.rdpEnabled ? record.rdpEnabled ?
                                    <Dropdown.Item
                                        onClick={() => {
                                            SaveMachineRdpSettings(record.id, false).then(() => {
                                                setReloadFlag(true)
                                            })
                                        }}>{formatMessage({ id: 'devices.menu.disableRemoteDesktop' })}</Dropdown.Item> :
                                    <Dropdown.Item
                                        onClick={() => {
                                            SaveMachineRdpSettings(record.id, true).then(() => {
                                                setReloadFlag(true)
                                            })
                                        }}>{formatMessage({ id: 'devices.menu.enableRemoteDesktop' })}</Dropdown.Item> :
                                    <Popover zIndex={3001} content={<div className='p10' dangerouslySetInnerHTML={{ __html: formatMessage({ id: 'devices.menu.remoteDesktopUserNotEnabled' }) }} />}>
                                        <Dropdown.Item disabled >{formatMessage({ id: 'devices.menu.enableRemoteDesktop' })}</Dropdown.Item>
                                    </Popover> : <Popover zIndex={3001} content={<div className='p10' dangerouslySetInnerHTML={{ __html: formatMessage({ id: 'devices.menu.remoteDesktopNotEnabled' }) }} />}>
                                    <Dropdown.Item disabled >{formatMessage({ id: 'devices.menu.enableRemoteDesktop' })}</Dropdown.Item>
                                </Popover> : ''
                            }

                            <Dropdown.Item onClick={() => {
                                navigate(`${BASE_PATH}/logs/?target=${record.name}`)
                            }}>{formatMessage({ id: 'devices.menu.viewConfigLogs' })}</Dropdown.Item>

                            <Dropdown.Divider />
                            <Dropdown.Item onClick={() => {
                                setEditRouteVisible(true);
                                setCurDevice(record);
                            }}>{formatMessage({ id: 'devices.menu.editRouteSettings' })}</Dropdown.Item>
                            {VITE_USE_DEVELOP_FEATURE && <Dropdown.Item onClick={() => {
                                setEditRouteNewVisible(true);
                                setCurDevice(record);
                            }}>{formatMessage({ id: 'devices.menu.editRouteSettingsNew' })}</Dropdown.Item>}
                            {VITE_USE_DEVELOP_FEATURE && <Dropdown.Item onClick={() => {
                                setRangeAcceptVisible(true);
                                setCurDevice(record);
                            }}>{formatMessage({ id: 'devices.menu.serviceReceptionRange' })}</Dropdown.Item>}

                            <Dropdown.Item
                                onClick={() => {
                                    setEditRejectRouteVisible(true);
                                    setCurDevice(record);
                                }}>{formatMessage({ id: 'devices.menu.setRejectRoutes' })}</Dropdown.Item>

                            <Dropdown.Item onClick={() => {
                                setEditRelayVisiable(true);
                                setCurDevice(record);
                            }}>{formatMessage({ id: 'devices.menu.editRelayNode' })}</Dropdown.Item>
                            <Dropdown.Item onClick={() => {
                                setEditMeshEnabledVisible(true);
                                setCurDevice(record);
                            }}>{formatMessage({ id: 'devices.menu.editMeshMode' })}
                            </Dropdown.Item>
                            <Dropdown.Item onClick={() => {
                                setEditAclTagVisible(true);
                                setCurDevice(record);
                            }}>{formatMessage({ id: 'devices.menu.editAccessControlTags' })}</Dropdown.Item>
                            <Dropdown.Divider />
                            <Dropdown.Item disabled={isExpiry} type="danger" onClick={() => {
                                setExpiryVisible(true)
                                setCurDevice(record)
                            }}>{formatMessage({ id: 'devices.menu.forceKeyExpiry' })}</Dropdown.Item>
                            <Dropdown.Item type="danger" onClick={() => {
                                setDelVisible(true)
                                setCurDevice(record)
                            }}>{formatMessage({ id: 'devices.menu.deleteDevice' })}</Dropdown.Item>
                            <Divider />
                            <Dropdown.Item
                                onClick={() => {
                                    setEditGroupVisible(true);
                                    setCurDevice(record);
                                }}
                            >
                                {formatMessage({ id: 'devices.menu.editDeviceGroup' })}
                            </Dropdown.Item>
                            {curGroup && curGroup.type == GroupType.GROUP_STATIC && <Dropdown.Item
                                onClick={() => {
                                    setCurDevice(record);
                                    setRemoveFromGroupVisible(true);
                                }}
                            >{formatMessage({ id: 'devices.menu.removeFromGroup' })}</Dropdown.Item>}
                        </Dropdown.Menu>
                    }
                >
                    <Button><IconMore className='align-v-center' /></Button>
                </Dropdown></div>;
            },
        },
    ];

    const handleSort = (param: any) => {
        const sorter = param.sorter;
        const dataIndex = sorter.dataIndex;
        const sortOrder = sorter.sortOrder;


        let sortedAllDate = [...allDevices];

        if (sortOrder == 'ascend') {
            setSortOrder('ascend');

            if (dataIndex == 'name') {
                sortedAllDate.sort((a?: Machine, b?: Machine) => (a && b && a.name > b.name ? 1 : -1))
            }
            if (dataIndex == 'ipV4') {
                sortedAllDate.sort((a?: Machine, b?: Machine) => {
                    if (a && b && a.ipv4 && b.ipv4) {
                        const aArr = a.ipv4.split('.');
                        const bArr = b.ipv4.split('.');
                        const len = aArr.length > bArr.length ? bArr.length : aArr.length;
                        for (let i = 0; i < len; i++) {
                            if (parseInt(aArr[i]) > parseInt(bArr[i])) {
                                return 1;
                            } else if (parseInt(aArr[i]) < parseInt(bArr[i])) {
                                return -1;
                            }
                        }
                    }
                    return -1;
                });
            }
            if (dataIndex == 'os') {
                sortedAllDate.sort((a?: Machine, b?: Machine) => {
                    if (a && b && a.clientVersion && b.clientVersion) {
                        return compare(a.clientVersion.split('-')[0], b.clientVersion.split('-')[0])
                    }
                    return -1;

                });
            }

            if (dataIndex == 'lastSeen') {
                sortedAllDate.sort((a?: Machine, b?: Machine) => {
                    if (!a || !b) return -1;
                    if (a.connected && b.connected) {
                        return 1;
                    }
                    if (a.connected && !b.connected) {
                        return 1;
                    }
                    if (!a.connected && b.connected) {
                        return -1;
                    }
                    if (b.lastSeen && a.lastSeen && a.lastSeen && b.lastSeen) {
                        return a.lastSeen.seconds > b.lastSeen.seconds ? 1 : -1;
                    }
                    if (a.lastSeen && !b.lastSeen) {
                        return 1;
                    }
                    if (!a.lastSeen && b.lastSeen) {
                        return -1;
                    }
                    return -1;
                });
            }
            setAllDevices(sortedAllDate);

            setDevices(doFilter(1, sortedAllDate, filterParam, curGroup))
        } else if (sortOrder == 'descend') {
            setSortOrder('descend');
            if (dataIndex == 'name') {
                sortedAllDate.sort((a?: Machine, b?: Machine) => (a && b && a.name < b.name ? 1 : -1))
            }
            if (dataIndex == 'ipV4') {
                sortedAllDate.sort((a?: Machine, b?: Machine) => {
                    if (a && b && a.ipv4 && b.ipv4) {
                        const aArr = a.ipv4.split('.');
                        const bArr = b.ipv4.split('.');
                        const len = aArr.length > bArr.length ? bArr.length : aArr.length;
                        for (let i = 0; i < len; i++) {
                            if (parseInt(aArr[i]) > parseInt(bArr[i])) {
                                return -1;
                            } else if (parseInt(aArr[i]) < parseInt(bArr[i])) {
                                return 1;
                            }
                        }
                    }
                    return 1;
                });
            }
            if (dataIndex == 'os') {
                sortedAllDate.sort((a?: Machine, b?: Machine) => {
                    if (a && b && a.clientVersion && b.clientVersion) {
                        return compare(b.clientVersion.split('-')[0], a.clientVersion.split('-')[0])
                    }
                    return 1;

                });
            }

            if (dataIndex == 'lastSeen') {
                sortedAllDate.sort((a?: Machine, b?: Machine) => {
                    if (!a || !b) return -1;
                    if (a.connected && b.connected) {
                        return 1;
                    }
                    if (a.connected && !b.connected) {
                        return -1;
                    }
                    if (!a.connected && b.connected) {
                        return 1;
                    }
                    if (b.lastSeen && a.lastSeen && a.lastSeen && b.lastSeen) {
                        return a.lastSeen.seconds < b.lastSeen.seconds ? 1 : -1;
                    }
                    if (a.lastSeen && !b.lastSeen) {
                        return -1;
                    }
                    if (!a.lastSeen && b.lastSeen) {
                        return 1;
                    }
                    return 1;
                });
            }

            setAllDevices(sortedAllDate);
            setDevices(doFilter(1, sortedAllDate, filterParam, curGroup))
        } else {
            setSortOrder(undefined)
            setAllDevices(originData)
            setDevices(doFilter(1, originData, filterParam, curGroup))

        }
        setSortField(dataIndex)



    }
    // 过滤结果
    const doFilter = (page: number, src: Array<Machine>, filter: DeviceFilter, group?: MachineGroup): Array<Machine> => {
        if (!src || src.length == 0) {
            setTotal(src.length)
            return src.slice(0, page * pageSize);
        }

        if (filter.keywords == '' && filter.os.length == 0 && filter.connectStatus == '' && filter.meshEnabled == '' && !group) {
            setTotal(src.length)
            return src.slice(0, page * pageSize);
        }
        const filteredList = src.filter(record => {
            let { keywords: query, os, connectStatus } = filter;
            if (query) query = query.trim();
            let passQuery = true, passOs = true, passConnectStatus = true, passMeshEnabled = true;
            let passGroup = true;
            if (query) {
                var containsTag = false;
                if (record.tags && record.tags.length > 0) {
                    record.tags.forEach(tag => {
                        if (caseInsensitiveIncludes(tag, query)) {
                            containsTag = true;
                        }
                    })
                }

                if (caseInsensitiveIncludes(record.givenName, query) ||
                    record.user && caseInsensitiveIncludes(record.user.displayName, query) ||
                    record.user && caseInsensitiveIncludes(record.user.loginName, query) ||
                    caseInsensitiveIncludes(record.ipv4, query) ||
                    caseInsensitiveIncludes(record.ipv6, query) ||
                    caseInsensitiveIncludes(record.clientVersion, query) ||
                    caseInsensitiveIncludes(record.description, query) ||
                    containsTag
                ) {
                    passQuery = true;
                } else {
                    passQuery = false;
                }
            }

            if (record.meshEnabled && filter.meshEnabled == 'disable') {
                passMeshEnabled = false;
            } else if (!record.meshEnabled && filter.meshEnabled == 'enable') {
                passMeshEnabled = false;
            }

            if (group) {
                let findGroup = false;
                group.machines.forEach((val: Machine) => {
                    if (record.id == val.id) {
                        findGroup = true;
                    }
                })

                if (findGroup) {
                    passGroup = true;
                } else {
                    passGroup = false;
                }
            }



            if (os.length > 0) {
                passOs = os.indexOf(record.os) >= 0;
            }

            if (connectStatus == 'online') {
                passConnectStatus = record.connected;
            } else if (connectStatus == 'offline') {
                passConnectStatus = !record.connected;
            }
            if (!getLocalStorage(LOCAL_GUIDE_FLAG + flynetGeneral.id)) {
                setLocalStorage(LOCAL_GUIDE_FLAG + flynetGeneral.id, 'one');
            }
            return passQuery && passOs && passConnectStatus && passGroup && passMeshEnabled;
        })

        setTotal(filteredList.length)

        return filteredList.slice(0, page * pageSize)
    }

    const query = async () => {
        const res = await flylayerClient.listMachineGroups({
            flynetId: flynetGeneral.id
        })
        let curGroup: MachineGroup | undefined;
        setGroups(res.groups)
        if (res.groups) {
            res.groups.forEach(g => {
                if (g.id + '' == initFilter.group) {
                    curGroup = g;
                }
            })
        }

        setCurGroup(curGroup)

        setLoading(true)
        listMachines(flynetGeneral.id).then(res => {

            // if (!res.machines.length || res.machines.length < 2) {
            //     let flag = getLocalStorage(LOCAL_GUIDE_FLAG + flynetGeneral.id);
            //     if (!flag) {
            //         navigate(`${BASE_PATH}/guide`);
            //     }

            // }
            res.machines.sort((a?: Machine, b?: Machine) => {
                if (!a || !b) return -1;
                if (!a.authorized && b.authorized) {
                    return -1;
                }
                if (a.authorized && !b.authorized) {
                    return 1;
                }

                return 1;
            });

            setAllDevices(res.machines);
            let copyedData: any = []
            res.machines.forEach(user => {
                copyedData.push({ ...user })
            })
            setOriginData(copyedData);
            setDevices(doFilter(page, res.machines, filterParam, curGroup))
        }).catch(e => {
            console.error(e)
            Notification.error({ content: formatMessage({ id: 'devices.error.getDeviceListFailed' }) })
        }).finally(() => setLoading(false))
    }
    const addPage = () => {
        setDevices(doFilter(page + 1, allDevices, filterParam, curGroup));
        setPage(page + 1)

    }


    useEffect(() => {
        query()
    }, []);

    useEffect(() => {
        if (reloadFlag) {
            setReloadFlag(false)
            setPage(1)
            query()
        }
    }, [reloadFlag])
    useEffect(() => {
        setPage(1)
        setDevices(doFilter(1, allDevices, filterParam, curGroup))

    }, [filterParam])



    const handleGroupChange = (group?: MachineGroup) => {
        setDevices(doFilter(1, allDevices, filterParam, group));
    };


    return {
        columns, loading, allDevices, devices, curDevice, setCurDevice, delVisible, setDelVisible: setDelVisible, expiryVisible, setExpiryVisible, renameVisible, setRenameVisible, editRouteVisible, setEditRouteVisible, editAclTagVisible, setEditAclTagVisible, reloadFlag, setReloadFlag, filterParam, setFilterParam, page, total, addPage, pageSize, handleSort,
        groups,
        curGroup,
        setCurGroup,
        handleGroupChange,
        removeFromGroupVisible,
        setRemoveFromGroupVisible,
        editGroupVisible,
        setEditGroupVisible,
        editRelayVisiable,
        setEditRelayVisiable,
        editRejectRouteVisible,
        setEditRejectRouteVisible,
        editMeshEnabledVisible,
        setEditMeshEnabledVisible,
        editRouteNewVisible,
        setEditRouteNewVisible,
        rangeAcceptVisible,
        setRangeAcceptVisible,
    }
}

export default useTable;