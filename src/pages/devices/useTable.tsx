import { useEffect, useState, useContext, useCallback } from 'react';
import { getFlynet } from '@/services/flynet';
import { flylayerClient } from '@/services/core';
import { useLocale } from '@/locales';
import { Typography, Tag, List, Badge, Popover, Divider, Notification, Button, Dropdown, Tooltip, Select, CheckboxGroup, RadioGroup, Space } from '@douyinfe/semi-ui';
import { IconMore, IconGlobeStroke } from '@douyinfe/semi-icons';
import { FilterParam } from '@/components/search-filter';

import { Timestamp } from "@bufbuild/protobuf";

import { Machine, MachineGroup } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";

import { BASE_PATH } from '@/constants/router';
import { formatIPNVersion, formatDefaultTimestamp } from '@/utils/format';
import styles from './index.module.scss';
import { Flynet } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/flynets_pb";

import DeviceTag from '@/components/device-tag';
import { setMachineKeyExpiry, listMachines } from '@/services/device';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { useNavigate } from 'react-router-dom';
import Copyable from '@douyinfe/semi-ui/lib/es/typography/copyable';
import { VITE_USE_DEVELOP_FEATURE } from '@/utils/service';
import { debounce } from 'lodash';
const { Title, Paragraph, Text } = Typography;

export type DeviceFilter = {
    keywords: string,
    os: string,
    connectStatus: 'online' | 'offline' | '',
    groups: string,
    meshEnabled: 'enable' | 'disable' | ''
}

const useTable = (initFilter: DeviceFilter) => {
    const { formatMessage } = useLocale();
    const flynetGeneral = useContext(FlynetGeneralContext);
    const [_flynet, _setFlynet] = useState<Flynet>();

    const navigate = useNavigate();

    // 强制过期弹出框是否可见
    const [expiryVisible, setExpiryVisible] = useState(false);
    // 删除设备弹出框是否可见
    const [delVisible, setDelVisible] = useState(false);
    // 重命名设备弹出框是否可见
    const [renameVisible, setRenameVisible] = useState(false);
    // 编辑中继弹出框是否可见
    const [editRelayVisiable, setEditRelayVisiable] = useState(false);
    // 编辑路由设置弹出框是否可见
    const [editRouteVisible, setEditRouteVisible] = useState(false);
    // 编辑路由设置弹出框是否可见
    const [editRouteNewVisible, setEditRouteNewVisible] = useState(false);

    // 设置拒绝接受路由弹出框是否可见
    const [editRejectRouteVisible, setEditRejectRouteVisible] = useState(false);
    // 编辑Mesh模式弹出框是否可见
    const [editMeshEnabledVisible, setEditMeshEnabledVisible] = useState(false);
    // 编辑访问控制标签弹出框是否可见
    const [editAclTagVisible, setEditAclTagVisible] = useState(false);
    // 

    // 设备是否正在加载中
    const [loading, setLoading] = useState(true);
    // 设备列表
    const [devices, setDevices] = useState<Machine[]>([]);

    // 当前页码
    const [page, setPage] = useState(1);
    const pageSize = 20;
    // 过滤后总数据条数
    const [total, setTotal] = useState(0);

    const [sortOrder, setSortOrder] = useState<'ascend' | 'descend' | undefined>(undefined);
    const [sortField, setSortField] = useState<string>('');


    // 服务组列表
    const [groups, setGroups] = useState<MachineGroup[]>([]);

    // 当前菜单选中设备
    const [curDevice, setCurDevice] = useState<Machine>();
    // 从设备组中删除弹出框是否可见
    const [removeFromGroupVisible, setRemoveFromGroupVisible] = useState(false);
    // 编辑设备组弹出框是否可见
    const [editGroupVisible, setEditGroupVisible] = useState(false);
    // 服务接收范围设置是否可见
    const [rangeAcceptVisible, setRangeAcceptVisible] = useState(false);


    // 是否重新加载数据
    const [reloadFlag, setReloadFlag] = useState(false);
    // 过滤参数
    const [filter, setFilter] = useState<DeviceFilter>(initFilter);

    const [filterParams, setFilterParams] = useState<FilterParam[]>([]);

    const initFilterParams = (machineGroups: MachineGroup[]) => {
        setFilterParams([
            {
                name: 'keywords',
                // placeholder: '根据用户、设备名称、设备描述、IP、版本号、标签等搜索',
                placeholder: formatMessage({ id: 'devices.filter.search' }),
                label: formatMessage({ id: 'devices.filter.query' }),
                value: initFilter.keywords || '',
            }, {
                name: 'os',
                placeholder: formatMessage({ id: 'devices.filter.selectOS' }),
                label: formatMessage({ id: 'devices.filter.operatingSystem' }),
                value: initFilter.os || '',
                filterComponent: ({ value, onChange }) => {
                    const [checkedList, setCheckedList] = useState(value ? value.split(',') : []);
                    return <>
                        <CheckboxGroup
                            className='mb10'
                            style={{ width: '100%' }}
                            value={checkedList}
                            onChange={value => setCheckedList(value)}
                            options={[
                                { value: 'macOS', label: 'macOS' },
                                { value: 'iOS', label: 'iOS' },
                                { value: 'windows', label: 'Windows' },
                                { value: 'linux', label: 'Linux' },
                                { value: 'android', label: 'Android' },
                            ]}
                        ></CheckboxGroup>
                        <Divider className='mb10' />
                        <Button block onClick={() => onChange(checkedList.join(','))}>{formatMessage({ id: 'devices.filter.apply' })}</Button>
                    </>
                },
                funGetDisplayValue: (value: string) => {
                    let names: string[] = [];
                    if (value) {
                        let val = value.split(',');
                        val.forEach(v => {
                            names.push(v == 'macOS' ? 'macOS' : v == 'iOS' ? 'iOS' : v == 'windows' ? 'Windows' : v == 'linux' ? 'Linux' : v == 'android' ? 'Android' : '');
                        })
                    }
                    return names.join(',');
                }
            },
            // {
            //     name: 'connectStatus',
            //     placeholder: '选择连接状态',
            //     label: '连接状态',
            //     value: initFilter.connectStatus || '',
            //     filterComponent: ({ value, onChange }) => {
            //         const [val, setVal] = useState(value);
            //         return <>
            //             <RadioGroup
            //                 style={{ width: '100%' }}
            //                 value={val}
            //                 className='mb10'
            //                 onChange={e => setVal(e.target.value)}
            //                 options={[
            //                     { value: 'online', label: '在线' },
            //                     { value: 'offline', label: '离线' },
            //                 ]}
            //             />
            //             <Divider className='mb10' />
            //             <Button block onClick={() => onChange(val)}>应用</Button>
            //         </>
            //     },
            //     funGetDisplayValue: (value: any) => {
            //         return value == 'online' ? '在线' : value == 'offline' ? '离线' : '';
            //     }
            // },
            {
                name: 'meshEnabled',
                placeholder: formatMessage({ id: 'devices.filter.selectMeshMode' }),
                label: formatMessage({ id: 'devices.filter.meshMode' }),
                value: initFilter.meshEnabled || '',
                filterComponent: ({ value, onChange }) => {
                    const [val, setVal] = useState(value);

                    return <>
                        <RadioGroup
                            style={{ width: '100%' }}
                            value={val}
                            className='mb10'
                            onChange={e => setVal(e.target.value)}
                            options={[
                                { value: 'enable', label: formatMessage({ id: 'devices.filter.enable' }) },
                                { value: 'disable', label: formatMessage({ id: 'devices.filter.disable' }) },
                            ]}
                        />
                        <Divider className='mb10' />
                        <Button block onClick={() => onChange(val)}>{formatMessage({ id: 'devices.filter.apply' })}</Button>
                    </>
                },
                funGetDisplayValue: (value: any) => {
                    return value == 'enable' ? formatMessage({ id: 'devices.filter.enable' }) : value == 'disable' ? formatMessage({ id: 'devices.filter.disable' }) : '';
                }
            }, {
                name: 'groups',
                placeholder: formatMessage({ id: 'devices.filter.selectDeviceGroup' }),
                label: formatMessage({ id: 'devices.filter.deviceGroup' }),
                value: initFilter.groups || '',
                filterComponent: ({ value, onChange }) => {
                    const [val, setVal] = useState(value ? value.split(',') : []);
                    return <>
                        <Select
                            placeholder={formatMessage({ id: 'devices.filter.selectDeviceGroup' })}
                            className='mb10'
                            value={val}
                            onChange={(val) => {
                                setVal(val);
                            }}
                            style={{ width: 180 }}
                            optionList={
                                machineGroups.map(group => {
                                    return { value: group.id + '', label: group.alias }
                                })
                            }
                            multiple
                            maxTagCount={1}
                        >
                        </Select>
                        <Divider className='mb10' />
                        <Button block onClick={() => onChange(val.join(','))}>{formatMessage({ id: 'devices.filter.apply' })}</Button>
                    </>
                },
                funGetDisplayValue: (val: string) => {
                    let names: string[] = [];
                    if (val) {
                        let values = val.split(',');
                        values.forEach(v => {
                            let group = machineGroups.find(group => group.id + '' == v);
                            if (group) {
                                names.push(group.alias);
                            }
                        })
                    }
                    return names.join(',');
                }
            }
        ]);
    }

    useEffect(() => {
        getFlynet(flynetGeneral.id).then(res => {
            _setFlynet(res.flynet)
        })
    }, [])

    const columns = [
        {
            title: formatMessage({ id: 'devices.table.device' }),
            dataIndex: 'name',
            sorter: true,
            render: (_field: string, record: Machine, _index: number) => {
                // 设备名称
                const name = record.autoGeneratedName ? record.name : record.givenName
                return (
                    <div>
                        <Title heading={5}>
                            <a onClick={() => setTimeout(() => navigate(`${BASE_PATH}/devices/${record.ipv4}`), 10)}>{name}
                                <Text size='small' type='tertiary'>&nbsp;{record.description}</Text>
                            </a>&nbsp;
                            {record.connected ?
                                <span className='mobile-visible'><Badge dot style={{ backgroundColor: 'var(--semi-color-success)' }} /></span> :
                                <span className='mobile-visible'><Badge dot type='tertiary' /></span>}
                        </Title>
                        {record.tags && record.tags.length > 0 ? <div>
                            {record.tags.map((tag, index) => {
                                return <Tag key={index} size='large' shape='circle' color='white' style={{ marginRight: 4, marginTop: 5, marginBottom: 5 }}>{tag}</Tag>
                            })}
                        </div> : <Paragraph>{record.user?.displayName} <Text type="tertiary" className={styles.loginName} size='small' ellipsis={{ showTooltip: true }}>{record.user?.loginName}</Text></Paragraph>
                        }
                        <DeviceTag record={record}></DeviceTag>
                    </div>
                );
            },
        },
        {
            title: formatMessage({ id: 'devices.table.deviceGroup' }),
            width: 300,
            dataIndex: 'machineGroups',
            render: (_field: string, record: Machine) => {
                return <div><Space style={{ flexWrap: 'wrap' }}>{record.machineGroups.map((g, i) => <Tag key={i} size='large'>{g.alias} ({g.name})</Tag>)}</Space></div>
            
            }
        },
        {
            width: 230,
            title: formatMessage({ id: 'devices.table.ip' }),
            dataIndex: 'ipV4',
            sorter: true,
            render: (_field: string, record: Machine) => {
                return (<>
                    <div className={styles.ipCopyable} style={{ display: 'flex', alignItems: 'center', marginBottom: 10 }}>

                        <Popover content={<List
                            bordered>
                            {[record.ipv4, record.ipv6].map((val, index) => {
                                return <List.Item key={index}>
                                    <Paragraph copyable>
                                        {val}</Paragraph>
                                </List.Item>
                            })}

                        </List>
                        }>
                            <Text className={styles.ipLine}>{record.ipv4}</Text>
                        </Popover>
                        <Copyable style={{ lineHeight: 1 }} content={record.ipv4}></Copyable>

                    </div>
                    {record.meshEnabled ?
                        <Tooltip content={formatMessage({ id: 'devices.table.meshEnabledTooltip' })}>

                            <Tag
                                color='green'
                                size='large'
                                shape='circle'
                                prefixIcon={<IconGlobeStroke />}
                            >{formatMessage({ id: 'devices.table.meshEnabled' })}</Tag></Tooltip> :
                        <Tooltip
                            content={formatMessage({ id: 'devices.table.meshDisabledTooltip' })}
                        >
                            <Tag
                                size='large'
                                shape='circle'
                                prefixIcon={<IconGlobeStroke />}>{formatMessage({ id: 'devices.table.meshDisabled' })}</Tag>
                        </Tooltip>}
                </>
                );
            },
        },
        {
            width: 200,
            title: formatMessage({ id: 'devices.table.version' }),
            dataIndex: 'os',
            render: (field: string, record: Machine) => {
                return (
                    <div className='layout-left-icon'>
                        <div>
                            <Paragraph>{formatIPNVersion(record.clientVersion)}</Paragraph>
                            <Paragraph>{field}</Paragraph>
                        </div>
                    </div>
                );
            },
        },
        {
            width: 200,
            title: formatMessage({ id: 'devices.table.lastOnlineTime' }),
            dataIndex: 'last_seen',
            sorter: true,
            render: (_field: Timestamp, record: Machine) => {
                return record.connected ?
                    <span><Badge dot style={{ backgroundColor: 'var(--semi-color-success)' }} /> {formatMessage({ id: 'devices.table.online' })}</span> :
                    <><span><Badge dot type='tertiary' /> {formatDefaultTimestamp(record.lastSeen)}</span></>

            }
        },
        {
            title: '',
            width: 100,
            dataIndex: 'operate',
            render: (_: string, record: Machine) => {
                let isExpiry = false;
                if (!record.keyExpiryDisabled) {

                    if (record.expiresAt) {
                        if (record.createdAt && record.createdAt.toDate().getTime() > record.expiresAt.toDate().getTime()) {
                            // 创建时间大于过期时间，说明是旧数据，未过期
                        } else {
                            const expiry = record.expiresAt.toDate()
                            const now = new Date()
                            isExpiry = expiry < now;
                        }
                    }
                }
                return <div className='table-last-col'><Dropdown
                    position='bottomRight'
                    render={
                        <Dropdown.Menu>
                            <Dropdown.Item onClick={() => {
                                setRenameVisible(true);
                                setCurDevice(record);
                            }}>{formatMessage({ id: 'devices.menu.renameDevice' })}</Dropdown.Item>
                            {record.keyExpiryDisabled ?
                                <Dropdown.Item onClick={() => {
                                    setMachineKeyExpiry(record.id, false).then(() => {
                                        setReloadFlag(true)
                                    })
                                }}>{formatMessage({ id: 'devices.menu.enableKeyExpiry' })}</Dropdown.Item> :
                                <Dropdown.Item onClick={() => {
                                    setMachineKeyExpiry(record.id, true).then(() => {
                                        setReloadFlag(true)
                                    })
                                }}>{formatMessage({ id: 'devices.menu.disableKeyExpiry' })}</Dropdown.Item>}
                            <Dropdown.Item onClick={() => {
                                navigate(`${BASE_PATH}/logs/?target=${record.name}`)
                            }}>{formatMessage({ id: 'devices.menu.viewRecentLogs' })}</Dropdown.Item>

                            <Dropdown.Divider />
                            <Dropdown.Item onClick={() => {
                                setEditRouteVisible(true);
                                setCurDevice(record);
                            }}>{formatMessage({ id: 'devices.menu.editRouteSettings' })}</Dropdown.Item>
                            {VITE_USE_DEVELOP_FEATURE && <Dropdown.Item onClick={() => {
                                setEditRouteNewVisible(true);
                                setCurDevice(record);
                            }}>{formatMessage({ id: 'devices.menu.editRouteSettingsNew' })}</Dropdown.Item>}
                            {VITE_USE_DEVELOP_FEATURE && <Dropdown.Item onClick={() => {
                                setRangeAcceptVisible(true);
                                setCurDevice(record);
                            }}>{formatMessage({ id: 'devices.menu.serviceReceptionRange' })}</Dropdown.Item>}

                            <Dropdown.Item
                                onClick={() => {
                                    setEditRejectRouteVisible(true);
                                    setCurDevice(record);
                                }}>{formatMessage({ id: 'devices.menu.setRejectRoutes' })}</Dropdown.Item>

                            <Dropdown.Item onClick={() => {
                                setEditRelayVisiable(true);
                                setCurDevice(record);
                            }}>{formatMessage({ id: 'devices.menu.editRelayNode' })}</Dropdown.Item>
                            <Dropdown.Item onClick={() => {
                                setEditMeshEnabledVisible(true);
                                setCurDevice(record);
                            }}>{formatMessage({ id: 'devices.menu.editMeshMode' })}
                            </Dropdown.Item>

                            <Dropdown.Item onClick={() => {
                                setEditAclTagVisible(true);
                                setCurDevice(record);
                            }}>{formatMessage({ id: 'devices.menu.editAccessControlTags' })}</Dropdown.Item>
                            <Dropdown.Divider />
                            <Dropdown.Item disabled={isExpiry} type="danger" onClick={() => {
                                setExpiryVisible(true)
                                setCurDevice(record)
                            }}>{formatMessage({ id: 'devices.menu.forceKeyExpiry' })}</Dropdown.Item>
                            <Dropdown.Item type="danger" onClick={() => {
                                setDelVisible(true)
                                setCurDevice(record)
                            }}>{formatMessage({ id: 'devices.menu.deleteDevice' })}</Dropdown.Item>
                            <Divider />
                            <Dropdown.Item
                                onClick={() => {
                                    setEditGroupVisible(true);
                                    setCurDevice(record);
                                }}
                            >
                                {formatMessage({ id: 'devices.menu.editDeviceGroup' })}
                            </Dropdown.Item>
                   
                        </Dropdown.Menu>
                    }
                >
                    <Button><IconMore className='align-v-center' /></Button>
                </Dropdown></div>;
            },
        },
    ];

    const handleSort = (param: any) => {
        const sorter = param.sorter;
        const dataIndex = sorter.dataIndex;

        let sortOrder = '';
        if (sorter.sortOrder == 'ascend') {
            sortOrder = 'ASC';
        } else if (sorter.sortOrder == 'descend') {
            sortOrder = 'DESC';
        }

        queryDevices({
            page: 1,
            sortOrder: sortOrder,
            sortField: dataIndex,
        })
    }
    
    const query = async (_deviceFilter?: DeviceFilter) => {
        const res = await flylayerClient.listMachineGroups({
            flynetId: flynetGeneral.id
        })

        setGroups(res.groups)

        initFilterParams(res.groups)

        queryDevices({
            deviceGroups: res.groups
        })
    }

    const queryDevices = (params?: {
        filter?: DeviceFilter,
        sortOrder?: string,
        sortField?: string,
        page?: number,
        pageSize?: number,
        deviceGroups?: Array<MachineGroup>
    }) => {
        let queryArray: Array<string> = [];

        let limit = pageSize;
        let curPage = page;

        let filterKeywords = filter.keywords || '';
        let filterOs = filter.os || '';
        let filterConnectStatus = filter.connectStatus || '';
        let filterGroups = filter.groups || '';
        let filterMeshEnabled = filter.meshEnabled || '';


        let filterSortOrder = sortOrder;
        let filterSortField = sortField;

        if (params) {
            if (params.filter) {
                setFilter(params.filter);

                filterKeywords = params.filter.keywords;
                filterOs = params.filter.os;
                filterConnectStatus = params.filter.connectStatus;
                filterGroups = params.filter.groups;
                filterMeshEnabled = params.filter.meshEnabled;
            }
            if (params.sortOrder && params.sortField) {
                setSortOrder(params.sortOrder as any);
                setSortField(params.sortField);

                filterSortOrder = params.sortOrder as any;
                filterSortField = params.sortField;
            } else {
                filterSortOrder = undefined;
                filterSortField = '';
                setSortOrder(undefined);
                setSortField('');
            }

            if (params.page) {
                curPage = params.page;
                setPage(params.page);
            }

            if (params.pageSize) {
                limit = params.pageSize;
            }
        }

        if (filterKeywords) {
            queryArray.push(`keywords=${filterKeywords}`);
        }
        if (filterOs) {
            queryArray.push(`os=${filterOs}`);
        }
        if (filterConnectStatus) {
            queryArray.push(`connectStatus=${filterConnectStatus}`);
        }
        if (filterGroups) {
            queryArray.push(`groups=${filterGroups}`);
        }
        if (filterMeshEnabled) {
            queryArray.push(`mesh_disabled=${filterMeshEnabled == 'disable' ? true : false}`);
        }

        if (filterSortOrder && filterSortField) {
            setSortOrder(filterSortOrder as any);
            setSortField(filterSortField);
            let order_by = encodeURIComponent(`${filterSortField} ${filterSortOrder}`);
            queryArray.push(`order_by=${order_by}`);
        }


        const offset = (curPage - 1) * limit;

        queryArray.push(`limit=${limit}`);
        queryArray.push(`offset=${offset}`);

        setLoading(true)

        listMachines(flynetGeneral.id, queryArray.join('&')).then(res => {

            // if (Number(res.total) < 2) {
            //     let flag = getLocalStorage(LOCAL_GUIDE_FLAG + flynetGeneral.id);
            //     if (!flag) {
            //         navigate(`${BASE_PATH}/guide`);
            //     }

            // }

            if (curPage == 1) {
                setDevices(res.machines);
            } else {
                if (res.machines && res.machines.length > 0) {
                    setDevices([...devices, ...res.machines]);
                }
            }
            setTotal(Number(res.total));
        }).catch(e => {
            console.error(e)
            Notification.error({ content: formatMessage({ id: 'devices.error.getDeviceListFailed' }) })
        }).finally(() => setLoading(false))




        // setLoading(true)
        // listMachines(flynetGeneral.id, 'limit=20&offset=0').then(res => {

        //     if (!res.length || res.length < 2) {
        //         let flag = getLocalStorage(LOCAL_GUIDE_FLAG + flynetGeneral.id);
        //         if (!flag) {
        //             navigate(`${BASE_PATH}/guide`);
        //         }

        //     }
        //     res.sort((a?: Machine, b?: Machine) => {
        //         if (!a || !b) return -1;
        //         if (!a.authorized && b.authorized) {
        //             return -1;
        //         }
        //         if (a.authorized && !b.authorized) {
        //             return 1;
        //         }

        //         return 1;
        //     });

        //     setAllDevices(res);
        //     let copyedData: any = []
        //     res.forEach(user => {
        //         copyedData.push({ ...user })
        //     })
        //     setOriginData(copyedData);

        //     setDevices(doFilter(page, res, deviceFilter? deviceFilter: filter))
        // }).catch(e => {
        //     console.error(e)
        //     Notification.error({ content: '获取设备列表失败, 请稍后重试' })
        // }).finally(() => setLoading(false))
    }

    const addPage = () => {
        queryDevices({
            page: page + 1,
            sortOrder: sortOrder,
            sortField: sortField,
        })
    }


    useEffect(() => {
        query()
    }, []);

    useEffect(() => {
        setReloadFlag(false)
        if (reloadFlag) {
            queryDevices({
                page: 1,
                sortOrder: sortOrder,
                sortField: sortField,
            })
        }
    }, [reloadFlag])
    // useEffect(() => {
    //     setPage(1)
    //     setDevices(doFilter(1, allDevices, filter, curGroup))

    // }, [filter])

    const debounceQuery = useCallback(debounce(((filterParam) => queryDevices(filterParam)), 500), [])

    const handleFilterChange = (filter: DeviceFilter) => {
        debounceQuery({
            filter: filter,
            page: 1,
            sortOrder: sortOrder,
            sortField: sortField,
            pageSize: pageSize,
            deviceGroups: groups
        })
    }

    // const handleGroupChange = (group?: MachineGroup) => {
    //     setDevices(doFilter(1, allDevices, filter, ));
    // };


    return {
        columns, loading, devices,
        curDevice, setCurDevice,
        delVisible, setDelVisible,
        expiryVisible, setExpiryVisible,
        renameVisible, setRenameVisible,
        editRouteVisible, setEditRouteVisible,
        editAclTagVisible, setEditAclTagVisible,
        reloadFlag, setReloadFlag, filter, setFilter, page, total, addPage, pageSize, handleSort,
        groups,
        // curGroup,
        // setCurGroup,
        // handleGroupChange,
        removeFromGroupVisible,
        setRemoveFromGroupVisible,
        editGroupVisible,
        setEditGroupVisible,
        editRelayVisiable,
        setEditRelayVisiable,
        editRejectRouteVisible,
        setEditRejectRouteVisible,
        editMeshEnabledVisible,
        setEditMeshEnabledVisible,
        editRouteNewVisible,
        setEditRouteNewVisible,
        filterParams,
        setFilterParams,
        handleFilterChange,
        rangeAcceptVisible,
        setRangeAcceptVisible
    }
}

export default useTable;