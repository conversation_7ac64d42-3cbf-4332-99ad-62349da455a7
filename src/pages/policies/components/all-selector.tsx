import { FC, useState } from 'react';
import { Input, Divider, Button } from '@douyinfe/semi-ui'
import { AclOrigin_Resource, AclOrigin_ResourceType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
import { useLocale } from '@/locales';

const AllSelector: FC<{
    value: AclOrigin_Resource,
    onChange: (val: AclOrigin_Resource) => void
}> = (props) => {
    const { formatMessage } = useLocale();
    const [currentValue, _setCurrentValue] = useState('*');
    return <>
        <Input className='mb10' value={currentValue}  readOnly />
        <Divider className='mb10'></Divider>
        <Button block onClick={() => {
            props.value.value = currentValue;
            props.value.type = AclOrigin_ResourceType.ALL;

            props.onChange(props.value)
        }}>{formatMessage({ id: 'policies.policies.common.apply' })}</Button>
    </>
}

export default AllSelector;