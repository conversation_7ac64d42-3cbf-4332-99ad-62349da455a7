import { FC, useState, useContext, useEffect } from 'react';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import SearchFilter from '@/components/search-filter-combo';
import { UserGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb';
import { AclOrigin_Resource, AclOrigin_ResourceType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';

import { Toast, Modal, Typography, RadioGroup, Select } from '@douyinfe/semi-ui';
import { flylayerClient } from '@/services/core';
import { useLocale } from '@/locales';
import UserSelector from '@/pages/policies/components-filter/user-selector';
import SerrvicesSelector from '@/pages/policies/components-filter/services-selector';
import ServicesGroupSelector from '@/pages/policies/components-filter/services-group-selector';
import StatusSelector, { getStatusDisplayValue } from '@/pages/policies/components-filter/status-selector';

import useTable from '@/pages/policies/useTable'
const { Paragraph } = Typography;
interface FilterParam {
    name: string,
    label: string,
    placeholder: string,
    value: string | any,
    fixed?: boolean,
    filterComponent?: FC<{
        value: string | string[] | any,
        onChange: (val: string | string[] | any) => void
    }>,
    funGetDisplayValue?: (val: string | any) => string
}
type UserFilter = {
    keywords: string,
    roles: string,
    status: 'enable' | 'disable' | '',
    groups: string,
    temporary: 'true' | 'false' | ''
}
interface Props {
    close: () => void;
    success?: () => void;
}

const Index: FC<Props> = (props) => {

    const { getSrcDisplay } = useTable({
        query: '',
        user: '',
        services: '',
        servicesGroup: '',
        status: '',
        group: ''
    });

    const { formatMessage } = useLocale();
    const flynet = useContext(FlynetGeneralContext);

    const query = async () => {

        const resUserGroups = await flylayerClient.listUserGroups({
            flynetId: flynet.id,
        })
        const groups = resUserGroups.groups;
        initFilterParams(groups);

    }

    useEffect(() => {
        query();
    }, [])


    const [filterParams, setFilterParams] = useState<FilterParam[]>([]);


    const [filter, setFilter] = useState<UserFilter>(
        {
            keywords: '',
            roles: '',
            status: '',
            groups: '',
            temporary: ''
        } as UserFilter
    );
    const [saveLoading, setSaveLoading] = useState(false);

    const initFilterParams = (userGroups: UserGroup[]) => {
        let initParams: FilterParam[] = [{
            name: 'query',
            placeholder: formatMessage({ id: 'policies.filter.searchPlaceholder' }),
            label: formatMessage({ id: 'policies.filter.query' }),
            value: '',
        },
        {
            name: 'user',
            placeholder: formatMessage({ id: 'policies.resource.user' }),
            label: formatMessage({ id: 'policies.resource.user' }),
            value: '',
            filterComponent: UserSelector,
            fixed: true,
            funGetDisplayValue: (val) => {

                return getSrcDisplay(new AclOrigin_Resource({
                    value: val,
                    type: AclOrigin_ResourceType.USER
                }), false)
            }
        },
        {
            name: 'services',
            placeholder: formatMessage({ id: 'policies.resource.service' }),
            label: formatMessage({ id: 'policies.resource.service' }),
            value: '',
            filterComponent: SerrvicesSelector,
            fixed: true,
            funGetDisplayValue: (val) => {
                return getSrcDisplay(
                    new AclOrigin_Resource({
                        value: val,
                        type: AclOrigin_ResourceType.SERVICE
                    }), false)
            }
        }, {
            name: 'servicesGroup',
            placeholder: formatMessage({ id: 'policies.resource.serviceGroup' }),
            label: formatMessage({ id: 'policies.resource.serviceGroup' }),
            value: '',
            filterComponent: ServicesGroupSelector, 
            fixed: true,
            funGetDisplayValue: (val) => {
                return getSrcDisplay(new AclOrigin_Resource({
                    value: val,
                    type: AclOrigin_ResourceType.SERVICE_GROUP
                }), false)
            }
        }, {
            name: 'status',
            placeholder: formatMessage({ id: 'policies.filter.status' }),
            label: formatMessage({ id: 'policies.filter.status' }),
            value: '',
            fixed: true,
            filterComponent: StatusSelector,
            funGetDisplayValue: getStatusDisplayValue
        }]

        setFilterParams(initParams);
    }

    const handleSubmit = async () => {
        setSaveLoading(true);


        let queryArray = [];


        if (filter.groups) {
            queryArray.push(`groups=${filter.groups}`);
        }

        if (filter.status == 'enable') {
            queryArray.push(`disabled=false`);
        } else if (filter?.status == 'disable') {
            queryArray.push('disabled=true');
        }

        if (filter.temporary == 'true') {
            queryArray.push('temporary=true');
        } else if (filter.temporary == 'false') {
            queryArray.push('temporary=false');
        }

        if (filter.keywords != "" && filter.keywords.trim() != "") {
            queryArray.push(`keywords=${encodeURIComponent(filter.keywords || '')}`);
        }


        flylayerClient.exportUsers({
            flynetId: flynet.id,
            query: queryArray.join('&')
        }).then((res) => {
            setSaveLoading(false);
            if (res) {
                props.success && props.success();
                props.close();
            }
            window.open(res.downloadUrl, '_blank');
        }
        ).catch((err) => {
            setSaveLoading(false);
            if (err) {
                if (err.response) {
                    if (err.response.status === 403) {
                        Toast.error(formatMessage({ id: 'users.dataExport.noPermission' }));
                    } else {
                        Toast.error(err.response.data.message);
                    }
                }
            }
        });

    }

    return (
        <>
            <Modal
                width={600}
                title={formatMessage({ id: 'policies.dataExport.modal.title' })}
                visible={true}
                onCancel={props.close}
                onOk={handleSubmit}
                okText={formatMessage({ id: 'policies.dataExport.modal.export' })}
                okButtonProps={{ loading: saveLoading }}
                className='semi-modal'
                maskClosable={false}
            >
                <Paragraph type="tertiary" className='mb20'>
                    {formatMessage({ id: 'users.dataExport.description' })}
                </Paragraph>
                <SearchFilter onChange={(val: string, filterParam) => {

                    setFilter({ ...filter, [filterParam.name]: val } as UserFilter)

                    const newFilterParams = filterParams.map((item) => {
                        if (item.name == filterParam.name) {
                            item.value = val;
                        }
                        return item;
                    })
                    setFilterParams(newFilterParams);
                }} filterParams={filterParams} className='mb20 search-bar'></SearchFilter>





            </Modal>

        </>
    );
};

export default Index;