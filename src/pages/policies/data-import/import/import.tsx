import { FC, useContext, useState } from 'react';
import { Button, Typography, List, Toast, Tabs, TabPane, Modal, Row, Col, Space, Upload } from '@douyinfe/semi-ui';
import { User, UserRole, UserGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb';
import styles from './index.module.scss'

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { FileItem } from '@douyinfe/semi-ui/lib/es/upload';
import { IconPaperclip } from '@douyinfe/semi-icons';
import Papa from 'papaparse';
import TableEmpty from '@/components/table-empty';
import { flylayerClient } from '@/services/core';
import { uploadFileWithProgress } from '@/services/file';
import { ImportError } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/import_export_pb';
import { useLocale } from '@/locales';

const { Text, Paragraph, Title } = Typography;

interface Props {
    close: () => void;
    success?: () => void;
}

const UserOverview: FC<{ user: User }> = ({ user }) => {
    const { formatMessage } = useLocale();
    return (
        <List.Item className={styles.tabListItem}>
            <div className={styles.tabListItemInner}>
                <Row>
                    <Col span={16}>
                        <Title heading={6}>
                            {user.displayName}
                        </Title>
                    </Col>
                    <Col span={8} className='table-last-col'>
                        <Text type='tertiary'>
                            {user.role == UserRole.FLYNET_ADMIN ? formatMessage({ id: 'users.role.admin' }) :
                                user.role == UserRole.FLYNET_USER ? formatMessage({ id: 'users.role.user' }) :
                                    user.role == UserRole.SUPER_ADMIN ? formatMessage({ id: 'users.role.superAdmin' }) : formatMessage({ id: 'users.role.unknown' })}
                        </Text>
                    </Col>
                </Row>
                <Paragraph type='tertiary' className='mb10'>{user.loginName}</Paragraph>
                <Paragraph>{formatMessage({ id: 'users.field.userGroup' })}</Paragraph>
                {user.userGroups.map((group: UserGroup) => (
                    <Space key={group.id + ''} style={{ width: '100%' }}>
                        <Text type='tertiary'>{group.alias}</Text><Text size='small' type='tertiary'>{group.name}</Text>
                    </Space>
                ))}
            </div>
        </List.Item>
    );
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const rootPath = 'user';
    

    const [isPreviewStep, setIsPreviewStep] = useState(false);

    const [importUsers, setImportUsers] = useState<User[]>([]);
    const [updateUsers, setUpdateUsers] = useState<User[]>([]);
    const [importErrors, setImportErrors] = useState<ImportError[]>([]);

    const [importId, setImportId] = useState<bigint>();

    const flynet = useContext(FlynetGeneralContext);

    const [saveLoading, setSaveLoading] = useState(false);




    const handleTemplateDown = () => {
        let csv = Papa.unparse([]);
        let blob = new Blob(["\ufeff" + csv], { type: 'text/csv,charset=UTF-8' });
        let a = document.createElement('a');
        a.href = URL.createObjectURL(blob);
        a.download = `${formatMessage({ id: 'users.dataImport.template.filename' })}.csv`;
        a.click();
    }

    const checkFileType = (file: any) => {
        if (file.name.startsWith('.')) {
            return true;
        }

        return file.name.endsWith('.csv');
    }


    const [files, setFiles] = useState<FileItem[]>();
    const handleSubmit = async () => {
        setSaveLoading(true);

        flylayerClient.importUsers({
            id: importId,
        }).then(() => {
            setSaveLoading(false);
            props.success && props.success();
            Toast.success(formatMessage({ id: 'users.dataImport.success' }));
        }
        ).catch(() => {
            setSaveLoading(false);
            Toast.error(formatMessage({ id: 'users.dataImport.failed' }));
        });
    }


    return (
        <><Modal
            width={960}
            title={formatMessage({ id: 'users.dataImport.title' })}
            visible={true}
            onCancel={props.close}
            onOk={handleSubmit}
            okText={formatMessage({ id: 'users.dataImport.import' })}
            okButtonProps={{ loading: saveLoading, disabled: isPreviewStep ? false : true }}
            className='semi-modal'
            maskClosable={false}
        >
            {isPreviewStep ? <>
                <Tabs type="line">
                    <TabPane tab={formatMessage({ id: 'users.dataImport.newUsers' })} itemKey="1" className={styles.tabPane}>
                        {importUsers && importUsers.length > 0 ?
                            <List grid={{ gutter: 12, span: 6 }} dataSource={importUsers}
                                renderItem={(item, index) => (
                                    <UserOverview key={index} user={item} />
                                )}></List> : <TableEmpty loading={false} />}
                    </TabPane>
                    <TabPane tab={formatMessage({ id: 'users.dataImport.updateUsers' })} itemKey="2" className={styles.tabPane}>
                        {updateUsers && updateUsers.length > 0 ? <List grid={{ gutter: 12, span: 6 }} dataSource={updateUsers}
                            renderItem={(item, index) => (
                                <UserOverview key={index} user={item} />
                            )} /> : <TableEmpty loading={false} />}
                    </TabPane>
                    <TabPane tab={formatMessage({ id: 'users.dataImport.errorRows' })} itemKey="3" className={styles.tabPane}>
                        <div className={styles.errorWrap}>
                            <List layout="horizontal" className={styles.errorList}>
                                <List.Item className={styles.errorItem}>
                                    <Text type='tertiary'>{formatMessage({ id: 'users.dataImport.rowNumber' })}</Text>
                                </List.Item>
                                <List.Item className={styles.errorItem}>
                                    <Text type='tertiary'>{formatMessage({ id: 'users.dataImport.errorMessage' })}</Text>
                                </List.Item>
                            </List>

                            {importErrors && importErrors.length > 0 ? <>

                                {importErrors.map((item, index) => {
                                    return (
                                        <List layout="horizontal" key={index} className={styles.errorList} >
                                            <List.Item className={styles.errorItem}>
                                                {item.line}
                                            </List.Item>
                                            {item.data.map((dataItem, dataIndex) => {
                                                return (
                                                    <List.Item key={dataIndex} className={styles.errorItem}>
                                                        <Text ellipsis>{dataItem}</Text>
                                                    </List.Item>
                                                );
                                            })}
                                            <List.Item className={styles.errorItem}>
                                                {item.message}
                                            </List.Item>
                                        </List>
                                    );
                                })}

                            </> : <Paragraph>{formatMessage({ id: 'users.dataImport.noErrors' })}</Paragraph>
                            }
                        </div>
                    </TabPane>
                </Tabs>
            </> : <>

                <Space style={{ alignItems: 'flex-start' }}>
                    <Upload
                        beforeUpload={(prop) => {
                            if (!checkFileType(prop.file)) {
                                Toast.error(formatMessage({ id: 'users.dataImport.uploadCsvOnly' }));
                                return false;
                            }
                            return true;
                        }}
                        style={{ width: 740 }}
                        showUploadList={false}
                        action=''
                        draggable
                        dragMainText={formatMessage({ id: 'users.dataImport.dragMainText' })}
                        dragSubText={formatMessage({ id: 'users.dataImport.dragSubText' })}
                        fileList={files}
                        onChange={({ fileList }) => setFiles(fileList)}
                        customRequest={(options) => {
                            let ext = options.file.name.split('.').pop();
                            if (!ext) return;
                            let fileName = options.file.name.split('.').slice(0, -1).join('.');
                            if (fileName.length > 50) {
                                fileName = fileName.slice(0, 50);
                            }
                            fileName = `${fileName}-${new Date().getTime()}.${ext}`;


                            let uploadName = `${rootPath}/${fileName}`;

                            flylayerClient.getUploadUrlWithPathName({
                                path: rootPath,
                                name: fileName,
                            }).then((res) => {
                                uploadFileWithProgress(res.uploadUrl, options.fileInstance, (total, loaded) => {
                                    options.onProgress && options.onProgress({ total, loaded });
                                }).then(() => {
                                    flylayerClient.importUsersPreview({
                                        flynetId: flynet.id,
                                        fileName: uploadName,
                                        fileUrl: res.accessUrl,
                                    }).then((res) => {
                                        setImportUsers(res.importUsers);
                                        setUpdateUsers(res.updateUsers);
                                        setImportErrors(res.errors);
                                        setImportId(res.importRecordId);
                                        setIsPreviewStep(true);
                                    });
                                }).catch((err) => {
                                    options.onError(err);
                                });
                            }).catch((err) => {
                                options.onError(err);
                            });
                        }}
                        uploadTrigger='auto'
                        limit={1}
                        accept='.csv'
                    ></Upload>
                    <Button size='large' type='primary' theme='solid' icon={<IconPaperclip />} onClick={handleTemplateDown}>{formatMessage({ id: 'users.dataImport.downloadTemplate' })}</Button>
                </Space>
            </>}
        </Modal>
        </>
    );
};

export default Index;