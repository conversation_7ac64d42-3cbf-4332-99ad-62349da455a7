import React, { useState } from 'react'
import { Typography, Row, Col, Button, Tag, Table, Space, Banner, Tree, Tooltip, Spin, Layout, Input, Select } from '@douyinfe/semi-ui';
import { IconList, IconCheckList, IconSearch } from '@douyinfe/semi-icons';

import moment from 'moment';
import { getQueryParam } from '@/utils/query';
import { Location, useLocation, useNavigate } from 'react-router-dom';
import useTable, { ACLFilter } from './useTable-local';
import { AclGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
import qs from 'query-string';
import useAclGroup from './useAclGroup';
import { GroupType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import Del from './del';
import Suspend from './suspend';
import RuleNew from './rule-new';
import RuleEdit from './rule-edit';
import RuleNewService from './rule-new-service';
import UserSelector from '@/components/user-selector';

import PositionFixed from '@/components/position-fixed';

import TableEmpty from '@/components/table-empty';
import Hosts from './hosts';
import Groups from './groups';
import Expressions from './expressions';
import Tagowners from './tagowners';
import Autoapprovers from './autoapprovers';

import RemoveFromGroup from './remove-from-group';
import EditGroup from './edit-group';

import { BASE_PATH } from '@/constants/router';

import styles from './index.module.scss';

import IpGroup from './ip-group/index';

import SplitLayout from '@/components/split-layout';
import { VITE_USE_DEVELOP_FEATURE } from '@/utils/service';
import { useLocale } from '@/locales';
const { Sider, Content } = Layout;

const getACLFilter = (location: Location): ACLFilter => {
    const query: string = getQueryParam('query', location) as string;
    const usersQuery: string = getQueryParam('users', location) as string;

    const services: string = getQueryParam('services', location) as string;
    const servicesGroup: string = getQueryParam('servicesGroup', location) as string;
    const status: string = getQueryParam('status', location) as string;
    const group: string = getQueryParam('group', location) as string;

    let users: string[] = [];
    if (usersQuery && Array.isArray(usersQuery)) {
        users = usersQuery;
    } else if (usersQuery && typeof usersQuery === 'string') {
        users = [usersQuery];
    }

    return {
        query: query || '',
        users: users,
        services: services || '',
        servicesGroup: servicesGroup || '',
        status: status || '',
        group: group || ''
    }
}

const { Title, Paragraph, Text } = Typography;
const Index: React.FC = () => {
    const { formatMessage } = useLocale();

    const { treeData, aclGroupLoading, getMapGroupData } = useAclGroup();
    const initFilter: ACLFilter = getACLFilter(useLocation())
    const navigate = useNavigate();
    // 过滤参数改变时跳转路由
    const doNavigate = (param: ACLFilter) => {
        let query = '';
        if (param.query || (param.users && param.users.length > 0) || param.status || param.services.length > 0 || param.servicesGroup.length > 0 || param.group.length > 0) {
            query = qs.stringify(param, {
                skipEmptyString: true
            });
        }
        if (query) {
            navigate(`${BASE_PATH}/policies/acl?${query}`)
        } else {
            navigate(`${BASE_PATH}/policies/acl`)
        }
    }
    // 规则新建弹窗是否显示
    const [ruleNewVisible, setRuleNewVisible] = useState(false);

    const [ruleNewServiceVisible, setRuleNewServiceVisible] = useState(false);

    const { aclPolicyLoading, aclPolicy, setACLPolicy, columns, selectedACL,
        setSelectedACL,
        ruleEditVisible,
        setRuleEditVisible,
        ruleDeleteVisible,
        setRuleDeleteVisible,
        ruleEnableVisible,
        setRuleEnableVisible,
        ruleDisableVisible,
        setRuleDisableVisible,
        selectedACLIndex,
        userGroups,
        filter,
        acls,
        handleFilterChange,
        getSrcDisplay,
        getDstDisplay,
        storedACLs,
        reloadAclOrigin,
        syncAclOrigin,
        syncAclOriginLoading,
        total,
        ipGroups,
        requery,
        groups,
        curGroup,
        setCurGroup,
        handleGroupChange,
        removeFromGroupVisible,
        setRemoveFromGroupVisible,
        editGroupVisible,
        setEditGroupVisible,
        machineGroups,
    } = useTable(initFilter);

    // 主机名弹出框是否可见
    const [hostsVisible, setHostsVisible] = useState(false);
    // 用户组弹出框是否可见
    const [groupsVisible, setGroupsVisible] = useState(false);
    // 表达式弹出框是否可见
    const [expressionsVisible, setExpressionsvisible] = useState(false);
    // 标签弹出框是否可见
    const [tagownersVisible, setTagownersVisible] = useState(false);
    // 自动审批规则是否可见
    const [autoapproversVisible, setAutoapproversVisible] = useState(false);


    // ip组是否可见
    const [ipGroupVisible, setIpGroupVisible] = useState(false);


    const getTimeDisplay = (str: string) => {
        if (!str) {
            return '';
        }

        const cleanedTimestamp = str.replace(/\s+m=.*$/, '');

        // 将时间字符串转换为Date对象
        const date = new Date(cleanedTimestamp);


        return moment(date).format('YYYY-MM-DD HH:mm:ss');
    }

    const [showGroupNav, setShowGroupNav] = useState(true);




    return <>
        <div style={{ paddingTop: 10 }}>
            <Row className='mb10'>
                <Col span={16}>
                    <Layout className='mb20 search-bar'>
                        <Content className='pr10'>
                            <Input
                                placeholder='根据名称、描述搜索'
                                showClear
                                value={filter.query}
                                onChange={(e) => {

                                    doNavigate({
                                        ...filter,
                                        query: e
                                    });
                                    handleFilterChange({
                                        ...filter,
                                        query: e
                                    })
                                }}
                                suffix={<IconSearch />}
                            />
                        </Content>
                        <Sider>
                            <Space>
                                <UserSelector
                                    style={{ width: 220 }}
                                    insetLabel='用户'
                                    value={filter.users}
                                    onChange={(value) => {

                                        handleFilterChange({
                                            ...filter,
                                            users: value
                                        });
                                        doNavigate({
                                            ...filter,
                                            users: value
                                        });
                                    }}
                                    onLoadingChange={() => { }}
                                ></UserSelector>
                                <Select value={filter.status}
                                    insetLabel='状态'
                                    style={{ width: 120 }}
                                    onChange={(value) => {
                                        handleFilterChange({
                                            ...filter,
                                            status: value as string
                                        });
                                        doNavigate({
                                            ...filter,
                                            status: value as string
                                        });
                                    }}>
                                    <Select.Option value=''>全部</Select.Option>
                                    <Select.Option value='Enable'>启用</Select.Option>
                                    <Select.Option value='Disable'>禁用</Select.Option>
                                </Select>
                            </Space>
                        </Sider>
                    </Layout>

                </Col>
                <Col span={8}><div className='btn-right-col'>
                    <Space>
                        {/* <Button
                            onClick={() => navigate(`${BASE_PATH}/policies/import`)}
                        >数据导入</Button> */}
                        <Button
                            onClick={() => navigate(`${BASE_PATH}/policies/group/`)}>策略组</Button>
                        {/* <Dropdown
                            position='bottomRight'
                            render={
                                <Dropdown.Menu>
                                    <Dropdown.Item onClick={() => setIpGroupVisible(true)}>资源组</Dropdown.Item>
                                    <Dropdown.Divider />

                                    <Dropdown.Item onClick={() => setExpressionsvisible(true)}>表达式</Dropdown.Item>
                                    <Dropdown.Item onClick={() => setTagownersVisible(true)}>标签</Dropdown.Item>

                                </Dropdown.Menu>
                            }
                        >
                            <Button theme='solid' onClick={() => { }} icon={<IconSetting />}></Button>
                        </Dropdown> */}
                        <Button theme='solid' onClick={() => setRuleNewVisible(true)}>添加策略</Button>
                        {VITE_USE_DEVELOP_FEATURE && <Button theme='solid' onClick={() => setRuleNewServiceVisible(true)}>添加策略(新)</Button>}

                    </Space>
                </div></Col>
            </Row>

            {aclPolicy && aclPolicy.syncstatus && !aclPolicy.syncstatus.synchronized &&
                <PositionFixed fixedTop={60}><Banner
                    type="danger"
                    className='mb20'
                    description={<>当前策略已经修改，需要同步才能生效&nbsp;<Button type='danger' theme='solid' loading={syncAclOriginLoading} onClick={
                        syncAclOrigin
                    }>立即生效</Button></>}
                /></PositionFixed>}

            {aclGroupLoading ? <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', minHeight: '100px' }}><Spin /></div> : <SplitLayout
                sideBarVisible={treeData && treeData.length > 0 ? true : false}
                sideBar={<div className={styles.navTree}>
                    <Paragraph
                        className={curGroup ? styles.allGroup : styles.allGroupSelected}
                        onClick={() => {
                            setCurGroup(undefined);
                            const newFilter = { ...filter, group: '' };
                            handleFilterChange(newFilter);
                            doNavigate(newFilter);

                            handleGroupChange(undefined);
                        }}
                    >{formatMessage({ id: 'policies.allPolicies' })}</Paragraph>
                    <Tree
                        expandAll
                        value={curGroup ? curGroup.id + '' : ''}
                        onChange={(value) => {
                            let activeGroup: AclGroup | undefined = undefined;
                            let activeName = '';

                            if (value) {
                                groups.forEach(group => {
                                    if (group.id + '' == value as string) {
                                        setCurGroup(group);
                                        activeGroup = group;
                                        activeName = group.name;
                                    }
                                })
                            } else {
                                setCurGroup(undefined);
                            }
                            handleGroupChange(activeGroup);
                            const newFilter = { ...filter, group: activeName };
                            handleFilterChange(newFilter);
                            doNavigate(newFilter);
                        }}
                        treeData={treeData}
                        renderLabel={(label, item) => {
                            let id = item?.value as string;
                            let aclGroup = getMapGroupData().get(id);

                            if (!aclGroup) {
                                return <Text ellipsis={{ showTooltip: true }} >{label}</Text>
                            }
                            return <Space style={{ verticalAlign: 'text-top' }} spacing={5}>{aclGroup.type == GroupType.GROUP_DYNAMIC ?
                                <Tooltip content="动态策略组">
                                    <IconCheckList className={styles.dynamicIcon} /></Tooltip>
                                : <Tooltip content="静态策略组">
                                    <IconList className={styles.staticIcon} /></Tooltip>}
                                <Tooltip content={aclGroup.name}>
                                    <Text>{aclGroup.alias}</Text>
                                </Tooltip></Space>
                        }}
                    ></Tree>
                </div>}>
                <div className={styles.navContent}>
                    <div className={styles.groupInfo}>
                        <Space>
                            {!showGroupNav &&
                                <Title heading={6} style={{ paddingLeft: 30 }}>
                                    {curGroup ? curGroup.alias : formatMessage({ id: 'policies.allPolicies' })}</Title>}
                            <Tag> {formatMessage({ id: 'policies.totalCount' })}&nbsp;{!aclPolicyLoading ? total : <Spin size='small' />}</Tag>
                            {aclPolicy && aclPolicy.syncstatus && aclPolicy.syncstatus.synchronized && aclPolicy.syncstatus.lastsync && <>
                                <Tag size='small'>上次同步时间：{getTimeDisplay(aclPolicy.syncstatus.lastsync)}  </Tag>
                            </>}
                        </Space> </div>
                    <Table
                        loading={aclPolicyLoading}
                        columns={columns}
                        dataSource={acls}
                        pagination={false}
                        empty={<TableEmpty loading={aclPolicyLoading}></TableEmpty>}
                    ></Table>
                </div>
            </SplitLayout>}
        </div>


        {ruleNewVisible && aclPolicy && ipGroups && <RuleNew
            userGroups={userGroups}
            machineGroups={machineGroups}
            aclPolicy={aclPolicy}
            alcs={storedACLs}
            ipGroups={ipGroups}
            aclGroup={curGroup}
            getSrcDisplay={(val) => getSrcDisplay(val, true)}
            getDstDisplay={(val) => getDstDisplay(val, true)}
            close={() => setRuleNewVisible(false)}
            success={() => {
                reloadAclOrigin()
                setRuleNewVisible(false)
            }}
            groupTreeData={treeData ? treeData : []}
            mapGroupData={getMapGroupData()}
        ></RuleNew>}
        {ruleEditVisible && aclPolicy && selectedACL && ipGroups && <RuleEdit
            aclIndex={selectedACLIndex}
            aclPolicy={aclPolicy}
            acl={selectedACL}
            userGroups={userGroups}
            machineGroups={machineGroups}
            alcs={storedACLs}
            ipGroups={ipGroups}
            getSrcDisplay={(val) => getSrcDisplay(val, true)}
            getDstDisplay={(val) => getDstDisplay(val, true)}
            close={() => setRuleEditVisible(false)}
            success={() => {
                reloadAclOrigin()
                setRuleEditVisible(false)
            }}
            groupTreeData={treeData ? treeData : []}
            mapGroupData={getMapGroupData()}
        ></RuleEdit>

        }
        {
            ruleDeleteVisible && aclPolicy && selectedACL && <Del
                aclIndex={selectedACLIndex}
                aclPolicy={aclPolicy}
                acl={selectedACL}
                close={() => setRuleDeleteVisible(false)}
                success={() => {
                    reloadAclOrigin()
                    setRuleDeleteVisible(false)
                }}></Del>
        }
        {
            ruleEnableVisible && aclPolicy && selectedACL && <Suspend
                aclIndex={selectedACLIndex}
                aclPolicy={aclPolicy}
                acl={selectedACL}
                close={() => setRuleEnableVisible(false)}
                success={() => {
                    reloadAclOrigin()
                    setRuleEnableVisible(false)
                }
                }></Suspend>
        }
        {
            ruleDisableVisible && aclPolicy && selectedACL && <Suspend
                aclIndex={selectedACLIndex}
                aclPolicy={aclPolicy}
                acl={selectedACL}
                close={() => setRuleDisableVisible(false)}
                success={() => {
                    reloadAclOrigin()
                    setRuleDisableVisible(false)
                }
                }></Suspend>
        }
        {hostsVisible && aclPolicy && <Hosts
            aclPolicy={aclPolicy}
            onChange={(policy) => setACLPolicy(policy)}
            close={() => {
                setHostsVisible(false);
            }}
        ></Hosts>}
        {groupsVisible && aclPolicy && <Groups
            aclPolicy={aclPolicy}
            onChange={(policy) => setACLPolicy(policy)}
            close={() => {
                setGroupsVisible(false);
            }}
        ></Groups>}
        {expressionsVisible && aclPolicy && <Expressions
            aclPolicy={aclPolicy}
            onChange={(policy) => setACLPolicy(policy)}
            close={() => {
                setExpressionsvisible(false);
            }}
        ></Expressions>}
        {tagownersVisible && aclPolicy && <Tagowners
            aclPolicy={aclPolicy}
            onChange={(policy) => setACLPolicy(policy)}
            close={() => {
                setTagownersVisible(false);
            }}
        ></Tagowners>}
        {autoapproversVisible && aclPolicy && <Autoapprovers
            aclPolicy={aclPolicy}
            onChange={(policy) => setACLPolicy(policy)}
            close={() => {
                setAutoapproversVisible(false);
            }}
        ></Autoapprovers>}
        {ipGroupVisible && <IpGroup success={() => {
            requery();
            setIpGroupVisible(false);
        }} close={() => setIpGroupVisible(false)}></IpGroup>}

        {selectedACL && editGroupVisible && <EditGroup
            close={() => {
                setSelectedACL(undefined)
                setEditGroupVisible(false)
            }}
            record={selectedACL}
            success={() => {
                setEditGroupVisible(false)
                reloadAclOrigin()
            }}
        ></EditGroup>
        }
        {selectedACL && curGroup && removeFromGroupVisible && <RemoveFromGroup
            close={() => {
                setSelectedACL(undefined)
                setRemoveFromGroupVisible(false)
            }}
            record={selectedACL}
            group={curGroup}
            success={() => {
                setRemoveFromGroupVisible(false)
                reloadAclOrigin()
            }}
        ></RemoveFromGroup>}
        {ruleNewServiceVisible && aclPolicy && ipGroups && <RuleNewService
            userGroups={userGroups}
            aclPolicy={aclPolicy}
            alcs={storedACLs}
            ipGroups={ipGroups}
            aclGroup={curGroup}
            getSrcDisplay={(val) => getSrcDisplay(val, true)}
            getDstDisplay={(val) => getDstDisplay(val, true)}
            close={() => setRuleNewServiceVisible(false)}
            success={() => {
                reloadAclOrigin()
                setRuleNewServiceVisible(false)
            }}
            groupTreeData={treeData ? treeData : []}
            mapGroupData={getMapGroupData()}
        ></RuleNewService>}
    </>
}

export default Index;