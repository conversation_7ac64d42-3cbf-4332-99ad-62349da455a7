import React, { useState } from 'react'
import { Typography, Row, Col, Button, Tag, Table, Space, Banner, Spin, Tree, Tooltip } from '@douyinfe/semi-ui';
import { IconCheckList, IconList } from '@douyinfe/semi-icons';
import { LocaleFormatter } from '@/locales';
import { AclGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
import { GroupType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';

import moment from 'moment';
import { getQueryParam } from '@/utils/query';
import SearchFilter from '@/components/search-filter';
import { Location, useLocation, useNavigate } from 'react-router-dom';
import useTable, { ACLFilter } from './useTable';
import qs from 'query-string';
import Del from './del';
import Suspend from './suspend';
import RuleNew from './rule-new';
import RuleEdit from './rule-edit';
import RuleNewService from './rule-new-service';

import PositionFixed from '@/components/position-fixed';

import TableEmpty from '@/components/table-empty';

import Hosts from './hosts';
import Expressions from './expressions';
import Tagowners from './tagowners';
import Autoapprovers from './autoapprovers';
import { useLocale } from '@/locales';

import SplitLayout from '@/components/split-layout';
import EditGroup from './edit-group';

import { BASE_PATH } from '@/constants/router';

import styles from './index.module.scss';

import IpGroup from './ip-group/index';

import { VITE_USE_DEVELOP_FEATURE } from '@/utils/service';

const getACLFilter = (location: Location): ACLFilter => {
    const query: string = getQueryParam('query', location) as string;
    const user: string = getQueryParam('user', location) as string;

    const services: string = getQueryParam('services', location) as string;
    const servicesGroup: string = getQueryParam('servicesGroup', location) as string;
    const status: string = getQueryParam('status', location) as string;
    const group: string = getQueryParam('group', location) as string;

    return {
        query: query || '',
        user: user || '',
        services: services || '',
        servicesGroup: servicesGroup || '',
        status: status || '',
        group: group || ''
    }
}

const { Title, Paragraph, Text } = Typography;
const Index: React.FC = () => {
    const { formatMessage } = useLocale();

    const initFilter: ACLFilter = getACLFilter(useLocation())
    const navigate = useNavigate();
    // 过滤参数改变时跳转路由
    const doNavigate = (param: ACLFilter) => {
        let query = '';
        if (param.query || param.user || param.status || param.services.length > 0 || param.servicesGroup.length > 0 || param.group.length > 0) {
            query = qs.stringify(param, {
                skipEmptyString: true
            });
        }
        if (query) {
            navigate(`${BASE_PATH}/policies?${query}`)
        } else {
            navigate(`${BASE_PATH}/policies`)
        }
    }
    // 规则新建弹窗是否显示
    const [ruleNewVisible, setRuleNewVisible] = useState(false);

    const [ruleNewServiceVisible, setRuleNewServiceVisible] = useState(false);

    const { aclPolicyLoading, aclPolicy, setACLPolicy, columns, selectedACL,
        setSelectedACL,
        ruleEditVisible,
        setRuleEditVisible,
        ruleDeleteVisible,
        setRuleDeleteVisible,
        ruleEnableVisible,
        setRuleEnableVisible,
        ruleDisableVisible,
        setRuleDisableVisible,
        selectedACLIndex,
        userGroups,
        filter,
        setFilter,
        filterParams,
        setFilterParams,
        acls,
        handleFilterChange,
        getSrcDisplay,
        getDstDisplay,
        storedACLs,
        reloadAclOrigin,
        syncAclOrigin,
        syncAclOriginLoading,
        total,
        ipGroups,
        requery,
        treeData,
        getMapGroupData,
        editGroupVisible,
        setEditGroupVisible,
        groups,
        curGroup,
        setCurGroup,
        handleGroupChange,
        machineGroups,
    } = useTable(initFilter);

    // 主机名弹出框是否可见
    const [hostsVisible, setHostsVisible] = useState(false);

    // 表达式弹出框是否可见
    const [expressionsVisible, setExpressionsvisible] = useState(false);
    // 标签弹出框是否可见
    const [tagownersVisible, setTagownersVisible] = useState(false);
    // 自动审批规则是否可见
    const [autoapproversVisible, setAutoapproversVisible] = useState(false);


    // ip组是否可见
    const [ipGroupVisible, setIpGroupVisible] = useState(false);

    const getTimeDisplay = (str: string) => {
        if (!str) {
            return '';
        }

        const cleanedTimestamp = str.replace(/\s+m=.*$/, '');

        // 将时间字符串转换为Date对象
        const date = new Date(cleanedTimestamp);
        return moment(date).format('YYYY-MM-DD HH:mm:ss');
    }

    const [showGroupNav] = useState(true);



    return <>
        <div className='general-page'>
            <Row>
                <Col sm={24} md={20}>
                    <Title heading={3} className='mb10'><LocaleFormatter id="policies.title" /></Title>
                </Col>
                <Col sm={24} md={4}><div className='btn-right-col mb10'>
                    <Space>
                        <Button
                            onClick={() => navigate(`${BASE_PATH}/policies/export/`)}
                        >{formatMessage({ id: 'users.index.button.dataExport' })}</Button>
                        <Button
                            onClick={() => navigate(`${BASE_PATH}/policies/import/`)}
                        >{formatMessage({ id: 'users.index.button.dataImport' })}</Button>
                        {aclPolicy && aclPolicy.syncstatus && aclPolicy.syncstatus.synchronized && aclPolicy.syncstatus.lastsync && <>
                            <Tag style={{ height: 32 }} size='large'><LocaleFormatter id="policies.lastSyncTime" />：{getTimeDisplay(aclPolicy.syncstatus.lastsync)}  </Tag>
                        </>}
                        <Button
                            onClick={() => navigate(`${BASE_PATH}/policies/group/`)}><LocaleFormatter id="policies.policyGroup" /></Button>
                        {/* <Dropdown
                position='bottomRight'
                render={
                    <Dropdown.Menu>
                        <Dropdown.Item onClick={() => setIpGroupVisible(true)}>{formatMessage({ id: 'policies.resourceGroup' })}</Dropdown.Item>
                        <Dropdown.Divider />
                        <Dropdown.Item onClick={() => setExpressionsvisible(true)}>{formatMessage({ id: 'policies.expressions' })}</Dropdown.Item>
                        <Dropdown.Item onClick={() => setTagownersVisible(true)}>{formatMessage({ id: 'policies.tags' })}</Dropdown.Item>
                    </Dropdown.Menu>
                }
            >
                <Button theme='solid' onClick={() => { }} icon={<IconSetting />}></Button>
            </Dropdown> */}
                        <Button theme='solid' onClick={() => setRuleNewVisible(true)}><LocaleFormatter id="policies.addPolicy" /></Button>
                        {VITE_USE_DEVELOP_FEATURE && <Button theme='solid' onClick={() => setRuleNewServiceVisible(true)}><LocaleFormatter id="policies.addPolicyNew" /></Button>}

                    </Space>
                </div></Col>
            </Row>
            <Paragraph type='tertiary' className='mb40'><LocaleFormatter id="policies.description" /></Paragraph>

            <SearchFilter
                onChange={(val: string, filterParam) => {
                    const newFilter = { ...filter, [filterParam.name]: val };
                    setFilter(newFilter)
                    doNavigate(newFilter);

                    const newFilterParams = filterParams.map((item) => {
                        if (item.name == filterParam.name) {
                            item.value = val;
                        }
                        return item;
                    })
                    setFilterParams(newFilterParams);
                    handleFilterChange(newFilter)
                }}
                filterParams={filterParams}
                className='mb20 search-bar'
            />
            {aclPolicy && aclPolicy.syncstatus && !aclPolicy.syncstatus.synchronized &&
                <PositionFixed fixedTop={60}><Banner
                    type="danger"
                    className='mb20'
                    description={<><LocaleFormatter id="policies.syncRequired" />&nbsp;<Button type='danger' theme='solid' loading={syncAclOriginLoading} onClick={
                        syncAclOrigin
                    }><LocaleFormatter id="policies.syncNow" /></Button></>}
                /></PositionFixed>}
            {aclPolicyLoading ? <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', minHeight: '100px' }}><Spin /></div> : <SplitLayout
                sideBarVisible={treeData && treeData.length == 0 ? false : true}
                sideBar={<div className={styles.navTree}>
                    <Paragraph
                        className={curGroup ? styles.allGroup : styles.allGroupSelected}
                        onClick={() => {
                            setCurGroup(undefined);
                            const newFilter = { ...filter, group: '' };
                            setFilter(newFilter);
                            doNavigate(newFilter);

                            handleGroupChange(undefined);
                        }}
                    >{formatMessage({ id: 'policies.allPolicies' })}</Paragraph>
                    <Tree
                        expandAll
                        value={curGroup ? curGroup.id + '' : ''}
                        onChange={(value) => {
                            let activeGroup: AclGroup | undefined = undefined;
                            let activeName = '';

                            if (value) {
                                groups.forEach(group => {
                                    if (group.id + '' == value as string) {
                                        setCurGroup(group);
                                        activeGroup = group;
                                        activeName = group.name;
                                    }
                                })
                            } else {
                                setCurGroup(undefined);
                            }
                            handleGroupChange(activeGroup);
                            const newFilter = { ...filter, group: activeName };
                            setFilter(newFilter);
                            doNavigate(newFilter);
                        }}
                        treeData={treeData}
                        renderLabel={(label, item) => {
                            let id = item?.value as string;
                            let aclGroup = getMapGroupData().get(id);

                            if (!aclGroup) {
                                return <Text ellipsis={{ showTooltip: true }} >{label}</Text>
                            }
                            return <Space style={{ verticalAlign: 'text-top' }} spacing={5}>{aclGroup.type == GroupType.GROUP_DYNAMIC ?
                                <Tooltip content="动态策略组">
                                    <IconCheckList className={styles.dynamicIcon} /></Tooltip>
                                : <Tooltip content="静态策略组">
                                    <IconList className={styles.staticIcon} /></Tooltip>}
                                <Tooltip content={aclGroup.name}>
                                    <Text>{aclGroup.alias}</Text>
                                </Tooltip></Space>
                        }}
                    ></Tree>
                </div>}>
                <div className={styles.navContent}>
                    <div className={styles.groupInfo}>
                        <Space>
                            {!showGroupNav &&
                                <Title heading={6} style={{ paddingLeft: 30 }}>
                                    {curGroup ? curGroup.alias : formatMessage({ id: 'policies.allPolicies' })}</Title>}
                            <Tag> {formatMessage({ id: 'policies.totalCount' })}&nbsp;{!aclPolicyLoading ? total : <Spin size='small' />}</Tag></Space> </div>
                    <Table
                        loading={aclPolicyLoading}
                        columns={columns}
                        dataSource={acls}
                        pagination={false}
                        empty={<TableEmpty loading={aclPolicyLoading}></TableEmpty>}
                    ></Table>
                </div>
            </SplitLayout>}
        </div>

        {ruleNewVisible && aclPolicy && ipGroups && <RuleNew
            userGroups={userGroups}
            machineGroups={machineGroups}
            aclPolicy={aclPolicy}
            alcs={storedACLs}
            ipGroups={ipGroups}
            aclGroup={groups.length > 0 ? groups[0] : undefined}
            getSrcDisplay={(val) => getSrcDisplay(val, true)}
            getDstDisplay={(val) => getDstDisplay(val, true)}
            close={() => setRuleNewVisible(false)}
            success={() => {
                reloadAclOrigin()
                setRuleNewVisible(false)
            }}
            groupTreeData={treeData ? treeData : []}
            mapGroupData={getMapGroupData()}
        ></RuleNew>}
        {ruleEditVisible && aclPolicy && selectedACL && ipGroups && <RuleEdit
            aclIndex={selectedACLIndex}
            aclPolicy={aclPolicy}
            acl={selectedACL}
            userGroups={userGroups}
            machineGroups={machineGroups}
            alcs={storedACLs}
            ipGroups={ipGroups}
            getSrcDisplay={(val) => getSrcDisplay(val, true)}
            getDstDisplay={(val) => getDstDisplay(val, true)}
            close={() => setRuleEditVisible(false)}
            success={() => {
                reloadAclOrigin()
                setRuleEditVisible(false)
            }}
            groupTreeData={treeData ? treeData : []}
            mapGroupData={getMapGroupData()}
        ></RuleEdit>

        }
        {
            ruleDeleteVisible && aclPolicy && selectedACL && <Del
                aclIndex={selectedACLIndex}
                aclPolicy={aclPolicy}
                acl={selectedACL}
                close={() => setRuleDeleteVisible(false)}
                success={() => {
                    reloadAclOrigin()
                    setRuleDeleteVisible(false)
                }}></Del>
        }
        {
            ruleEnableVisible && aclPolicy && selectedACL && <Suspend
                aclIndex={selectedACLIndex}
                aclPolicy={aclPolicy}
                acl={selectedACL}
                close={() => setRuleEnableVisible(false)}
                success={() => {
                    reloadAclOrigin()
                    setRuleEnableVisible(false)
                }
                }></Suspend>
        }
        {
            ruleDisableVisible && aclPolicy && selectedACL && <Suspend
                aclIndex={selectedACLIndex}
                aclPolicy={aclPolicy}
                acl={selectedACL}
                close={() => setRuleDisableVisible(false)}
                success={() => {
                    reloadAclOrigin()
                    setRuleDisableVisible(false)
                }
                }></Suspend>
        }
        {hostsVisible && aclPolicy && <Hosts
            aclPolicy={aclPolicy}
            onChange={(policy) => setACLPolicy(policy)}
            close={() => {
                setHostsVisible(false);
            }}
        ></Hosts>}

        {expressionsVisible && aclPolicy && <Expressions
            aclPolicy={aclPolicy}
            onChange={(policy) => setACLPolicy(policy)}
            close={() => {
                setExpressionsvisible(false);
            }}
        ></Expressions>}
        {tagownersVisible && aclPolicy && <Tagowners
            aclPolicy={aclPolicy}
            onChange={(policy) => setACLPolicy(policy)}
            close={() => {
                setTagownersVisible(false);
            }}
        ></Tagowners>}
        {autoapproversVisible && aclPolicy && <Autoapprovers
            aclPolicy={aclPolicy}
            onChange={(policy) => setACLPolicy(policy)}
            close={() => {
                setAutoapproversVisible(false);
            }}
        ></Autoapprovers>}
        {ipGroupVisible && <IpGroup success={() => {
            requery();
            setIpGroupVisible(false);
        }} close={() => setIpGroupVisible(false)}></IpGroup>}

        {selectedACL && editGroupVisible && <EditGroup
            close={() => {
                setSelectedACL(undefined)
                setEditGroupVisible(false)
            }}
            record={selectedACL}
            success={() => {
                setEditGroupVisible(false)
                reloadAclOrigin()
            }}
        ></EditGroup>}

        {ruleNewServiceVisible && aclPolicy && ipGroups && <RuleNewService
            userGroups={userGroups}
            aclPolicy={aclPolicy}
            alcs={storedACLs}
            ipGroups={ipGroups}
            getSrcDisplay={(val) => getSrcDisplay(val, true)}
            getDstDisplay={(val) => getDstDisplay(val, true)}
            close={() => setRuleNewServiceVisible(false)}
            success={() => {
                reloadAclOrigin()
                setRuleNewServiceVisible(false)
            }}
            groupTreeData={treeData ? treeData : []}
            mapGroupData={getMapGroupData()}
        ></RuleNewService>}
    </>
}

export default Index;