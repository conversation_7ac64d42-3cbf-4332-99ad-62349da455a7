import React, { useState, useContext } from "react";

import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { Typography, Modal, Form, Row, Col, Notification, Popover, Space, Divider, Tooltip, Checkbox } from "@douyinfe/semi-ui";
import { useLocale } from '@/locales';
import MulteSelect, { SelectItemParam } from "@/components/multi-select";
import { AclOrigin_Resource, AclOrigin_ResourceType, AclGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
import { VITE_LOCAL_PAGER_AND_FILTER } from '@/utils/service';
import { MachineGroup } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";

import { GroupType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import { IpGroup } from '@/interface/ip-group';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import SingleSelector from "../components/single-selector";
import SingleSelectorMulti from '../components/single-selector-multi';
import SingleSelectorPort from "../components/single-selector-port";
import SingleSelectorPortMulti from "../components/single-selector-port-multi";
import IpInput from "../components/ip-input";
import IpInputMulti from "../components/ip-input-multi";
import IpInputPort from "../components/ip-input-port";
import IpInputPortMulti from "../components/ip-input-port-multi";

import ServicesSelector from '../components/services-selector';
import ServicesSelectorMulti from '../components/services-selector-multi';
import ServicesGroupSelector from "../components/services-group-selector";
import ServicesGroupSelectorMulti from '../components/services-group-selector-multi';
import UserSelector from "../components/user-selector";
import UserSelectorMulti from "../components/user-selector-multi";
import UserSelectorPort from "../components/user-selector-port";
import UserSelectorPortMulti from "../components/user-selector-port-multi";
import AllSelector from "../components/all-selector";
import AllSelectorPort from "../components/all-selector-port";
import MachineSelector from "../components/machine-selector";
import MachineSelectorMulti from "../components/machine-selector-multi";
import MachineSelectorPort from "../components/machine-selector-port";
import MachineSelectorPortMulti from "../components/machine-selector-port-multi";
import { ACLPolicy, AclOrigin } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
import { IconHelpCircle, IconApartment } from '@douyinfe/semi-icons';
import { UserGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb';

import { flylayerClient } from '@/services/core';

interface Props {
    close: () => void,
    success?: () => void,
    ipGroups: IpGroup[],
    alcs: AclOrigin[],
    aclPolicy: ACLPolicy,
    userGroups: UserGroup[],
    machineGroups: MachineGroup[],
    aclGroup?: AclGroup,
    getSrcDisplay: (val: AclOrigin_Resource) => string,
    getDstDisplay: (val: AclOrigin_Resource) => string,
    groupTreeData: any[],
    mapGroupData: Map<string, AclGroup>,
}
const { Input, Select, RadioGroup, Radio, InputNumber, TreeSelect } = Form
const { Text } = Typography;

const Index: React.FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const flynet = useContext(FlynetGeneralContext);
    const [formApi, SetFormApi] = useState<FormApi>();
    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);

    // 主机名
    const hostList: Array<{ value: string, label: string }> = [];
    props.ipGroups.forEach(ipGroup => {
        hostList.push({
            value: ipGroup.name,
            label: ipGroup.description
        })
    })


    // 用户组
    const userGroups: Array<{ value: string, label: string }> = [];
    props.userGroups.forEach(userGroup => {
        userGroups.push({
            value: `group:${userGroup.name}`,
            label: userGroup.alias ? `${userGroup.alias}(${userGroup.name})` : userGroup.name
        })
    })
    
    // 表达式
    const expressions: Array<{ value: string, label: string }> = [];
    Object.keys(props.aclPolicy.expressions).forEach(key => {
        expressions.push({
            label: key, value: key
        })
    })

    // 标签
    const tags: Array<{ value: string, label: string }> = [];
    Object.keys(props.aclPolicy.tagowners).forEach(key => {
        tags.push({
            label: key, value: key
        })
    })


    const [src, setSrc] = useState<AclOrigin_Resource[]>([]);
    const [dst, setDst] = useState<AclOrigin_Resource[]>([]);

    const [aclGroups, setAclGroups] = useState<AclGroup[]>(props.aclGroup ? [props.aclGroup] : []);

    const handleOk = async () => {
        if (!formApi) {
            return
        }

        await formApi.validate();

        const { name, description, action, disabled, priority } = formApi.getValues();


        let acl = new AclOrigin({
            name: name.trim(),
            description: description ? description.trim() : '',
            action: action,
            src: src,
            dst: dst,
            disabled: disabled === 0 ? false : true,
            priority: priority,
            aclGroups: aclGroups
        });

        // 验证是否有源，目标重复的数据
        for (let i = 0; i < props.alcs.length; i++) {
            let ia = props.alcs[i];

            if (ia.name === acl.name) {
                Notification.error({ content: `${formatMessage({ id: 'policies.new.duplicateName' })} ${ia.name}`, position: "bottomRight" })
                return;
            }

            if (ia.src === acl.src && ia.dst === acl.dst) {
                Notification.error({ content: `${formatMessage({ id: 'policies.new.duplicatePolicy' })} ${ia.name}`, position: "bottomRight" })
                return;
            }
        }
        setLoading(true);
        flylayerClient.createAclOrigin({
            flynetId: flynet.id,
            origin: acl
        }).then(() => {
            props.success && props.success();
            Notification.success({ content: formatMessage({ id: 'policies.saveSuccess' }), position: "bottomRight" })
        }).catch((err) => {
            console.error(err);
            Notification.error({ content: formatMessage({ id: 'policies.saveFailed' }), position: "bottomRight" })
        }).finally(() => {
            setLoading(false);
        })
    };


    let dstItems = [{
        name: '',
        label: formatMessage({ id: 'policies.selector.all' }),
        placeholder: '',
        value: new AclOrigin_Resource({
            type: AclOrigin_ResourceType.ALL,
            value: '*:*'
        }),
        selectComponent: AllSelectorPort
    }, {
        name: '',
        label: 'IP',
        placeholder: '',
        value: new AclOrigin_Resource({
            type: AclOrigin_ResourceType.IP,
            value: ''
        }),
        selectComponent: IpInputPort,
        multiSelectComponent: IpInputPortMulti
    },
    {
        name: '',
        label: formatMessage({ id: 'policies.selector.device' }),
        placeholder: '',
        value: new AclOrigin_Resource({
            type: AclOrigin_ResourceType.DEVICE,
            value: ''
        }),
        selectComponent: MachineSelectorPort,
        multiSelectComponent: MachineSelectorPortMulti
    },
    {
        name: '',
        label: formatMessage({ id: 'policies.selector.deviceGroup' }),
        placeholder: '',
        value: new AclOrigin_Resource({
            type: AclOrigin_ResourceType.DEVICE_GROUP,
            value: ''
        }),
        selectComponent: SingleSelectorPort,
        multiSelectComponent: SingleSelectorPortMulti,
        optList: props.machineGroups.map(item => {
            return {
                value: item.name,
                label: item.alias ? `${item.alias}(${item.name})` : item.name
            }
        })
    },
    // {
    //     name: '',
    //     label: '域名',
    //     placeholder: '',
    //     value: new AclOrigin_Resource({
    //         type: AclOrigin_ResourceType.DOMAIN,
    //         value: ''
    //     }),
    //     selectComponent: DomainInputPort
    // }, 
    {
        name: '',
        label: formatMessage({ id: 'policies.label.resourceGroup' }),
        placeholder: '',
        value: new AclOrigin_Resource({
            type: AclOrigin_ResourceType.IP_GROUP,
            value: ''
        }),
        selectComponent: SingleSelectorPort,
        multiSelectComponent: SingleSelectorPortMulti,
        optList: hostList
    }, {
        name: '',
        label: formatMessage({ id: 'policies.label.user' }),
        placeholder: '',
        value: new AclOrigin_Resource({
            type: AclOrigin_ResourceType.USER,
            value: ''
        }),
        selectComponent: UserSelectorPort,
        multiSelectComponent: UserSelectorPortMulti
    }, {
        name: '',
        label: formatMessage({ id: 'policies.label.userGroup' }),
        placeholder: '',
        value: new AclOrigin_Resource({
            type: AclOrigin_ResourceType.USER_GROUP,
            value: ''
        }),
        optList: userGroups,
        selectComponent: SingleSelectorPort,
        multiSelectComponent: SingleSelectorPortMulti,
    }, {
        name: '',
        label: formatMessage({ id: 'policies.label.builtinUserGroup' }),
        placeholder: '',
        value: new AclOrigin_Resource({
            type: AclOrigin_ResourceType.USER_AUTO_GROUP,
            value: ''
        }),
        optList: [{
            value: 'autogroup:self',
            label: formatMessage({ id: 'policies.builtinGroup.self' })
        }, {
            value: 'autogroup:members',
            label: formatMessage({ id: 'policies.builtinGroup.members' })
        }
        ],
        selectComponent: SingleSelectorPort,
        multiSelectComponent: SingleSelectorPortMulti,

    },
    {
        name: '',
        label: formatMessage({ id: 'policies.label.tag' }),
        placeholder: '',
        value: new AclOrigin_Resource({
            type: AclOrigin_ResourceType.TAG,
            value: ''
        }),
        optList: tags,
        selectComponent: SingleSelectorPort,
        multiSelectComponent: SingleSelectorPortMulti,
    }, {
        name: '',
        label: formatMessage({ id: 'policies.label.service' }),
        placeholder: '',
        value: new AclOrigin_Resource({
            type: AclOrigin_ResourceType.SERVICE,
            value: ''
        }),

        selectComponent: ServicesSelector,
        multiSelectComponent: ServicesSelectorMulti

    }, {
        name: '',
        label: formatMessage({ id: 'policies.label.serviceGroup' }),
        placeholder: '',
        value: new AclOrigin_Resource({
            type: AclOrigin_ResourceType.SERVICE_GROUP,
            value: ''
        }),
        selectComponent: ServicesGroupSelector,
        multiSelectComponent: ServicesGroupSelectorMulti
    }];

    if (VITE_LOCAL_PAGER_AND_FILTER) {
        dstItems = [{
            name: '',
            label: formatMessage({ id: 'policies.selector.all' }),
            placeholder: '',
            value: new AclOrigin_Resource({
                type: AclOrigin_ResourceType.ALL,
                value: '*:*'
            }),
            selectComponent: AllSelectorPort
        }, {
            name: '',
            label: 'IP',
            placeholder: '',
            value: new AclOrigin_Resource({
                type: AclOrigin_ResourceType.IP,
                value: ''
            }),
            selectComponent: IpInputPort,
            multiSelectComponent: IpInputPortMulti
        }, 
        {
            name: '',
            label: formatMessage({ id: 'policies.selector.device' }),
            placeholder: '',
            value: new AclOrigin_Resource({
                type: AclOrigin_ResourceType.DEVICE,
                value: ''
            }),
            selectComponent: MachineSelectorPort,
            multiSelectComponent: MachineSelectorPortMulti
        },
        {
            name: '',
            label: formatMessage({ id: 'policies.selector.deviceGroup' }),
            placeholder: '',
            value: new AclOrigin_Resource({
                type: AclOrigin_ResourceType.DEVICE_GROUP,
                value: ''
            }),
            selectComponent: SingleSelectorPort,
            multiSelectComponent: SingleSelectorPortMulti,
            optList: props.machineGroups.map(item => {
                return {
                    value: item.name,
                    label: item.alias ? `${item.alias}(${item.name})` : item.name
                }
            })
        },
        {
            name: '',
            label: formatMessage({ id: 'policies.label.resourceGroup' }),
            placeholder: '',
            value: new AclOrigin_Resource({
                type: AclOrigin_ResourceType.IP_GROUP,
                value: ''
            }),
            selectComponent: SingleSelectorPort,
            multiSelectComponent: SingleSelectorPortMulti,
            optList: hostList
        }, {
            name: '',
            label: formatMessage({ id: 'policies.label.user' }),
            placeholder: '',
            value: new AclOrigin_Resource({
                type: AclOrigin_ResourceType.USER,
                value: ''
            }),
            selectComponent: UserSelectorPort,
            multiSelectComponent: UserSelectorPortMulti
        }, {
            name: '',
            label: formatMessage({ id: 'policies.label.userGroup' }),
            placeholder: '',
            value: new AclOrigin_Resource({
                type: AclOrigin_ResourceType.USER_GROUP,
                value: ''
            }),
            optList: userGroups,
            selectComponent: SingleSelectorPort,
            multiSelectComponent: SingleSelectorPortMulti,
        }, {
            name: '',
            label: formatMessage({ id: 'policies.label.builtinUserGroup' }),
            placeholder: '',
            value: new AclOrigin_Resource({
                type: AclOrigin_ResourceType.USER_AUTO_GROUP,
                value: ''
            }),
            optList: [{
                value: 'autogroup:self',
                label: formatMessage({ id: 'policies.builtinGroup.self' })
            }, {
                value: 'autogroup:members',
                label: formatMessage({ id: 'policies.builtinGroup.members' })
            }
        ],
            selectComponent: SingleSelectorPort,
            multiSelectComponent: SingleSelectorPortMulti,

        },
        {
            name: '',
            label: formatMessage({ id: 'policies.label.tag' }),
            placeholder: '',
            value: new AclOrigin_Resource({
                type: AclOrigin_ResourceType.TAG,
                value: ''
            }),
            optList: tags,
            selectComponent: SingleSelectorPort,
            multiSelectComponent: SingleSelectorPortMulti,
        }];
    }

    return <><Modal
        title={formatMessage({ id: 'policies.addPolicy' })}
        visible={true}
        onOk={handleOk}
        onCancel={props.close}
        width={1080}
        okButtonProps={{ loading, disabled: src.length == 0 || dst.length == 0 }}
        closeOnEsc={true}

        maskClosable={false}
    >
        <Form getFormApi={SetFormApi} initValues={{
            action: 'accept',
            disabled: 0,
            priority: 1,
            aclGroups: aclGroups.map(item => item.id + '')
        }} render={({ formState: _formState }) => <>
            <Row gutter={20}>
                <Col span={12}>
                    <Input field="name" validate={value => {
                        if (!value) {
                            return formatMessage({ id: 'policies.new.nameRequired' });
                        }
                        return '';
                    }} label={formatMessage({ id: 'policies.new.policyName' })} trigger={'blur'}></Input>
                </Col>
                <Col span={6}>
                    <InputNumber field="priority" min={1} max={100} step={1} label={<>{formatMessage({ id: 'policies.new.priority' })}&nbsp;<Popover content={<div className='p10'>{formatMessage({ id: 'policies.new.priorityTooltip' })}</div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover></>}></InputNumber>
                </Col>
                <Col span={6}>
                    <RadioGroup field="disabled" label={formatMessage({ id: 'policies.new.status' })}>
                        <Radio value={0}>{formatMessage({ id: 'policies.new.enabled' })}</Radio>
                        <Radio value={1}>{formatMessage({ id: 'policies.new.disabled' })}</Radio>
                    </RadioGroup>
                </Col>
            </Row>
            <Row gutter={20}>
                <Col span={12}>
                    <Input field="description" label={formatMessage({ id: 'policies.new.description' })}></Input>
                </Col>

                <Col span={12}>
                    <TreeSelect
                        field="aclGroups"
                        label={formatMessage({ id: 'policies.new.policyGroup' })}
                        // zIndex={9999}
                        style={{ width: '100%' }}
                        expandAll
                        treeData={
                            props.groupTreeData
                        }
                        onChange={(val) => {
                            if (val) {
                                let arr = val as string[];
                                let groups: AclGroup[] = [];
                                let mapGroupData = props.mapGroupData;
                                arr.forEach(item => {
                                    let group = mapGroupData.get(item);
                                    if (group) {
                                        groups.push(group);
                                    }
                                })
                                setAclGroups(groups);
                            }
                        }}
                        renderFullLabel={({ className,
                            onExpand: _onExpand,
                            data,
                            onCheck,
                            checkStatus,
                            expandIcon, }) => {
                            const { label, value } = data;
                            let aclGroup = props.mapGroupData.get(value as string);

                            const isLeaf = !(data.children && data.children.length);

                            let labelEle = <></>;
                            if (!aclGroup) {
                                labelEle = <Text ellipsis={{ showTooltip: true }} >{label}</Text>
                            } else {
                                labelEle = <Space>{aclGroup.type == GroupType.GROUP_DYNAMIC ?
                                    <Tooltip content={formatMessage({ id: 'policies.tooltip.dynamicGroup' })}>
                                        <IconApartment /></Tooltip>
                                    : ''}
                                    <Tooltip content={aclGroup.name}>
                                        <Text>{aclGroup.alias}</Text>
                                    </Tooltip></Space>
                            }

                            return (
                                <li
                                    className={className}
                                    role="treeitem"
                                    onClick={aclGroup?.type == GroupType.GROUP_DYNAMIC ? undefined : onCheck}
                                // onClick={isLeaf ? onCheck : onExpand}
                                >
                                    {isLeaf ? null : expandIcon}
                                    <div onClick={aclGroup?.type == GroupType.GROUP_DYNAMIC ? undefined : onCheck} role='checkbox' tabIndex={0} aria-checked={checkStatus.checked}>
                                        <Checkbox
                                            indeterminate={checkStatus.halfChecked}
                                            checked={checkStatus.checked}
                                            disabled={aclGroup?.type == GroupType.GROUP_DYNAMIC}
                                            style={{ marginRight: 8 }}
                                        />
                                    </div>
                                    <span>{labelEle}</span>
                                </li>
                            );
                        }}

                        multiple
                        filterTreeNode
                        showFilteredOnly
                        checkRelation="unRelated"
                        dropdownStyle={{ maxHeight: 300, overflow: 'auto' }}
                    ></TreeSelect>
                </Col>
            </Row>

            <Divider className="mb10" />

            <div style={{ display: 'flex', width: '100%' }}>
                <div style={{ flexGrow: 1, width: 295 }}>
                    <MulteSelect
                        getDisplayValue={props.getSrcDisplay}
                        label={formatMessage({ id: 'policies.table.source' })}
                        styles={{
                            paddingTop: 12,
                            paddingBottom: 20
                        }}
                        selectItems={[{
                            name: '',
                            label: formatMessage({ id: 'policies.selector.all' }),
                            placeholder: '',
                            value: new AclOrigin_Resource({
                                type: AclOrigin_ResourceType.ALL,
                                value: '*'
                            }),

                            selectComponent: AllSelector
                        }, {
                            name: '',
                            label: 'IP',
                            placeholder: '',
                            value: new AclOrigin_Resource({
                                type: AclOrigin_ResourceType.IP,
                                value: ''
                            }),
                            selectComponent: IpInput,
                            multiSelectComponent: IpInputMulti
                        },
                        {
                            name: '',
                            label: formatMessage({ id: 'policies.selector.device' }),
                            placeholder: '',
                            value: new AclOrigin_Resource({
                                type: AclOrigin_ResourceType.DEVICE,
                                value: ''
                            }),
                            selectComponent: MachineSelector,
                            multiSelectComponent: MachineSelectorMulti
                        },
                        {
                            name: '',
                            label: formatMessage({ id: 'policies.selector.deviceGroup' }),
                            placeholder: '',
                            value: new AclOrigin_Resource({
                                type: AclOrigin_ResourceType.DEVICE_GROUP,
                                value: ''
                            }),
                            selectComponent: SingleSelector,
                            multiSelectComponent: SingleSelectorMulti,
                            optList: props.machineGroups.map(item => {
                                return {
                                    value: item.name,
                                    label: item.alias ? `${item.alias}(${item.name})` : item.name
                                }
                            })
                        },
                        {
                            name: '',
                            label: formatMessage({ id: 'policies.label.user' }),
                            placeholder: '',
                            value: new AclOrigin_Resource({
                                type: AclOrigin_ResourceType.USER,
                                value: ''
                            }),
                            selectComponent: UserSelector,
                            multiSelectComponent: UserSelectorMulti
                        }, {
                            name: '',
                            label: formatMessage({ id: 'policies.label.userGroup' }),
                            placeholder: '',
                            value: new AclOrigin_Resource({
                                type: AclOrigin_ResourceType.USER_GROUP,
                                value: ''
                            }),
                            optList: userGroups,
                            selectComponent: SingleSelector,
                            multiSelectComponent: SingleSelectorMulti
                        }, {
                            name: '',
                            label: formatMessage({ id: 'policies.label.builtinUserGroup' }),
                            placeholder: '',
                            value: new AclOrigin_Resource({
                                type: AclOrigin_ResourceType.USER_AUTO_GROUP,
                                value: ''
                            }),
                            optList: [{
                                value: 'autogroup:self',
                                label: formatMessage({ id: 'policies.builtinGroup.self' })
                            }, {
                                value: 'autogroup:members',
                                label: formatMessage({ id: 'policies.builtinGroup.members' })
                            }
                        ],
                            selectComponent: SingleSelector,
                            multiSelectComponent: SingleSelectorMulti

                        },
                        {
                            name: '',
                            label: formatMessage({ id: 'policies.label.tag' }),
                            placeholder: '',
                            value: new AclOrigin_Resource({
                                type: AclOrigin_ResourceType.TAG,
                                value: ''
                            }),
                            optList: tags,
                            selectComponent: SingleSelector,
                            multiSelectComponent: SingleSelectorMulti
                        }]}

                        value={src}
                        existValues={src}
                        onChange={(val: AclOrigin_Resource[], _selectItem: SelectItemParam) => {
                            setSrc(val);
                        }}

                    ></MulteSelect>
                </div>
                <div style={{
                    display: 'flex', alignItems: 'center', flexDirection: 'column',
                    borderLeft: '1px solid var(--semi-color-border)'
                    , borderRight: '1px solid var(--semi-color-border)'
                    , paddingLeft: 10, paddingRight: 10,
                    marginLeft: 10, marginRight: 10
                }}  >
                    <div style={{ lineHeight: '52px', width: '100%', textAlign: 'center', borderBottom: '1px solid var(--semi-color-border)' }}>{formatMessage({ id: 'policies.table.action' })} <Popover content={<div className='p10'>{formatMessage({ id: 'policies.table.actionTooltip' })}</div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover></div>
                    <div
                        style={{
                            display: 'flex',
                            alignItems: 'center',
                            marginLeft: 10,
                            marginRight: 10,
                            flexGrow: 1,
                        }}
                    >
                        <Select field="action" noLabel style={{ width: 80 }}>
                            <Select.Option value="accept">{formatMessage({ id: 'policies.action.allow' })}</Select.Option>
                            <Select.Option value="reject">{formatMessage({ id: 'policies.action.deny' })}</Select.Option>
                        </Select>
                    </div>
                </div>
                <div style={{ flexGrow: 1, width: 295 }}>
                    <MulteSelect
                        getDisplayValue={props.getDstDisplay}
                        label={formatMessage({ id: 'policies.table.target' })}
                        styles={{
                            paddingTop: 12,
                            paddingBottom: 20
                        }}
                        selectItems={dstItems}

                        value={dst}
                        existValues={dst}
                        onChange={(val: AclOrigin_Resource[], _selectItem: SelectItemParam) => {
                            setDst(val);
                        }}

                    ></MulteSelect>
                </div>
            </div>
        </>}>
        </Form>
    </Modal></>
}

export default Index;
