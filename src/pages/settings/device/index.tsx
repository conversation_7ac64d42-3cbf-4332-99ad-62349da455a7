import React, { useContext, useEffect, useState } from 'react'
import { Typography, Row, Switch, Col, Form, Divider, Button, Space, Popover } from '@douyinfe/semi-ui';

import { Flynet } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/flynets_pb";
const { Title, Paragraph, Text } = Typography;
import styles from './index.module.scss'
import { disableMachineAuthorization, enableMachineAuthorization, getFlynet, setDefaultKeyExpireDays, setMeshEnabled, setRdpSettings } from '@/services/flynet';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral'
import { useLocale } from '@/locales';
// import { IconMore, IconHelpCircle } from '@douyinfe/semi-icons';


import { FormApi } from '@douyinfe/semi-ui/lib/es/form';

const Index: React.FC = () => {
    const { formatMessage } = useLocale();
    const flynetGeneral = useContext(FlynetGeneralContext);
    const [flynet, setFlynet] = useState<Flynet>();
    // 手动审批新设备loading Flag
    const [authorizedLoading, setAuthorizedLoading] = useState(false);
    const [meshLoading, setMeshLoading] = useState(false);
    // // 默认密钥过期天数loading Flag
    // const [keyExpireDaysLoading, setKeyExpireDaysLoading] = useState(false);

    // const [defaultKeyMinExpireDays, setDefaultKeyMinExpireDays] = useState(1);
    // const [defaultKeyMaxExpireDays, setDefaultKeyMaxExpireDays] = useState(180);

    // const [keyExpireFormApi, SetKeyExpireFormApi] = useState<FormApi<{ defaultKeyExpireDays: number }>>()


    const queryFlynet = async () => {
        let res = await getFlynet(flynetGeneral.id);
        if (res) {
            setFlynet(res.flynet)
            // keyExpireFormApi?.setValue('defaultKeyExpireDays', res.flynet.defaultKeyExpireDays)

            // if (res.constraint) {
            //     setDefaultKeyMinExpireDays(res.constraint.defaultKeyMinExpireDays);
            //     setDefaultKeyMaxExpireDays(res.constraint.defaultKeyMaxExpireDays);
            // }
        }
    }

    useEffect(() => {
        queryFlynet()
    }, [])


    // 手动审批新设备选项改变
    const handleMachineAuthorizationChange = (checked: boolean) => {
        if (!flynet) {
            return;
        }
        setAuthorizedLoading(true)
        if (checked) {
            enableMachineAuthorization(flynet?.id)
                .then(() => { setFlynet(Object.assign({}, flynet, { machineAuthorizationEnabled: true })) })
                .finally(() => setAuthorizedLoading(false))
        } else {
            disableMachineAuthorization(flynet?.id).then(() => { setFlynet(Object.assign({}, flynet, { machineAuthorizationEnabled: false })) }).finally(() => setAuthorizedLoading(false))
        }
    }

    // Mesh模式选项改变
    const handleMeshChange = (checked: boolean) => {
        if (!flynet) {
            return;
        }

        setMeshLoading(true)
        setMeshEnabled(flynet?.id, checked).then(() => {
            setFlynet(Object.assign({}, flynet, { meshEnabled: checked }))
            setMeshLoading(false)
        })

    }

    // const handleKeyExpireSubmit = (values: { defaultKeyExpireDays: number }) => {
    //     if (!flynet) {
    //         return;
    //     }
    //     setKeyExpireDaysLoading(true)
    //     setDefaultKeyExpireDays(flynet?.id, values.defaultKeyExpireDays)
    //         .then(() => { setFlynet(Object.assign({}, flynet, { setDefaultKeyExpireDays: values.defaultKeyExpireDays })) })
    //         .finally(() => setKeyExpireDaysLoading(false))
    // }


    const [rdpLoading, setRdpLoading] = useState(false)
    const [rdpFormApi, SetRdpFormApi] = useState<FormApi<{
        rdpAutoGrant: boolean,
        rdpEnabled: boolean,
    }>>()

    const handleRdpSubmit = (values: {
        rdpAutoGrant: boolean,
        rdpEnabled: boolean,
    }) => {
        if (!flynet) {
            return;
        }
        setRdpLoading(true)
        setRdpSettings(flynet?.id, values.rdpAutoGrant, values.rdpEnabled)
            .then(() => { setFlynet(Object.assign({}, flynet, { rdpAutoGrant: values.rdpAutoGrant, rdpEnabled: values.rdpEnabled })) })
            .finally(() => setRdpLoading(false))
    }
    return <><div className='settings-page'>
        <Title heading={3} className='mb10'>{formatMessage({ id: 'settings.device.title' })}</Title>
        <Paragraph className='mb40' type='tertiary'>{formatMessage({ id: 'settings.device.description' })}</Paragraph>

        <Title heading={4} className="mb2">{formatMessage({ id: 'settings.device.approval.title' })}</Title>
        <Paragraph className='mb20' type='tertiary'>{formatMessage({ id: 'settings.device.approval.description' })}</Paragraph>
        <Row className='mb20'>
            <Col span={4}><div className={styles.colFormItem}>
                <Switch
                    loading={authorizedLoading}
                    checked={flynet?.machineAuthorizationEnabled}
                    onChange={handleMachineAuthorizationChange}
                />
            </div></Col>
            <Col span={20}><Paragraph>{formatMessage({ id: 'settings.device.approval.manual' })}</Paragraph></Col>
        </Row>
        <Divider className='mb40' />


        <Title heading={4} className="mb2">{formatMessage({ id: 'settings.device.mesh.title' })}</Title>
        <Paragraph className='mb20' type='tertiary'>
        {formatMessage({ id: 'settings.device.mesh.description' })}
        </Paragraph>
        <Row className='mb20'>
            <Col span={4}><div className={styles.colFormItem}>
                <Switch
                    loading={meshLoading}
                    checked={flynet?.meshEnabled}
                    onChange={handleMeshChange}
                />
            </div></Col>
            <Col span={20}><Paragraph>{formatMessage({ id: 'settings.device.mesh.enable' })}</Paragraph></Col>
        </Row>
        {/* <Divider className='mb40' /> */}
        {/* 
        <Title heading={4} className="mb2">{formatMessage({ id: 'settings.device.keyExpiry.title' })}</Title>
        <Paragraph className='mb20' type='tertiary'>{formatMessage({ id: 'settings.device.keyExpiry.description' })}</Paragraph>

        <div className='mb20'>
            {flynet ? <Form onSubmit={handleKeyExpireSubmit} getFormApi={SetKeyExpireFormApi} initValues={{ defaultKeyExpireDays: flynet?.defaultKeyExpireDays }}>

                <Form.InputNumber labelPosition='left'
                    min={defaultKeyMinExpireDays}
                    max={defaultKeyMaxExpireDays}
                    field='defaultKeyExpireDays' label='天数' extraText={`必须介于 ${defaultKeyMinExpireDays} 到 ${defaultKeyMaxExpireDays} 天之间。`}></Form.InputNumber>
                <Space>
                    <Button htmlType="reset">重置</Button>
                    <Button type="primary" theme='solid' loading={keyExpireDaysLoading} htmlType="submit">提交</Button>
                </Space>
            </Form> : ''}

        </div> */}
        <Divider className='mb40' />
        <div className='mb40'>
            <Title heading={4} className="mb2">{formatMessage({ id: 'settings.device.remoteDesktop.title' })}</Title>
            <Paragraph className='mb20' type='tertiary'>{formatMessage({ id: 'settings.device.remoteDesktop.description' })}</Paragraph>
            {flynet && <Form onSubmit={handleRdpSubmit} getFormApi={SetRdpFormApi}
                initValues={{
                    rdpAutoGrant: flynet?.rdpAutoGrant,
                    rdpEnabled: flynet?.rdpEnabled
                }}
            >{({ formState, values, formApi }) => (<>

                <Form.Switch labelWidth={100} labelPosition='left' label={formatMessage({ id: 'settings.device.remoteDesktop.enable' })} field='rdpEnabled' onChange={(checked) => {
                    if (!checked) {
                        rdpFormApi?.setValue('rdpAutoGrant', false);
                    }
                }} />
                <Form.Switch labelWidth={100} labelPosition='left' label={formatMessage({ id: 'settings.device.remoteDesktop.autoGrant' })}
                    extraTextPosition='middle' extraText={formatMessage({ id: 'settings.device.remoteDesktop.autoGrantHint' })}
                    field='rdpAutoGrant' disabled={!values.rdpEnabled} />

                <div></div>
                <Space>
                    <Button htmlType="reset">重置</Button>
                    <Button type="primary" theme='solid' loading={rdpLoading} htmlType="submit">提交</Button>
                </Space>
            </>)}

            </Form>}
        </div>

    </div></>
}

export default Index