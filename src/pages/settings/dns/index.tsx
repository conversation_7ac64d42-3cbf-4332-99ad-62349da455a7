import React, { useEffect, useContext, useState } from "react";
import { Popover, Typography, Notification, Dropdown, Tag, Row, Col, Switch, List, Descriptions, Nav, Collapsible, Banner, Layout, Divider, Popconfirm } from '@douyinfe/semi-ui';
import { IconInfoCircle, IconMore, IconLock, IconGlobeStroke, IconChevronDown, IconChevronRight } from '@douyinfe/semi-icons';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { useLocale } from '@/locales';
import { flylayerClient } from '@/services/core';
import { DNSConfig, Routes, Record } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/dns_pb';
import styles from './index.module.scss';
import Button from "@douyinfe/semi-ui/lib/es/button/Button";
import DisableDNS from "./disable-dns";
import DisableHttps from "./disable-https";
import AddNameserver from "./add-nameserver";
import EditNameserver from "./edit-nameserver";
import AddSearchdomain from "./add-searchdomain";
import EditSearchdomain from "./edit-searchdomain";
import IconMagicDns from "@/assets/icon/magic-dns.svg";
import IconSplitDns from "@/assets/icon/split-dns.svg";

const { Sider, Content } = Layout;
import { DOMAIN_STATIC_IP } from "@/constants";
const { Title, Paragraph, Text } = Typography;

const Index: React.FC = () => {
    const { formatMessage } = useLocale();
    // 页面hash
    const [hash, setHash] = useState(location.hash ? location.hash : '#flynet-name');


    const flynet = useContext(FlynetGeneralContext);
    const [dnsConfig, setDnsConfig] = useState<DNSConfig>();

    // 禁用分配设备域名对话框是否显示
    const [disableDNSVisible, setDisableDNSVisible] = useState(false);

    // 禁用HTTPS对话框是否显示
    const [disableHttpsVisible, setDisableHttpsVisible] = useState(false);

    // 添加DNS服务器对话框是否显示
    const [addNameserverVisible, setAddNameserverVisible] = useState(false);

    // 编辑DNS服务器对话框是否显示
    const [editNameserverVisible, setEditNameserverVisible] = useState(false);
    // 编辑DNS服务器数据
    const [editNameserverData, setEditNameserverData] = useState({
        ip: '',
        domain: '',
        useSplitDNS: false
    });

    // 添加自定义域名解析对话框是否显示
    const [addSearchdomainVisible, setAddSearchdomainVisible] = useState(false);
    // 编辑自定义域名解析对话框是否显示
    const [editSearchdomainVisible, setEditSearchdomainVisible] = useState(false);
    // 编辑自定义域名解析数据
    const [editSearchdomainData, setEditSearchdomainData] = useState<{
        domain: string,
        extraRecords: {
            name: string,
            value: string,
            type: string,
            sys:boolean
        }[]
    }>({
        domain: '',
        extraRecords: []
    });


    // 限定域数据
    const [splitRoutes, setSplitRoutes] = useState<{
        domain: string,
        routes: Array<string>
    }[]>([]);

    // 自定义域名解析数据
    const [searchDomains, setSearchDomains] = useState<{
        domain: string,
        extraRecords: Array<Record>
    }[]>([]);

    const SearchdomainItem: React.FC<{
        domain: string,
        extraRecords: Array<Record>,
        handleDeleteSearchDomain: (domain: string) => void
    }> = (props) => {
        const { domain, extraRecords, handleDeleteSearchDomain } = props;
        // 是否折叠
        const [isCollapsible, setIsCollapsible] = useState(false);

        let hasSys = false;
        extraRecords.forEach((record) => {
            if (record.sys) {
                hasSys = true;
            }
        })

        return <List.Item key={domain} className={styles.ipListItem}>
            <div style={{ display: 'flex', justifyContent: 'space-between', width: '100%', alignItems: 'center' }}>
                <span style={{ display: 'flex', alignItems: 'center', flexGrow: 1, paddingRight: '20px' }}
                    className={styles.toggleCollapsible}
                ><span style={{ cursor: 'pointer', display: 'flex', alignItems: 'center', color: 'var(--semi-color-text-1)' }} onClick={() => setIsCollapsible(!isCollapsible)}>{isCollapsible ? <IconChevronDown /> : <IconChevronRight />}&nbsp;{domain}</span></span>
                
                <Dropdown
                    trigger={'hover'}
                    position={'bottomLeft'}
                    render={
                        <Dropdown.Menu>
                            <Dropdown.Item
                                onClick={() => {
                                    setEditSearchdomainData({
                                        domain,
                                        extraRecords: extraRecords.map(record => {
                                            return {
                                                name: record.name,
                                                value: record.value,
                                                type: record.type,
                                                sys: record.sys
                                            }
                                        })
                                    })
                                    setEditSearchdomainVisible(true)
                                }}
                            > {formatMessage({ id: 'components.common.edit' })}</Dropdown.Item>
                            <Dropdown.Divider></Dropdown.Divider>
                            <Popconfirm
                                title={formatMessage({ id: 'components.common.confirmDelete' })}
                                content={formatMessage({ id: 'components.common.irreversibleAction' })}
                                onConfirm={() => handleDeleteSearchDomain(domain)}
                                disabled={hasSys}
                            ><Dropdown.Item type={hasSys ? 'tertiary': 'danger'}>{formatMessage({ id: 'components.common.delete' })}</Dropdown.Item>
                            </Popconfirm>

                        </Dropdown.Menu>
                    }
                >
                    <IconMore />
                </Dropdown>
            </div>
            <Collapsible isOpen={isCollapsible}>
                <Descriptions className={styles.searchdomainDetail} size="small">
                    {extraRecords.map((record: Record, index: number) => {
                        return <Descriptions.Item key={index}
                            style={{
                                opacity: record.sys ? 0.3 : 1,
                            }} 
                            itemKey={`${record.name}`}>
                            <Text style={{ marginRight: 100 }}>
                                {record.type === 'A' ? formatMessage({ id: 'settings.dns.recordType.a' }) : ''}
                                {record.type === 'CNAME' ? 'CNAME' : ''}
                            </Text>
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{record.value}
                        </Descriptions.Item>
                    })}
                </Descriptions>
            </Collapsible>
        </List.Item>
    }

    // hash 改变
    const handleRouteChange = (hash: string) => {
        setHash(hash);
        location.hash = hash;
        // scrollToHash(hash);
    }

    // 跳转到hash所处页面位置
    const scrollToHash = (hash: string) => {
        if (hash) {
            const ele = document.querySelector(hash);

            const bounds = ele?.getBoundingClientRect();
            if (bounds) {
                window.scrollTo({
                    top: bounds.top + window.scrollY,
                    behavior: 'smooth'
                })
            }

        }
    }

    useEffect(() => {
        scrollToHash(location.hash);
    }, [])


    // 查询DNS配置
    const queryDNSConfig = () => {
        flylayerClient.getDNSConfig({
            flynetId: flynet.id,
        }).then(res => {
            if (res.config) {
                setDnsConfig(res.config)
                setSearchDomains([]);
                if (res.config?.routes) {
                    let staticDomains: Array<string> = [];
                    const config: DNSConfig = res.config;

                    // 限定域数据
                    let splitRoutes: Array<{
                        domain: string,
                        routes: Array<string>
                    }> = []
                    // 自定义域名解析数据
                    let searchDomains: Array<{
                        domain: string,
                        extraRecords: Array<Record>
                    }> = [];



                    Object.keys(res.config?.routes).forEach(domain => {
                        splitRoutes.push({
                            domain,
                            routes: getRoutes(domain, config)
                        })
                    })

                    config.extraRecords.forEach(record => {
                        let find = false;
                        searchDomains.forEach(searchDomain => {
                            if (record.domain === searchDomain.domain) {
                                searchDomain.extraRecords.push(record);
                                find = true;
                            }
                        });
                        if (!find) {
                            searchDomains.push({
                                domain: record.domain,
                                extraRecords: [record]
                            })
                        }
                    })

                    // 限定域数据
                    setSplitRoutes(splitRoutes);
                    // 自定义域名解析数据
                    setSearchDomains(searchDomains);


                }
            }
        })

    };

    useEffect(() => {
        queryDNSConfig()
    }, [])

    // 启用分配设备域名是否正在加载中
    const [enablePrivateDNSLoading, setEnablePrivateDNSLoading] = useState(false);
    // 启用分配设备域名
    const handleEnablePrivateDNS = () => {
        setEnablePrivateDNSLoading(true);
        flylayerClient.setDNSConfig({
            flynetId: flynet.id,
            config: { ...dnsConfig, magicDns: true }
        }).then(res => {
            if (res.config) {
                setDnsConfig(res.config)
            }
            Notification.success({ content: formatMessage({ id: 'settings.dns.deviceDomain.enableSuccess' }), position: "bottomRight" })
        }).catch(() => {
            Notification.error({ content: formatMessage({ id: 'settings.dns.deviceDomain.enableFailed' }), position: "bottomRight" })
        }).finally(() => {
            setEnablePrivateDNSLoading(false);
        })
    }

    // 启用HTTPS是否正在加载中
    const [enableHttpsLoading, setEnableHttpsLoading] = useState(false);
    // 启用HTTPS
    const handleEnableHttps = () => {
        if (dnsConfig && !dnsConfig.magicDns) {
            Notification.error({ content: formatMessage({ id: 'settings.dns.httpsCerts.enablePrerequisite' }), position: "bottomRight" })
            return;
        }
        setEnableHttpsLoading(true);
        flylayerClient.setDNSConfig({
            flynetId: flynet.id,
            config: { ...dnsConfig, httpsCerts: true }
        }).then(res => {
            if (res.config) {
                setDnsConfig(res.config)
            }
            Notification.success({ content: formatMessage({ id: 'settings.dns.httpsCerts.enableSuccess' }), position: "bottomRight" })
        }).catch(() => {
            Notification.error({ content: formatMessage({ id: 'settings.dns.httpsCerts.enableFailed' }), position: "bottomRight" })
        }).finally(() => {
            setEnableHttpsLoading(false);
        })
    }

    const getRoutes = (domain: string, config: DNSConfig): string[] => {
        let routes = config.routes[domain];
        if (routes) {
            return routes.routes
        }
        return [];
    }

    // 删除全局DNS服务器
    const handleDeleteGlobalNameserver = (nameserver: string) => {
        let config = {
            ...dnsConfig,
            nameservers: dnsConfig?.nameservers?.filter(item => item !== nameserver),
        };

        flylayerClient.setDNSConfig({
            flynetId: flynet.id,
            config: config
        }).then(() => {

            queryDNSConfig();
            Notification.success({ content: formatMessage({ id: 'settings.dns.dnsServers.deleteSuccess' }), position: "bottomRight" })
        }).catch((err) => {
            console.error(err);
            Notification.error({ content: formatMessage({ id: 'settings.dns.dnsServers.deleteFailed' }), position: "bottomRight" })
        })
    }

    // 删除DNS服务器
    const handleDeleteNameserver = (domain: string, ip: string) => {

        let routers = new Map<string, Routes>();
        if (dnsConfig?.routes) {
            Object.keys(dnsConfig?.routes).forEach(key => {
                if (key != domain) {
                    routers.set(key, dnsConfig?.routes[key]);
                } else {
                    let routes = dnsConfig?.routes[key];
                    if (routes) {
                        routes.routes = routes.routes.filter(item => item !== ip);
                        if (routes.routes.length > 0) {
                            routers.set(key, routes);
                        } else {
                            routers.delete(key);
                            delete dnsConfig.routes[key];
                        }
                    }
                }
            })
        }

        let config = {
            ...dnsConfig,
            routers: routers,
        };

        flylayerClient.setDNSConfig({
            flynetId: flynet.id,
            config: config
        }).then(() => {
            queryDNSConfig();
            Notification.success({ content: formatMessage({ id: 'settings.dns.dnsServers.deleteSuccess' }), position: "bottomRight" })
        }).catch((err) => {
            console.error(err);
            Notification.error({ content: formatMessage({ id: 'settings.dns.dnsServers.deleteFailed' }), position: "bottomRight" })
        })
    }

    // 覆盖本地DNS是否正在加载中
    const [overrideLocalDnsLoading, setOverrideLocalDnsLoading] = useState(false);
    // 覆盖本地DNS
    const handleOverrideLocalDnsChange = (checked: boolean) => {
        setOverrideLocalDnsLoading(true);
        flylayerClient.setDNSConfig({
            flynetId: flynet.id,
            config: { ...dnsConfig, overrideLocalDns: checked }
        }).then(res => {
            if (res.config) {
                setDnsConfig(res.config)
            }
            Notification.success({ content: formatMessage({ id: 'settings.dns.overrideLocalDns.success' }), position: "bottomRight" })
        }).catch(() => {
            Notification.error({ content: formatMessage({ id: 'settings.dns.overrideLocalDns.failed' }), position: "bottomRight" })
        }).finally(() => {
            setOverrideLocalDnsLoading(false);
        })
    }

    // 删除自定义域名解析
    const handleDeleteSearchDomain = (domain: string) => {

        let config = {
            ...dnsConfig,
            routers: dnsConfig?.routes,
            extraRecords: dnsConfig?.extraRecords.filter(item => item.domain !== domain || item.sys),
        };

        flylayerClient.setDNSConfig({
            flynetId: flynet.id,
            config: config
        }).then(() => {
            queryDNSConfig();
            Notification.success({ content: formatMessage({ id: 'settings.dns.customDomain.deleteSuccess' }), position: "bottomRight" })
        }).catch((err) => {
            console.error(err);
            Notification.error({ content: formatMessage({ id: 'settings.dns.customDomain.deleteFailed' }), position: "bottomRight" })
        })
    }

    return <>
        <div className='settings-page'>
            <div className={styles.titleHash}>
                <a id="dns" href="#dns"></a>
            </div>
            <Title heading={3} className="mb10">{formatMessage({ id: 'settings.dns.title' })}</Title>
            <Paragraph type='tertiary'>{formatMessage({ id: 'settings.dns.description' })}</Paragraph>

            {/* <div className={styles.hash}>
                <a id="flynet-name" href="#flynet-name"></a>
            </div>
            <section className="mb40">
                <Title heading={4} className="mb2">{formatMessage({ id: 'settings.dns.domainNameTitle' })}</Title>
                <Paragraph type='tertiary' className='mb20'>{formatMessage({ id: 'settings.dns.domainNameDescription' })}</Paragraph>
                <Paragraph className={styles.tailnetName} copyable  >{dnsConfig?.magicDnsSuffix}</Paragraph>
                <Button disabled>{formatMessage({ id: 'settings.dns.modifyDomainName' })}</Button>
            </section>

            <Divider />
            <div className={styles.hash}>
                <a id="flynet-dns" href="#flynet-dns"></a>
            </div>
            <section className="mb40">
                <Title heading={4} className="mb2">{formatMessage({ id: 'settings.dns.deviceDomain.title' })}</Title>
                <Paragraph type='tertiary' className='mb20'>{formatMessage({ id: 'settings.dns.deviceDomain.description' })}</Paragraph>
                {dnsConfig?.magicDns ?
                    <Button onClick={() => setDisableDNSVisible(true)} theme="solid" type="danger">{formatMessage({ id: 'settings.dns.deviceDomain.disable' })}</Button>
                    :
                    <Button onClick={handleEnablePrivateDNS} theme="solid" loading={enablePrivateDNSLoading} >{formatMessage({ id: 'settings.dns.deviceDomain.enable' })}</Button>
                }
            </section>

            <Divider /> */}
            {/* <div className={styles.hash}>
                <a id="https" href="#https"></a>
            </div>
            <section className="mb40">
                <Title heading={4} className="mb2">{formatMessage({ id: 'settings.dns.httpsCerts.title' })}</Title>
                <Paragraph type='tertiary' className='mb20'>{formatMessage({ id: 'settings.dns.httpsCerts.description' })}</Paragraph>
                {dnsConfig?.httpsCerts ?
                    <Button theme="solid" type="danger" onClick={() => { setDisableHttpsVisible(true) }}>{formatMessage({ id: 'settings.dns.httpsCerts.disable' })}</Button>
                    :
                    <Button theme="solid" onClick={handleEnableHttps} loading={enableHttpsLoading}>{formatMessage({ id: 'settings.dns.httpsCerts.enable' })}</Button>
                }
            </section>
            <Divider /> */}
            <div className={styles.hash}>
                <a id="nameserver" href="#nameserver"></a>
            </div>
            <section className="mb40">
                <Title heading={4} className="mb2">{formatMessage({ id: 'settings.dns.dnsServers.title' })}</Title>
                <Paragraph type='tertiary' className='mb40'>{formatMessage({ id: 'settings.dns.dnsServers.description' })}</Paragraph>
                <div className={styles.nameForm}>
                    {dnsConfig?.magicDns ? <>
                        <Title className="mb10" heading={6}>{dnsConfig.magicDnsSuffix} <Tag style={{ marginLeft: 8 }}> <img className={styles.iconMagicDns} src={IconMagicDns}></img>&nbsp;{formatMessage({ id: 'settings.dns.domainName' })}</Tag></Title>
                        <Paragraph className={styles.displayInput}>{DOMAIN_STATIC_IP}<IconLock /></Paragraph>

                    </> : null}
                    {
                        splitRoutes.map((item, index) => {
                            return <div key={index} className={styles.nameForm}>
                                <Title
                                    className={styles.splitDNSTitle}
                                    heading={6}>
                                    <IconGlobeStroke className={styles.splitDNSIcon} /> {item.domain}
                                    <Tag style={{ marginLeft: 8 }}> <img className={styles.iconSplitDNS} src={IconSplitDns}></img>&nbsp;{formatMessage({ id: 'settings.dns.splitDomain' })}</Tag>
                                    {/* <SortBar curIndex={countSplitRoute - 1} total={splitRouteCount} onChange={() => { }}></SortBar> */}
                                </Title>
                                <List className={styles.ipList}>
                                    {item.routes.map(ip => {
                                        return <List.Item key={ip} className={styles.ipListItem}>{ip}<Dropdown
                                            trigger={'hover'}
                                            position={'bottomLeft'}
                                            render={
                                                <Dropdown.Menu>
                                                    <Dropdown.Item onClick={() => {
                                                        setEditNameserverData({
                                                            ip: ip,
                                                            domain: item.domain,
                                                            useSplitDNS: true
                                                        });
                                                        setEditNameserverVisible(true);

                                                    }}>{formatMessage({ id: 'components.common.edit' })}</Dropdown.Item>
                                                    <Dropdown.Divider></Dropdown.Divider>
                                                    <Popconfirm
                                                        title={formatMessage({ id: 'components.common.confirmDelete' })}
                                                        content={formatMessage({ id: 'components.common.irreversibleAction' })}
                                                        onConfirm={() => handleDeleteNameserver(item.domain, ip)}

                                                    ><Dropdown.Item type="danger">{formatMessage({ id: 'components.common.delete' })}</Dropdown.Item>
                                                    </Popconfirm>

                                                </Dropdown.Menu>
                                            }
                                        >
                                            <IconMore />
                                        </Dropdown></List.Item>
                                    })}
                                </List>
                            </div>
                        })
                    }
                    <Row>
                        <Col span={12}> <Title className="mb10" heading={6} >{formatMessage({ id: 'settings.dns.globalDnsServers' })}</Title></Col>
                        <Col span={12}>
                            <div className={styles.overrideLocalDns}>
                                <Popover content={<div className="p10 mw400">{formatMessage({ id: 'settings.dns.overrideLocalDns.tooltip' })}
                                    <br />
                                    {formatMessage({ id: 'settings.dns.overrideLocalDns.tooltipDisabled' })}</div>}>
                                    <IconInfoCircle />
                                </Popover>&nbsp;
                                {formatMessage({ id: 'settings.dns.overrideLocalDns.label' })}&nbsp;<Switch disabled={dnsConfig?.nameservers.length == 0} size="small" loading={overrideLocalDnsLoading} onChange={handleOverrideLocalDnsChange} checked={dnsConfig?.overrideLocalDns}></Switch>
                            </div>
                        </Col>
                    </Row>
                    {dnsConfig?.overrideLocalDns && dnsConfig?.nameservers.length == 0 ? <Banner className={styles.banner} type="info" description={formatMessage({ id: 'settings.dns.globalDnsServers.notSet' })} closeIcon={null} ></Banner> : <List className={styles.ipList}>
                        {!dnsConfig?.overrideLocalDns ? <List.Item className={styles.ipListItem} style={{ color: 'var(--semi-color-text-2)' }}>{formatMessage({ id: 'settings.dns.localDnsSettings' })}</List.Item>
                            : ''}
                        {dnsConfig?.nameservers.map((item, index) => {
                            return <List.Item key={index} className={styles.ipListItem}>
                                {item}

                                <Dropdown
                                    trigger={'hover'}
                                    position={'bottomLeft'}
                                    render={
                                        <Dropdown.Menu>
                                            <Dropdown.Item onClick={() => {
                                                setEditNameserverData({
                                                    ip: item,
                                                    domain: '',
                                                    useSplitDNS: false
                                                });
                                                setEditNameserverVisible(true);
                                            }}>{formatMessage({ id: 'components.common.edit' })}</Dropdown.Item>
                                            <Dropdown.Divider></Dropdown.Divider>
                                            <Popconfirm
                                                title={formatMessage({ id: 'components.common.confirmDelete' })}
                                                content={formatMessage({ id: 'components.common.irreversibleAction' })}
                                                onConfirm={() => handleDeleteGlobalNameserver(item)}
                                            ><Dropdown.Item type="danger">{formatMessage({ id: 'components.common.delete' })}</Dropdown.Item>
                                            </Popconfirm>

                                        </Dropdown.Menu>
                                    }
                                >
                                    <IconMore />
                                </Dropdown>

                            </List.Item>
                        })}
                    </List>}

                    <Button theme="solid" onClick={() => setAddNameserverVisible(true)}>{formatMessage({ id: 'settings.dns.dnsServers.add' })}</Button>
                </div>
            </section>

            <Divider />
            <div className={styles.hash}>
                <a id="searchdomain" href="#searchdomain"></a>
            </div>
            <section className="mb40" style={{ height: '600px' }}>
                <Title heading={4} className="mb2">{formatMessage({ id: 'settings.dns.customDomain.title' })}</Title>
                <Paragraph type='tertiary' className='mb10'>{formatMessage({ id: 'settings.dns.customDomain.description' })}</Paragraph>
                {dnsConfig?.overrideLocalDns || true ? <>{!dnsConfig?.magicDns && searchDomains.length == 0 ? <Banner className={styles.banner} type="info" description={formatMessage({ id: 'settings.dns.customDomain.notSet' })} closeIcon={null} ></Banner> :
                    <List className={styles.ipList}>
                        {dnsConfig?.magicDns ? <>
                            <List.Item className={styles.ipListItem} style={{ color: 'var(--semi-color-text-2)' }}>{dnsConfig.magicDnsSuffix}</List.Item>
                        </> : null}

                        {searchDomains.map((item, index) => {
                            return <SearchdomainItem
                                key={index}
                                domain={item.domain}
                                extraRecords={item.extraRecords}
                                handleDeleteSearchDomain={handleDeleteSearchDomain}
                            ></SearchdomainItem>
                        })}
                    </List>}

                    <Button onClick={() => setAddSearchdomainVisible(true)} theme="solid" >{formatMessage({ id: 'settings.dns.customDomain.add' })}</Button></> : <Banner
                    bordered
                    fullMode={false}
                    type="info"
                    closeIcon={null}
                    description={formatMessage({ id: 'settings.dns.customDomain.enableOverrideRequired' })}
                />}

            </section>
        </div>
        {disableDNSVisible && dnsConfig ? <DisableDNS
            flynetId={flynet.id}
            success={() => {
                queryDNSConfig();
                setDisableDNSVisible(false);
            }}
            close={() => setDisableDNSVisible(false)} record={dnsConfig}></DisableDNS> : null}

        {disableHttpsVisible && dnsConfig ? <DisableHttps
            flynetId={flynet.id}
            success={() => {
                queryDNSConfig();
                setDisableHttpsVisible(false);
            }}
            close={() => setDisableHttpsVisible(false)} record={dnsConfig}></DisableHttps> : null}

        {addNameserverVisible && dnsConfig ? <AddNameserver
            flynetId={flynet.id}
            record={dnsConfig}
            success={() => {
                queryDNSConfig();
                setAddNameserverVisible(false);
            }}
            close={() => setAddNameserverVisible(false)}
        ></AddNameserver> : null}

        {editNameserverVisible && dnsConfig ? <EditNameserver
            flynetId={flynet.id}
            record={dnsConfig}
            success={() => {
                queryDNSConfig();
                setEditNameserverVisible(false);
            }}
            close={() => setEditNameserverVisible(false)}
            data={editNameserverData}
        ></EditNameserver> : null}

        {addSearchdomainVisible && dnsConfig ? <AddSearchdomain
            flynetId={flynet.id}
            record={dnsConfig}
            success={() => {
                queryDNSConfig();
                setAddSearchdomainVisible(false);

            }}
            close={() => setAddSearchdomainVisible(false)} /> : null
        }

        {editSearchdomainVisible && dnsConfig ? <EditSearchdomain
            flynetId={flynet.id}
            record={dnsConfig}
            success={() => {
                queryDNSConfig();
                setEditSearchdomainVisible(false);
            }}
            close={() => setEditSearchdomainVisible(false)}
            data={editSearchdomainData}
        ></EditSearchdomain> : null}
    </>
};

export default Index;
