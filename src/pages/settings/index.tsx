import React, { Suspense, useState } from 'react';
import { Outlet, useLocation, useNavigate } from "react-router-dom";
import { Collapse, Space, Typography, List } from '@douyinfe/semi-ui';

import { Layout, Nav } from '@douyinfe/semi-ui';
import { IconHome, IconApartment, IconGlobe, IconSafe, IconServer } from '@douyinfe/semi-icons';

import styles from './index.module.scss'
import { BASE_PATH } from '@/constants/router';
import { VITE_LOCAL_PAGER_AND_FILTER, VITE_USE_LICENSE } from '@/utils/service';
import { VITE_USE_DEVELOP_FEATURE } from '@/utils/service';
import { useLocale } from '@/locales';
const { Text } = Typography;
const { Sider, Content } = Layout;
const Index: React.FC = () => {
    const { formatMessage } = useLocale();
    const navigate = useNavigate();
    const location = useLocation();
    let pathArr = location.pathname.split('/');

    let initSelectedKey = pathArr[pathArr.length - 1];

    if (initSelectedKey == 'settings') {
        initSelectedKey = 'device';
    }

    const getSubActiveKey = (key: string) => {
        let aKey = 'global';
        if (key == 'device' || key == 'user' || key == 'client' || key == 'service') {
            aKey = 'global';
        } else if (key == 'flynet' || key == 'relay' || key == 'connector' || key == 'dns') {
            aKey = 'network';
        }
        else if (key == 'mfa' || key == 'api' || key == 'sensitive' || key == 'keys') {
            aKey = 'personal';
        } else if (key == 'schema' || key == 'logs' || key == 'datasource' || key == 'application') {
            aKey = 'data';
        } else if (key == 'rights') {
            aKey = 'rights';
        }
        return aKey;
    }

    let initSubActiveKey = getSubActiveKey(initSelectedKey);

    const [activeKey, setActiveKey] = useState(initSelectedKey);
    const [activeSubKey, setActiveSubKey] = useState(initSubActiveKey);

    const doSetKey = (key: string) => {
        setActiveKey(key);
        setActiveSubKey(getSubActiveKey(key));
    }



    return <Layout className={styles.layout}>
        <Sider className={styles.sider}>
            <Collapse accordion defaultActiveKey={activeSubKey} className={styles.collapse}>
                <Collapse.Panel
                    itemKey='global'
                    className={styles.collapsePanel}
                    header={<Space style={{ paddingLeft: 4 }}><IconHome size='large' /><Text>{formatMessage({ id: 'settings.category.system' })}</Text></Space>}
                    key="global"
                >
                    <List className={styles.list}>
                        <List.Item key="device" className={activeKey == 'device' ? styles.active : ''} onClick={() => {
                            doSetKey('device')
                            navigate(`${BASE_PATH}/settings/device`)
                        }}>{formatMessage({ id: 'settings.menu.device' })}</List.Item>
                        <List.Item key="user" className={activeKey == 'user' ? styles.active : ''} onClick={() => {
                            doSetKey('user')
                            navigate(`${BASE_PATH}/settings/user`)
                        }}>{formatMessage({ id: 'settings.menu.user' })}</List.Item>
                        <List.Item key="client" className={activeKey == 'client' ? styles.active : ''} onClick={() => {
                            doSetKey('client')
                            navigate(`${BASE_PATH}/settings/client`)
                        }}>{formatMessage({ id: 'settings.menu.client' })}</List.Item>
                        {VITE_USE_DEVELOP_FEATURE && <List.Item className={activeKey == 'service' ? styles.active : ''} key="service" onClick={() => {
                            doSetKey('service')
                            navigate(`${BASE_PATH}/settings/service`)
                        }}>{formatMessage({ id: 'settings.menu.service' })}</List.Item>}
                    </List>
                </Collapse.Panel>
                <Collapse.Panel
                    itemKey='network'
                    className={styles.collapsePanel}

                    header={<Space style={{ paddingLeft: 4 }}><IconGlobe size='large' /><Text>{formatMessage({ id: 'settings.category.network' })}</Text></Space>}
                    key="network">
                    <List className={styles.list}>
                        <List.Item key="flynet" className={activeKey == 'flynet' ? styles.active : ''} onClick={() => {
                            doSetKey('flynet')
                            navigate(`${BASE_PATH}/settings/flynet`)
                        }}>{formatMessage({ id: 'settings.menu.flynet' })}</List.Item>
                        <List.Item key="relay" className={activeKey == 'relay' ? styles.active : ''} onClick={() => {
                            doSetKey('relay')
                            navigate(`${BASE_PATH}/settings/relay`)
                        }}>{formatMessage({ id: 'settings.menu.relay' })}</List.Item>
                        {VITE_USE_DEVELOP_FEATURE && <List.Item className={activeKey == 'connector' ? styles.active : ''} key="connector" onClick={() => {
                            doSetKey('connector')
                            navigate(`${BASE_PATH}/settings/connector`)
                        }}>{formatMessage({ id: 'settings.menu.connector' })}</List.Item>}
                        <List.Item key="dns" className={activeKey == 'dns' ? styles.active : ''} onClick={() => {
                            doSetKey('dns')
                            navigate(`${BASE_PATH}/settings/dns`)
                        }}>DNS</List.Item>
                    </List>
                </Collapse.Panel>
                <Collapse.Panel
                    itemKey='personal'
                    className={styles.collapsePanel}

                    header={<Space style={{ paddingLeft: 4 }}><IconSafe size='large' /><Text>{formatMessage({ id: 'settings.category.security' })}</Text></Space>}
                    key="personal">
                    <List className={styles.list}>
                        {!VITE_USE_LICENSE && <List.Item key="mfa" className={activeKey == 'mfa' ? styles.active : ''} onClick={() => {
                            doSetKey('mfa')
                            navigate(`${BASE_PATH}/settings/mfa`)
                        }}>MFA</List.Item>}
                        <List.Item key="api" className={activeKey == 'api' ? styles.active : ''} onClick={() => {
                            doSetKey('api')
                            navigate(`${BASE_PATH}/settings/api`)
                        }}>{formatMessage({ id: 'settings.menu.api' })}</List.Item>
                        <List.Item key="sensitive" className={activeKey == 'sensitive' ? styles.active : ''} onClick={() => {
                            doSetKey('sensitive')
                            navigate(`${BASE_PATH}/settings/sensitive`)
                        }}>{formatMessage({ id: 'settings.menu.sensitive' })}</List.Item>
                        <List.Item key="keys" className={activeKey == 'keys' ? styles.active : ''} onClick={() => {
                            doSetKey('keys')
                            navigate(`${BASE_PATH}/settings/keys`)
                        }}>{formatMessage({ id: 'settings.menu.keys' })}</List.Item>
                    </List>
                </Collapse.Panel>
                <Collapse.Panel
                    itemKey='data'
                    className={styles.collapsePanel}

                    header={<Space style={{ paddingLeft: 4 }}><IconServer size='large' /><Text>{formatMessage({ id: 'settings.category.data' })}</Text></Space>}
                    key="data">
                    <List className={styles.list}>
                        <List.Item key="schema" className={activeKey == 'schema' ? styles.active : ''} onClick={() => {
                            doSetKey('schema')
                            navigate(`${BASE_PATH}/settings/schema`)
                        }}>{formatMessage({ id: 'settings.menu.schema' })}</List.Item>
                        <List.Item key="logs" className={activeKey == 'logs' ? styles.active : ''} onClick={() => {
                            doSetKey('logs')
                            navigate(`${BASE_PATH}/settings/logs`)
                        }}>{formatMessage({ id: 'settings.menu.logs' })}</List.Item>
                        {!VITE_LOCAL_PAGER_AND_FILTER && <List.Item key="datasource" className={activeKey == 'datasource' ? styles.active : ''} onClick={() => {
                            doSetKey('datasource')
                            navigate(`${BASE_PATH}/settings/datasource`)
                        }}>{formatMessage({ id: 'settings.menu.datasource' })}</List.Item>}
                        {VITE_LOCAL_PAGER_AND_FILTER && <List.Item key="application" className={activeKey == 'application' ? styles.active : ''} onClick={() => {
                            doSetKey('application')
                            navigate(`${BASE_PATH}/settings/application`)
                        }}>{formatMessage({ id: 'settings.menu.application' })}</List.Item>}
                    </List>
                </Collapse.Panel>
                {VITE_USE_LICENSE && <Collapse.Panel
                    className={styles.collapsePanel}

                    itemKey='rights'
                    header={<Space style={{ paddingLeft: 4 }}><IconApartment size='large' /><Text>{formatMessage({ id: 'settings.category.rights' })}</Text></Space>}
                    key="rights">
                    <List className={styles.list}>
                        <List.Item key="rights" className={activeKey == 'rights' ? styles.active : ''} onClick={() => {
                            doSetKey('rights')
                            navigate(`${BASE_PATH}/settings/rights`)
                        }}>{formatMessage({ id: 'settings.menu.rights' })}</List.Item>
                    </List>
                </Collapse.Panel>}
            </Collapse>

            {/* <Nav
                className={styles.nav}
                selectedKeys={selectedKeys}
            >
                <Nav.Sub itemKey={'global'} text={formatMessage({ id: 'settings.category.system' })} icon={<IconHome />}>
                    <Nav.Item itemKey="device" text={formatMessage({ id: 'settings.menu.device' })} onClick={() => {
                        setSelectedKeys(['device'])
                        navigate(`${BASE_PATH}/settings/device`)
                    }}></Nav.Item>
                    <Nav.Item itemKey="user" text={formatMessage({ id: 'settings.menu.user' })} onClick={() => {
                        setSelectedKeys(['user'])
                        navigate(`${BASE_PATH}/settings/user`)
                    }}></Nav.Item>
                    <Nav.Item itemKey="client" text={formatMessage({ id: 'settings.menu.client' })} onClick={() => {
                        setSelectedKeys(['client'])
                        navigate(`${BASE_PATH}/settings/client`)
                    }}></Nav.Item>
                    {SHOW_NEW_FEATURE && <Nav.Item itemKey="service" text={formatMessage({ id: 'settings.menu.service' })} onClick={() => {
                        setSelectedKeys(['service'])
                        navigate(`${BASE_PATH}/settings/service`)
                    }}></Nav.Item>}
                </Nav.Sub>
           
                <Nav.Sub itemKey="network" text={formatMessage({ id: 'settings.category.network' })} icon={<IconGlobe />}>
                    <Nav.Item itemKey="flynet" text={formatMessage({ id: 'settings.menu.flynet' })} onClick={() => {
                        setSelectedKeys(['flynet'])
                        navigate(`${BASE_PATH}/settings/flynet`)
                    }}></Nav.Item>
                    <Nav.Item itemKey="relay" text={formatMessage({ id: 'settings.menu.relay' })} onClick={() => {
                        setSelectedKeys(['relay'])
                        navigate(`${BASE_PATH}/settings/relay`)
                    }}></Nav.Item>
                    {SHOW_NEW_FEATURE && <Nav.Item itemKey="connector" text={formatMessage({ id: 'settings.menu.connector' })} onClick={() => {
                        setSelectedKeys(['connector'])
                        navigate(`${BASE_PATH}/settings/connector`)
                    }}></Nav.Item>}
                    <Nav.Item itemKey="dns" text='DNS' onClick={() => {
                        setSelectedKeys(['dns'])
                        navigate(`${BASE_PATH}/settings/dns`)
                    }}></Nav.Item>
                </Nav.Sub>
           
                <Nav.Sub itemKey="personal" text={formatMessage({ id: 'settings.category.security' })} icon={<IconSafe />} >
                    <Nav.Item itemKey="mfa" text="MFA" onClick={() => {
                        setSelectedKeys(['mfa'])
                        navigate(`${BASE_PATH}/settings/mfa`)
                    }}></Nav.Item>
                    <Nav.Item itemKey="api" text={formatMessage({ id: 'settings.menu.api' })} onClick={() => {
                        setSelectedKeys(['api'])
                        navigate(`${BASE_PATH}/settings/api`)
                    }}></Nav.Item>

                    <Nav.Item itemKey="sensitive" text={formatMessage({ id: 'settings.menu.sensitive' })} onClick={() => {
                        setSelectedKeys(['sensitive'])
                        navigate(`${BASE_PATH}/settings/sensitive`)
                    }
                    }></Nav.Item>
                    <Nav.Item itemKey="keys" text={formatMessage({ id: 'settings.menu.keys' })} onClick={() => {
                        setSelectedKeys(['keys'])
                        navigate(`${BASE_PATH}/settings/keys`)
                    }}></Nav.Item>
                </Nav.Sub>
          
                <Nav.Sub itemKey="data" text={formatMessage({ id: 'settings.category.data' })} icon={<IconServer />} >
                    <Nav.Item itemKey="schema" text={formatMessage({ id: 'settings.menu.schema' })} onClick={() => {
                        setSelectedKeys(['schema'])
                        navigate(`${BASE_PATH}/settings/schema`)
                    }}></Nav.Item>
                    <Nav.Item itemKey="logs" text={formatMessage({ id: 'settings.menu.logs' })} onClick={() => {
                        setSelectedKeys(['logs'])
                        navigate(`${BASE_PATH}/settings/logs`)
                    }}></Nav.Item>
                    {VITE_LOCAL_PAGER_AND_FILTER && <Nav.Item itemKey="datasource" text={formatMessage({ id: 'settings.menu.datasource' })} onClick={() => {
                        setSelectedKeys(['datasource'])
                        navigate(`${BASE_PATH}/settings/datasource`)
                    }}></Nav.Item>}
                    {VITE_LOCAL_PAGER_AND_FILTER && <Nav.Item itemKey="application" text={formatMessage({ id: 'settings.menu.application' })} onClick={() => {
                        setSelectedKeys(['application'])
                        navigate(`${BASE_PATH}/settings/application`)
                    }}></Nav.Item>}
                </Nav.Sub>
          
                {VITE_LOCAL_PAGER_AND_FILTER && <Nav.Sub itemKey={'rights'} text={formatMessage({ id: 'settings.category.rights' })} icon={<IconApartment />}>
                    <Nav.Item itemKey="rights" text={formatMessage({ id: 'settings.menu.rights' })} onClick={() => {
                        setSelectedKeys(['rights'])
                        navigate(`${BASE_PATH}/settings/rights`)
                    }}></Nav.Item>
                </Nav.Sub>}
            </Nav> */}

        </Sider>
        <Content className={styles.content}>
            <Suspense fallback={<></>}>
                <Outlet />
            </Suspense></Content>
    </Layout>
}

export default Index;
