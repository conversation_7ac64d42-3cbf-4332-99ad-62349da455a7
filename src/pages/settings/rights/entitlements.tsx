import React, { useContext, useEffect, useState } from 'react'
import { <PERSON><PERSON><PERSON>, Banner, Row, Card, Col, Space, Button } from '@douyinfe/semi-ui';
import styles from './index.module.scss'
import { IconUser, IconListView, IconApps, IconSafe, IconWifi, IconUserGroup, IconDesktop, IconVerify, IconExpand, IconKanban, IconSetting } from '@douyinfe/semi-icons';

import { LicenseContext } from '@/hooks/useLicense';
const { Title, Paragraph, Text } = Typography;


const dynamicForm = {
    vpn:
    {
        "name": "VPN和组网",
        icon: <IconSafe />,
        "form_settings": [
            // {
            //     "key": "concurrent",
            //     "min": 0,
            //     "type": "input.number",
            //     "extra": "",
            //     "label": "最大并发连接数",
            //     "layout": {
            //         "span": 12
            //     },
            //     "prefix": "",
            //     "suffix": "",
            //     "required": true,
            //     "conditions": [],
            //     "initial_value": "0"
            // },
            {
                "key": "devices",
                "min": 0,
                "type": "input.number",
                "extra": "",
                "label": "最大设备数量",
                "layout": {
                    "span": 12
                },
                "prefix": "",
                "suffix": "",
                "required": true,
                "conditions": [],
                "initial_value": "0"
            },
            {
                "key": "users",
                "min": 0,
                "type": "input.number",
                "extra": "",
                "label": "最大用户数",
                "layout": {
                    "span": 24
                },
                "prefix": "",
                "suffix": "",
                "required": true,
                "conditions": [],
                "initial_value": "0"
            }
        ]
    }

}

const Entitlements = () => {
    const license = useContext(LicenseContext);
    const renderEntitlements = (entitlements:object) => {
        return Object.keys(entitlements).map((key) => {
            const entitlement = (entitlements as any)[key];

            const form = (dynamicForm as any)[key];
            if (!form) {
                return null;
            }
            return <Col  key={key} span={8} className={styles.module}><Card title={<Space>{form.icon}<Text>{form.name}</Text></Space>}>
                {
                    Object.keys(entitlement).map((key) => {
                        const value = entitlement[key];
                        const item = form.form_settings.find((item:any) => item.key === key);
                        if (!item) {
                            return null;
                        }
                        return <div key={key} className='mb10'>
                            <Space>
                                <Text type='tertiary'>{item.label}</Text>
                                <Text type='secondary'>{value}</Text>
                            </Space>
                        </div>
                    })
                }
                
            </Card></Col>
        })
    }

    return <Row gutter={20} type='flex'>
        {renderEntitlements(license.entitlements)}
    </Row>
}

export default Entitlements