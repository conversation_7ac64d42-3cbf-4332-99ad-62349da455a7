import React, { useContext } from 'react'
import { <PERSON>po<PERSON>, <PERSON>, Card, Col, Space } from '@douyinfe/semi-ui';
import styles from './index.module.scss'
import { IconGlobeStroke, IconListView, IconApps, IconSafe, IconWifi, IconUserGroup, IconDesktop, IconVerify, IconExpand, IconKanban, IconSetting } from '@douyinfe/semi-icons';

import { LicenseContext } from '@/hooks/useLicense';
const { Text } = Typography;


const dynamicForm = {
    vpn:
    {
        "name": "VPN和组网",
        icon: <IconSafe />,
        "form_settings": [
            // {
            //     "key": "concurrent",
            //     "min": 0,
            //     "type": "input.number",
            //     "extra": "",
            //     "label": "最大并发连接数",
            //     "layout": {
            //         "span": 12
            //     },
            //     "prefix": "",
            //     "suffix": "",
            //     "required": true,
            //     "conditions": [],
            //     "initial_value": "0"
            // },
            {
                "key": "devices",
                "min": 0,
                "type": "input.number",
                "extra": "",
                "label": "最大设备数量",
                "layout": {
                    "span": 12
                },
                "prefix": "",
                "suffix": "",
                "required": true,
                "conditions": [],
                "initial_value": "0"
            },
            {
                "key": "users",
                "min": 0,
                "type": "input.number",
                "extra": "",
                "label": "最大用户数",
                "layout": {
                    "span": 24
                },
                "prefix": "",
                "suffix": "",
                "required": true,
                "conditions": [],
                "initial_value": "0"
            }
        ]
    },
    acl: {
        "name": "访问控制",
        "icon": <IconGlobeStroke/>,
        "form_settings": [
            {
                "key": "max_dns",
                "min": 0,
                "type": "input.number",
                "extra": "",
                "label": "最大DNS解析数",
                "layout": {
                    "span": 12
                },
                "prefix": "",
                "suffix": "",
                "required": true,
                "conditions": [],
                "initial_value": "0"
            },
            {
                "key": "max_domain",
                "min": 0,
                "type": "input.number",
                "extra": "",
                "label": "最大域名解析数",
                "layout": {
                    "span": 12
                },
                "prefix": "",
                "suffix": "",
                "required": true,
                "conditions": [],
                "initial_value": "0"
            },
            {
                "key": "max_acl",
                "min": 0,
                "type": "input.number",
                "extra": "",
                "label": "最大策略数",
                "layout": {
                    "span": 12
                },
                "prefix": "",
                "suffix": "",
                "required": true,
                "conditions": [],
                "initial_value": "0"
            }
        ]
    },
    advance: {
        "name": "高级功能",
        "icon": <IconApps/>,
        "form_settings": [
            {
                "key": "dynamic_user_group",
                "type": "radio",
                "extra": "",
                "label": "动态用户组",
                "layout": {
                    "span": 12
                },
                "prefix": "",
                "suffix": "",
                "options": [
                    {
                        "label": "开",
                        "value": "true"
                    },
                    {
                        "label": "关",
                        "value": "false"
                    }
                ]
            },
            {
                "key": "dynamic_device_group",
                "type": "radio",
                "extra": "",
                "label": "动态设备组",
                "layout": {
                    "span": 12
                },
                "prefix": "",
                "suffix": "",
                "options": [
                    {
                        "label": "开",
                        "value": "true"
                    },
                    {
                        "label": "关",
                        "value": "false"
                    }
                ]
            }
        ]
    }
}

const Entitlements = () => {
    const license = useContext(LicenseContext);
    const renderEntitlements = (entitlements: object) => {
        return Object.keys(entitlements).map((key) => {
            const entitlement = (entitlements as any)[key];

            const form = (dynamicForm as any)[key];
            if (!form) {
                return null;
            }
            return <Col key={key} span={8} className={styles.module}><Card title={<Space>{form.icon}<Text>{form.name}</Text></Space>}>
                {
                    Object.keys(entitlement).map((key) => {
                        let value = entitlement[key];
                        const item = form.form_settings.find((item: any) => item.key === key);
                        if (!item) {
                            return null;
                        }
                        if(item.type == 'radio') {
                            if(value == true || value == 'true') {
                                value = '开'
                            } else {
                                value = '关'
                            }
                        }
                        return <div key={key} className='mb10'>
                            <Space>
                                <Text type='tertiary'>{item.label}</Text>
                                <Text type='secondary'> {value}</Text>
                            </Space>
                        </div>
                    })
                }

            </Card></Col>
        })
    }

    

    return <Row gutter={20} type='flex'>
        {renderEntitlements(license.entitlements)}
    </Row>
}

export default Entitlements