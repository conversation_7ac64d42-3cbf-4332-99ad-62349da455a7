import React, { useContext, useState } from 'react'
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { LicenseContext } from '@/hooks/useLicense';
import { formatDefaultDate, formatDefaultTimestamp } from '@/utils/format';
import Entitlements from './entitlements';
import Activation from '@/components/activation';

import { Typography, Banner, Row, Card, Col, Space, Button } from '@douyinfe/semi-ui';
import styles from './index.module.scss'
import { VITE_LOCAL_PAGER_AND_FILTER } from '@/utils/service';
import { useLocale } from '@/locales';
import Copyable from '@douyinfe/semi-ui/lib/es/typography/copyable';


const { Title, Paragraph, Text } = Typography;

const Index: React.FC = () => {
    const { formatMessage } = useLocale();
    const flynetGeneral = useContext(FlynetGeneralContext);

    const license = useContext(LicenseContext);

    // 邀请码对话框是否显示
    const [inviteCodeModalVisible, setInviteCodeModalVisible] = useState(false)

    return <>
        <div className='settings-page'>
            <Title heading={3} className='mb10' >{formatMessage({ id: 'settings.rights.title' })}</Title>
            <Paragraph className='mb40' type='tertiary'>
                {formatMessage({ id: 'settings.rights.description' })}
            </Paragraph>
            {license.expired &&
            <Banner className='mb20' type='warning'
                description={<><Text size='normal'>
                    {formatMessage({ id: 'settings.rights.activationWarning' })}
                    </Text>&nbsp;{flynetGeneral.disabledExpiry ? '' : <Button type="warning" theme='solid' style={{ marginLeft: 20 }} onClick={() => {
                    setInviteCodeModalVisible(true)
                }}>{formatMessage({ id: 'settings.rights.activate' })}</Button>}</>}
            ></Banner>}

            <div className={styles.workspaceForm}>
                <Row>
                    <Col span={4}><Text>{formatMessage({ id: 'settings.rights.licensee' })}</Text></Col>
                    <Col span={16}>
                        <Text type='tertiary'>
                            {license.customer.name}
                        </Text>
                    </Col>
                    <Col span={4} className='btn-right-col'>
                        <Button theme='solid' onClick={() => {
                            setInviteCodeModalVisible(true)
                        }}>{formatMessage({ id: 'settings.rights.updateLicense' })}</Button>
                    </Col>
                </Row>
                <Row style={{marginBottom: 32}}>
                    <Col span={4}><Text>{formatMessage({ id: 'settings.rights.validUntil' })}</Text></Col>
                    <Col span={20}>
                        <Text type='tertiary'>{license.disabledExpiry ? formatMessage({ id: 'settings.rights.permanent' }) : license.expiredAt ? formatDefaultDate(license.expiredAt) : ''}</Text>
                    </Col>
                </Row>
                <Row>
                    <Col span={4}><Text>{formatMessage({ id: 'settings.rights.machineCode' })}</Text></Col>
                    <Col span={20}>
                        <Space>
                        <Text type='tertiary' copyable style={{width: '100%', wordWrap:'break-word', wordBreak: 'break-all'}} >
                            {license.machineCode}
                        </Text>
                        
                        </Space>
                    </Col>
                </Row>

            </div>

            <Entitlements/>

        </div>

        {/**邀请码弹出框 */}
        {inviteCodeModalVisible && <Activation 
        close={() => { setInviteCodeModalVisible(false) }} success={() => {
            setInviteCodeModalVisible(false)
            window.location.reload();
        }} />}
    </>
}

export default Index