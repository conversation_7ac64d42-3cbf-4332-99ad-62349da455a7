import { FC, useState, useContext, useEffect } from 'react';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import SearchFilter from '@/components/search-filter-combo';
import { UserGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb';

import { Toast, Modal, Typography, RadioGroup, Select } from '@douyinfe/semi-ui';
import useUserField from '../../useUserField';
import { flylayerClient } from '@/services/core';
import { useLocale } from '@/locales';

const { Paragraph } = Typography;
interface FilterParam {
    name: string,
    label: string,
    placeholder: string,
    value: string | any,
    fixed?: boolean,
    filterComponent?: FC<{
        value: string | string[] | any,
        onChange: (val: string | string[] | any) => void
    }>,
    funGetDisplayValue?: (val: string | any) => string
}
type UserFilter = {
    keywords: string,
    roles: string,
    status: 'enable' | 'disable' | '',
    groups: string,
    temporary: 'true' | 'false' | ''
}
interface Props {
    close: () => void;
    success?: () => void;
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const flynet = useContext(FlynetGeneralContext);

    const query = async () => {

        const resUserGroups = await flylayerClient.listUserGroups({
            flynetId: flynet.id,
        })
        const groups = resUserGroups.groups;
        initFilterParams(groups);

    }

    useEffect(() => {
        query();
    }, [])


    const { fields, setFields, selectedKeys, setSelectedKeys } = useUserField(false);
    const [filterParams, setFilterParams] = useState<FilterParam[]>([]);


    const [filter, setFilter] = useState<UserFilter>(
        {
            keywords: '',
            roles: '',
            status: '',
            groups: '',
            temporary: ''
        } as UserFilter
    );
    const [saveLoading, setSaveLoading] = useState(false);

    const initFilterParams = (userGroups: UserGroup[]) => {
        setFilterParams([{
            name: 'keywords',
            placeholder: formatMessage({ id: 'users.dataExport.filter.searchPlaceholder' }),
            label: formatMessage({ id: 'users.dataExport.filter.search' }),
            value: '',
        },
        {
            name: 'status',
            placeholder: formatMessage({ id: 'users.dataExport.filter.statusPlaceholder' }),
            label: formatMessage({ id: 'users.dataExport.filter.status' }),
            fixed: true,
            value: '',
            filterComponent: ({ value, onChange }) => {
                return <Select insetLabel={formatMessage({ id: 'users.dataExport.filter.status' })} style={{ width: 400 }} value={value} onChange={(val) => onChange(val as string)}>
                    <Select.Option value=''>{formatMessage({ id: 'users.dataExport.filter.all' })}</Select.Option>
                    <Select.Option value='enable'>{formatMessage({ id: 'users.dataExport.filter.enabled' })}</Select.Option>
                    <Select.Option value='disable'>{formatMessage({ id: 'users.dataExport.filter.disabled' })}</Select.Option>
                </Select>

            },
            funGetDisplayValue: (val: string) => {
                if (val == 'enable') {
                    return formatMessage({ id: 'users.dataExport.filter.enabled' });
                } else if (val == 'disable') {
                    return formatMessage({ id: 'users.dataExport.filter.disabled' });
                }
                return '';
            }
        }, {
            name: 'groups',
            placeholder: formatMessage({ id: 'users.dataExport.filter.groupPlaceholder' }),
            label: formatMessage({ id: 'users.dataExport.filter.group' }),
            value: '',
            fixed: true,
            filterComponent: ({ value, onChange }) => {
                return <Select
                    position='top'
                    value={value ? value.split(',') : []}
                    onChange={(val) => {
                        const value = val as string[];
                        onChange(value.join(','));
                    }}
                    style={{ width: 400 }}
                    placeholder={formatMessage({ id: 'users.dataExport.filter.groupPlaceholder' })}
                    optionList={[
                        ...userGroups.map(g => {
                            return { value: g.id + '', label: g.alias }
                        })
                    ]}
                    multiple
                    maxTagCount={1}
                >
                </Select>;
            },
            funGetDisplayValue: (value: string) => {
                let names: string[] = [];
                if (value) {
                    let val = value.split(',');
                    val.forEach(v => {
                        let group = userGroups.find(g => g.id + '' == v);
                        if (group) {
                            names.push(group.alias);
                        }
                    });
                }
                return names.join(',');
            }
        }, {
            name: 'temporary',
            placeholder: formatMessage({ id: 'users.dataExport.filter.typePlaceholder' }),
            label: formatMessage({ id: 'users.dataExport.filter.type' }),
            value: '',
            fixed: true,
            filterComponent: ({ value, onChange }) => {

                return <RadioGroup
                    type='button'
                    value={value}
                    onChange={(val) => {
                        onChange(val.target.value);
                    }}
                    style={{ width: 180 }}
                    options={[
                        { value: 'true', label: formatMessage({ id: 'users.dataExport.filter.temporaryUser' }) },
                        { value: 'false', label: formatMessage({ id: 'users.dataExport.filter.normalUser' }) }
                    ]}
                >
                </RadioGroup>
            },
            funGetDisplayValue: (val: string) => {
                if (val == 'true') {
                    return formatMessage({ id: 'users.dataExport.filter.temporaryUser' });
                } else if (val == 'false') {
                    return formatMessage({ id: 'users.dataExport.filter.normalUser' });
                }
                return '';
            }
        }])

    }

    const handleSubmit = async () => {
        setSaveLoading(true);


        let queryArray = [];


        if (filter.groups) {
            queryArray.push(`groups=${filter.groups}`);
        }

        if (filter.status == 'enable') {
            queryArray.push(`disabled=false`);
        } else if (filter?.status == 'disable') {
            queryArray.push('disabled=true');
        }

        if (filter.temporary == 'true') {
            queryArray.push('temporary=true');
        } else if (filter.temporary == 'false') {
            queryArray.push('temporary=false');
        }

        if (filter.keywords != "" && filter.keywords.trim() != "") {
            queryArray.push(`keywords=${encodeURIComponent(filter.keywords || '')}`);
        }


        flylayerClient.exportUsers({
            flynetId: flynet.id,
            query: queryArray.join('&')
        }).then((res) => {
            setSaveLoading(false);
            if (res) {
                props.success && props.success();
                props.close();
            }
            window.open(res.downloadUrl, '_blank');
        }
        ).catch((err) => {
            setSaveLoading(false);
            if (err) {
                if (err.response) {
                    if (err.response.status === 403) {
                        Toast.error(formatMessage({ id: 'users.dataExport.noPermission' }));
                    } else {
                        Toast.error(err.response.data.message);
                    }
                }
            }
        });
        // const flynetGeneral = await flylayerClient.getFlynetGeneral();
        // let res = await flylayerClient.exportUsers({
        //     flynetId: flynetGeneral.id,
        //     fields: newFields,
        // })
        // setSaveLoading(false);
        // if (res) {
        //     props.success && props.success();
        //     props.close();
        // }

        //  window.open(res.downloadUrl, '_blank');

    }

    return (
        <>
            <Modal
                width={600}
                title={formatMessage({ id: 'users.dataExport.modal.title' })}
                visible={true}
                onCancel={props.close}
                onOk={handleSubmit}
                okText={formatMessage({ id: 'users.dataExport.modal.export' })}
                okButtonProps={{ loading: saveLoading }}
                className='semi-modal'
                maskClosable={false}
            >
                <Paragraph type="tertiary" className='mb20'>
                    {formatMessage({ id: 'users.dataExport.description' })}
                </Paragraph>
                <SearchFilter onChange={(val: string, filterParam) => {

                    setFilter({ ...filter, [filterParam.name]: val } as UserFilter)

                    const newFilterParams = filterParams.map((item) => {
                        if (item.name == filterParam.name) {
                            item.value = val;
                        }
                        return item;
                    })
                    setFilterParams(newFilterParams);
                }} filterParams={filterParams} className='mb20 search-bar'></SearchFilter>





            </Modal>

        </>
    );
};

export default Index;