import { FC, useEffect, useState } from 'react';
import { Typography, Table, Skeleton, Breadcrumb, Tag, Divider, Descriptions } from '@douyinfe/semi-ui';
import { BASE_PATH } from '@/constants/router';
import { useParams } from 'react-router-dom';
import { ImportRecord } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/import_export_pb';
import { flylayerClient } from '@/services/core';
import TableEmpty from '@/components/table-empty';
import DateFormat from '@/components/date-format';
import { useLocale } from '@/locales';

const { Title, Paragraph } = Typography;

const Index: FC = () => {
    const { formatMessage } = useLocale();
    const params = useParams<{ id: string }>()
    const idStr = params.id || '';

    const [loading, setLoading] = useState(true);
    const [record, setRecord] = useState<ImportRecord>();

    useEffect(() => {
        setLoading(true);
        flylayerClient.getImportRecord({
            id: BigInt(idStr)
        }).then((res) => {
            setRecord(res.record);
            setLoading(false);
        }
        ).catch((err) => {
            console.error('Failed to fetch import record', err);
            setLoading(false);
        }
        ).finally(() => {
            setLoading(false);
        });
    }, [idStr]);

    return <>
        <div className='general-page'>
            <Breadcrumb routes={
                [
                    {
                        path: `${BASE_PATH}/users`,
                        href: `${BASE_PATH}/users`,
                        name: formatMessage({ id: 'users.allUsers' })
                    }, {
                        path: `${BASE_PATH}/users/import`,
                        href: `${BASE_PATH}/users/import`,
                        name: formatMessage({ id: 'users.dataImport.title' }),
                    }, {
                        name: formatMessage({ id: 'users.dataImport.detail.title' })
                    }
                ]
            }>
            </Breadcrumb>
            <Title heading={3} className='mb20'>{formatMessage({ id: 'users.dataImport.detail.recordDetail' })}</Title>
            <Divider className='mb10' />
            <Skeleton loading={loading} >
                <Descriptions className='mb40'>
                    <Descriptions.Item key={'fileName'} itemKey={formatMessage({ id: 'users.dataImport.detail.fileName' })}>{record?.fileName}</Descriptions.Item>
                    <Descriptions.Item key={'fileUrl'} itemKey={formatMessage({ id: 'users.dataImport.detail.fileUrl' })}>
                        <a className='link-external' href={record?.fileUrl} target='_blank'>{record?.fileUrl}</a>
                    </Descriptions.Item>
                    <Descriptions.Item key={'totalLine'} itemKey={formatMessage({ id: 'users.dataImport.detail.totalLines' })}>{record?.totalLine}</Descriptions.Item>
                    <Descriptions.Item key={'errorLine'} itemKey={formatMessage({ id: 'users.dataImport.detail.errorLines' })}>{record?.errorLine}</Descriptions.Item>
                    <Descriptions.Item key={'createLine'} itemKey={formatMessage({ id: 'users.dataImport.detail.createLines' })}>{record?.createLine}</Descriptions.Item>
                    <Descriptions.Item key={'updateLine'} itemKey={formatMessage({ id: 'users.dataImport.detail.updateLines' })}>{record?.updateLine}</Descriptions.Item>
                    <Descriptions.Item key={'finish'} itemKey={formatMessage({ id: 'users.dataImport.detail.isFinished' })}>{record?.finish ? formatMessage({ id: 'common.yes' }): formatMessage({ id: 'common.no' })}</Descriptions.Item>
                    <Descriptions.Item key={'user'} itemKey={formatMessage({ id: 'users.dataImport.table.operator' })}>{record?.user?.displayName}({record?.user?.loginName})</Descriptions.Item>
                    <Descriptions.Item key={'createdAt'} itemKey={formatMessage({ id: 'users.dataImport.table.importTime' })}><DateFormat date={record?.createdAt} /></Descriptions.Item>
                </Descriptions>


                <Title heading={4} className='mb2'>{formatMessage({ id: 'users.dataImport.detail.errorRows' })}</Title>
                <Paragraph className='mb10' type='tertiary'>{formatMessage({ id: 'users.dataImport.detail.errorRowsDescription' })}</Paragraph>
                {record?.errors && record.errors.length > 0 ? (
                    <Table
                        dataSource={record.errors}
                        rowKey='line'
                        pagination={false}
                        columns={[
                            {
                                title: formatMessage({ id: 'users.dataImport.detail.lineNumber' }),
                                dataIndex: 'line',
                                width: 100,
                            },
                            {
                                title: formatMessage({ id: 'users.dataImport.detail.errorMessage' }),
                                dataIndex: 'message',
                            }
                        ]}
                    />
                ) : (
                    <TableEmpty loading={false}></TableEmpty>
                )}
                <div style={{ height: 40 }}></div>


                <Title heading={4} className='mb2'>{formatMessage({ id: 'users.dataImport.detail.importDetails' })}</Title>
                <Paragraph className='mb10' type='tertiary'>{formatMessage({ id: 'users.dataImport.detail.importDetailsDescription' })}</Paragraph>
                {record?.importItems && record.importItems.length > 0 ? (
                    <Table
                        dataSource={record.importItems}
                        rowKey='line'
                        pagination={false}
                        columns={[
                            {
                                title: formatMessage({ id: 'users.dataImport.detail.lineNumber' }),
                                dataIndex: 'lineNumber',
                                width: 100,
                            },
                            {
                                title: formatMessage({ id: 'users.dataImport.detail.isSuccess' }),
                                dataIndex: 'success',
                                render: (value: boolean) => {
                                    return <Tag color={value  ? 'green' : 'red'}>{value ? formatMessage({ id: 'common.yes' }) : formatMessage({ id: 'common.no' })}</Tag>;
                                }
                            },
                            {
                                title: formatMessage({ id: 'users.dataImport.detail.isFinished' }),
                                dataIndex: 'finish',
                                render: (value: boolean) => {
                                    return <Tag color={value ? 'green' : 'red'}>{value ? formatMessage({ id: 'common.yes' }) : formatMessage({ id: 'common.no' })}</Tag>;
                                }
                            },
                            {
                                title: formatMessage({ id: 'users.dataImport.detail.message' }),
                                dataIndex: 'errorMessage',
                            }
                        ]}
                    />
                ) : (
                    <TableEmpty loading={false}></TableEmpty>
                    
                )}
                <div style={{ height: 40 }}></div>
            </Skeleton>

        </div>
    </>
}

export default Index;