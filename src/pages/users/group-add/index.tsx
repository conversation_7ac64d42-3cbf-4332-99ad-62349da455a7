import React, { useState, useContext, useEffect } from 'react'
import { Typography, Form, Modal, Row, Col, Button, Divider, Notification, Popover, Space, Card } from '@douyinfe/semi-ui';
import pinyin from 'tiny-pinyin';
import styles from './index.module.scss';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { User, UserGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb';
import { GroupType, DynamicGroupMode } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';

import { getFlynet } from '@/services/flynet';
import { Expression } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import { flylayerClient } from '@/services/core';
import TableEmpty from '@/components/table-empty'
import { IconPlus, IconMinusCircle, IconHelpCircle, IconArrowUpRight } from '@douyinfe/semi-icons';
import UserModalSelector from '@/components/user-modal-selector';
import CodeEditor from '@/components/code-editor';

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
const { Paragraph, Text } = Typography;
import Expressions from '@/components/expressions';
import { sanitizeLabel, validateParamCombo } from '@/utils/common';
import { BASE_PATH } from '@/constants/router';
import { AttributeTemplate } from '@/interface/attribute-template';
import { useLocale } from '@/locales';
import { at } from 'lodash';
const { Input, RadioGroup, Radio, InputNumber, Switch } = Form
interface Props {
    close: () => void,
    success?: (userGroup?: UserGroup) => void
}
const Index: React.FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const flynet = useContext(FlynetGeneralContext);
    const [formApi, SetFormApi] = useState<FormApi<{
        name: string,
        description: string,
        alias: string,
        type: GroupType,
        // otherUsers: Array<string>,
        expressionsCombo: string,
        advancedDynamicMode: boolean,
        priority: number,
    }>>()



    const [loading, setLoading] = useState(false);
    const [users, setUsers] = useState<User[]>([]);
    const [groupType, setGroupType] = useState<GroupType>(GroupType.GROUP_STATIC);

    const [attributeTemplate, setAttributeTemplate] = useState<AttributeTemplate>();

    const queryFlynet = async () => {
        let res = await getFlynet(flynet.id);
        if (res && res.flynet && res.flynet.attributeTemplate) {
            const json = JSON.parse(res.flynet.attributeTemplate);
            const userProperties = json.properties.input.properties.User;

            let attributeTemplate: AttributeTemplate = {
                type: 'object',
                title: 'properties',
                description: 'properties',
                properties: {
                    'input': {

                        type: 'object',
                        description: formatMessage({ id: 'users.groupAdd.input' }),
                        title: formatMessage({ id: 'users.groupAdd.input' }),
                        properties: {
                            User: userProperties
                        }
                    }
                }

            }

            setAttributeTemplate(attributeTemplate);
        }
    }
    useEffect(() => {
        queryFlynet();
    }, [])

    const [expressions, setExpressions] = useState<Array<Expression>>([]);
    const [advancedDynamicMode, setAdvancedDynamicMode] = useState(false);
    const [expressionAdvanced, setExpressionAdvanced] = useState(`
    package play

    import future.keywords.if
    
    # Please enter OPA expression script
    # See: https://play.openpolicyagent.org/
    
    default hello := false
    
    hello if input.message == "world"
        
    `);


    const [expressionsError, setExpressionsError] = useState(false);

    const [userSelectorVisible, setUserSelectorVisible] = useState(false);

    const handleSubmit = async () => {

        await formApi?.validate();

        const values = formApi?.getValues();
        if (!values) {
            return;
        }

        const name = values.name.trim();
        const description = values.description ? values.description.trim() : '';
        const alias = values.alias ? values.alias.trim() : '';
        const type = values.type;
        // const otherUsers = values.otherUsers;
        const expressionsCombo = values.expressionsCombo;
        const priority = values.priority;


        if (type == GroupType.GROUP_DYNAMIC) {
            if (advancedDynamicMode) {
                if (expressionAdvanced.length == 0) {
                    setExpressionsError(true)
                    return;
                }
            } else {
                if (expressionsError || expressions.length == 0) {
                    setExpressionsError(true)
                    return;
                }
            }
        }

        setLoading(true);


        flylayerClient.createUserGroup({
            flynetId: flynet.id,
            name: name,
            description: description,
            alias: alias,
            type: type,
            users: users,
            priority: priority,
            attrs: advancedDynamicMode ? {
                // otherUsers: otherUsers,
                dynamicGroupMode: DynamicGroupMode.DYNAMIC_GROUP_ADVANCED,
                expressionAdvanced: expressionAdvanced
            } : {
                // otherUsers: otherUsers,
                dynamicGroupMode: DynamicGroupMode.DYNAMIC_GROUP_STANDARD,
                expressions: expressions,
                expressionsCombo: expressionsCombo
            }
        }).then((res) => {
            Notification.success({
                title: formatMessage({ id: 'users.groupAdd.success' }),
                content: formatMessage({ id: 'users.groupAdd.success' })
            })

            props.success && props.success();
        }).catch((err) => {
            Notification.error({
                title: formatMessage({ id: 'users.groupAdd.failed' }),
                content: err.message
            })
        }).finally(() => {
            setLoading(false);
        })

    }

    return <>
        <Modal
            width={800}
            title={formatMessage({ id: 'users.groupAdd.title' })}
            visible={true}
            onCancel={props.close}
            onOk={handleSubmit}
            okButtonProps={{ loading: loading }}
            className='semi-modal'
            maskClosable={false}
        >

            <div className={styles.addService}>
                <Form getFormApi={SetFormApi}
                    allowEmpty
                    initValues={{
                        type: GroupType.GROUP_STATIC,
                        advancedDynamicMode: false,
                        priority: 1,
                    }}
                    onValueChange={(values, changedValue) => {
                        if (changedValue.hasOwnProperty('alias')) {
                            formApi?.setValue('name', sanitizeLabel(pinyin.convertToPinyin(changedValue.alias, '', true)))
                        }
                    }}
                >
                    <Row gutter={12}>
                        <Col span={12}>
                            <Input field='alias' label={formatMessage({ id: 'users.groupAdd.name' })} trigger={'blur'} validate={value => {
                                if (!value) {
                                    return formatMessage({ id: 'users.groupAdd.nameRequired' });
                                }
                                return '';
                            }} />
                        </Col>
                        <Col span={12}>
                            <Input field='name'
                                label={<>{formatMessage({ id: 'users.groupAdd.code' })} <Popover content={<div className='p10'>{formatMessage({ id: 'users.groupAdd.codeHint' })}</div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover></>}
                                trigger={'blur'} validate={value => {
                                    if (!value) {
                                        return formatMessage({ id: 'users.groupAdd.codeRequired' });
                                    }

                                    if (value.trim().startsWith('-')) {
                                        return formatMessage({ id: 'users.groupAdd.codeNoStartDash' });
                                    }
                                    if (!/^[\dA-Za-z-]+$/.test(value.trim())) {
                                        return formatMessage({ id: 'users.groupAdd.codeFormat' });
                                    }
                                    return '';
                                }}
                                required />
                        </Col>

                    </Row>
                    <Row>
                        <Col span={24}>
                            <Input field='description' label={formatMessage({ id: 'users.groupAdd.description' })} />
                        </Col>
                    </Row>
                    <Divider className='mb10'></Divider>
                    <Row>
                        <Col span={12}>
                            <RadioGroup fieldStyle={{paddingTop:10, paddingBottom:10}} labelPosition="left" field='type' label={formatMessage({ id: 'users.groupAdd.type' })} onChange={(e) => setGroupType(e.target.value)}>
                                <Radio value={GroupType.GROUP_STATIC}>{formatMessage({ id: 'users.groupAdd.staticGroup' })}</Radio>
                                <Radio value={GroupType.GROUP_DYNAMIC}>{formatMessage({ id: 'users.groupAdd.dynamicGroup' })}</Radio>
                            </RadioGroup>
                        </Col>

                    </Row>
                    {/* <Divider className='mb10'></Divider> */}
                    {groupType == GroupType.GROUP_STATIC && <>
                        <Row className='mb10'>
                            <Col span={20}>
                                <Text type='tertiary'>{formatMessage({ id: 'users.groupAdd.builtinUsers' })}</Text>
                            </Col>
                            <Col span={4} className={styles.rightColumn}>
                                <Button
                                    onClick={() => {
                                        setUserSelectorVisible(true);
                                    }}
                                    icon={<IconPlus></IconPlus>}></Button>
                            </Col>
                        </Row>
                        {
                            users.length == 0 ? <TableEmpty loading={false}></TableEmpty> :
                                <>
                                    {users.map((item, index) => {
                                        return <Row className="mb10" key={index}>
                                            <Col span={20}>
                                                {item.displayName}({item.loginName})
                                            </Col>
                                            <Col span={4} className={styles.rightColumn}>
                                                <Button
                                                    type='danger'
                                                    onClick={() => {
                                                        let newUsers = users.filter((item, i) => i != index);
                                                        setUsers(newUsers);
                                                    }}
                                                    icon={<IconMinusCircle></IconMinusCircle>}></Button>
                                            </Col>
                                        </Row>
                                    })}
                                </>
                        }
                    </>}
                    {groupType == GroupType.GROUP_DYNAMIC && attributeTemplate && <>
                        {
                            advancedDynamicMode ? <>
                                <CodeEditor value={expressionAdvanced} onChange={(value) => setExpressionAdvanced(value || '')} height='280px' language='systemverilog'></CodeEditor>
                                {expressionsError && <Paragraph type='danger'>{formatMessage({ id: 'users.groupAdd.expressionRequired' })}</Paragraph>}
                            </> : <>
                                <Expressions
                                    expressions={expressions}
                                    onChange={(expressions: Array<Expression>) => {
                                        setExpressions(expressions);
                                        setExpressionsError(false);
                                    }}
                                    onError={() => {
                                        setExpressionsError(true);
                                    }}
                                    attributeTemplate={attributeTemplate}
                                ></Expressions>
                                {expressionsError && <Paragraph type='danger'>{formatMessage({ id: 'users.groupAdd.triggerParamError' })}</Paragraph>}
                                <Row>
                                    <Col span={24}>
                                        <Input
                                            validate={ value => (validateParamCombo(value, expressions.length))}
                                        
                                            extraText={formatMessage({ id: 'users.groupAdd.operatorHelp' })}
                                            field='expressionsCombo' label={formatMessage({ id: 'users.groupAdd.paramCombination' })} />
                                    </Col>
                                </Row>
                            </>
                        }

                    </>}
                    {groupType == GroupType.GROUP_DYNAMIC && !attributeTemplate && <Card>
                        <Paragraph style={{ textAlign: 'center' }}>{formatMessage({ id: 'users.groupAdd.dynamicGroupEmptyHint' })}
                            <a className='link-external' target='_blank' href={`${BASE_PATH}/settings/schema`} onClick={(e) => { e.stopPropagation() }}>
                                {formatMessage({ id: 'users.groupAdd.settings' })}<IconArrowUpRight />
                            </a>

                        </Paragraph>

                    </Card>}

                </Form>
            </div>
        </Modal>
        {
            userSelectorVisible && <UserModalSelector
                multi={true}
                value={users}
                onChange={(value: User | User[]) => {
                    setUserSelectorVisible(false)

                    let newUsers = users.filter((item) => true);
                    if (value instanceof Array) {
                        value.forEach((item) => {
                            if (!newUsers.some(u => u.id == item.id)) {
                                newUsers.push(item);
                            }
                        })
                    } else {
                        if (!newUsers.some(u => u.id == value.id)) {
                            newUsers.push(value);
                        }
                    }
                    setUsers(newUsers);
                }}
                close={() => setUserSelectorVisible(false)}
            ></UserModalSelector>
        }
    </>
}

export default Index;
