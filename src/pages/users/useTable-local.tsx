import { useEffect, useState, useContext } from 'react';
import { Space, Typography, Dropdown, Badge, Avatar, Button, Tag, Notification, Popover, Modal, Tooltip, Divider } from '@douyinfe/semi-ui';
import { IconMore, IconArrowUpRight } from '@douyinfe/semi-icons';
import { Flynet } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/flynets_pb";
import { getFlynet } from '@/services/flynet';
import { User, UserRole, UserGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb';
import { GroupType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import DOMPurify from 'dompurify';
import { flylayerClient } from '@/services/core';
import Papa from 'papaparse';

import avatarDefault from '@/assets/avatar_default.jpg';
import { Timestamp } from "@bufbuild/protobuf";
import { useNavigate } from 'react-router-dom';
import styles from './index.module.scss';
import { BASE_PATH } from '@/constants/router';
import DateFormat from '@/components/date-format';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { UserProfileContext } from '@/hooks/useUserProfile';
import { GlobalConfigContext } from '@/hooks/useGlobalConfig';
const { Title, Paragraph } = Typography;

import Handlebars from 'handlebars';
import { parseUser } from '@/utils/user';
import { caseInsensitiveIncludes, generatePassword, generateRandomID } from '@/utils/common';
import { formatDisplayDate } from '@/utils/format';
import { useLocale } from '@/locales';
export type UserFilter = {
    keywords: string,
    role: Array<string>,
    status: 'enable' | 'disable' | '',
    group: string,
    temporary: 'true' | 'false' | ''
}


const useTable = (initFilter: UserFilter) => {
    const { formatMessage } = useLocale();
    const globalConfig = useContext(GlobalConfigContext);

    const [password, setPassword] = useState('');
    const [passwordVisible, setPasswordVisible] = useState(false);

    const templateUserListTitle = Handlebars.compile(globalConfig.template?.userListTitle);
    const flynetGeneral = useContext(FlynetGeneralContext);
    const userProfile = useContext(UserProfileContext);
    const navigate = useNavigate();

    const [flynet, setFlynet] = useState<Flynet>();
    // 用户是否正在加载中
    const [loading, setLoading] = useState(true);
    const [data, setData] = useState<Array<User>>([]);
    const [originData, setOriginData] = useState<Array<User>>([]);
    const [allData, setAllData] = useState<Array<User>>([]);

    // 尚未加入用户数量
    const [otherUsersCount, setOtherUsersCount] = useState(0);

    const [sortOrder, setSortOrder] = useState<'ascend' | 'descend' | undefined>(undefined);
    const [sortField, setSortField] = useState<string>('');

    // 当前页码
    const [page, setPage] = useState(1);
    // 过滤后总数据条数
    const [total, setTotal] = useState(0);

    const pageSize = 20;

    // 编辑用户弹出框是否可见
    const [editVisible, setEditVisible] = useState(false);
    // 添加用户弹出框是否可见
    const [addVisible, setAddVisible] = useState(false);
    // 删除用户弹出框是否可见
    const [delVisible, setDelVisible] = useState(false);
    // 禁用用户弹出框是否可见
    const [suspendVisible, setSuspendVisible] = useState(false);
    // 编辑用户角色弹出框是否可见
    const [editRoleVisible, setEditRoleVisible] = useState(false);
    // 编辑用户组弹出框是否可见
    const [addGroupVisible, setAddGroupVisible] = useState(false);
    // 编辑远程桌面设置弹出框是否可见
    const [editRDPVisible, setEditRDPVisible] = useState(false);
    // 从用户组中删除用户弹出框是否可见
    const [removeUserFromGroupVisible, setRemoveUserFromGroupVisible] = useState(false);
    // 编辑用户组弹出框是否可见
    const [editGroupVisible, setEditGroupVisible] = useState(false);
    // MFA弹出框是否可见
    const [mfaVisible, setMfaVisible] = useState(false);

    const [unlockVisible, setUnlockVisible] = useState(false);

    const [removeMFADeviceVisible, setRemoveMFADeviceVisible] = useState(false);

    // 用户组列表
    const [groups, setGroups] = useState<UserGroup[]>([]);
    // 当前选中用户组名称
    const [curGroup, setCurGroup] = useState<UserGroup>();


    useEffect(() => {
        getFlynet(flynetGeneral.id).then(res => {
            setFlynet(res.flynet)
        })
    }, [])

    // 当前菜单选中设备
    const [curUser, setCurUser] = useState<User>();

    // 是否重新加载数据
    const [reloadFlag, setReloadFlag] = useState(false);
    // 过滤参数
    const [filterParam, setFilterParam] = useState<UserFilter>(initFilter);

    // 有效期修改是否可见
    const [expires_at_visible, setExpiresAtVisible] = useState(false);

    const columns = [
        {
            width: 350,
            title: formatMessage({ id: 'users.table.column.user' }),
            dataIndex: 'name',
            sorter: true,
            render: (_: string, record: User) => {
                const parsedUser = parseUser(record);
                const val = templateUserListTitle({ user: parsedUser });
                // 是否未激活用户
                const isOtherUser = record.avatarUrl == 'avatarUrl';


                return (

                    <div className={styles.name}><Avatar
                        src={record.avatarUrl && !isOtherUser ? record.avatarUrl : avatarDefault}
                        style={{ marginRight: 12 }}
                    ></Avatar>
                        <div>
                            <Title heading={6}>
                                {isOtherUser ? record.loginName : <a onClick={() => setTimeout(() => navigate(`${BASE_PATH}/users/${record.loginName}`), 10)}><span dangerouslySetInnerHTML={{
                                    __html: DOMPurify.sanitize(val, { USE_PROFILES: { html: true } })
                                }} /></a>}
                            </Title>
                            <Paragraph className='mb2'>
                                {record.loginName}
                            </Paragraph>
                            <div><Space>
                                {record.disabled ? <Tag size="small">{formatMessage({ id: 'users.status.disabled' })}</Tag> : ''}
                                {record.temporary && <Tooltip content={formatMessage({ id: 'users.status.expiryDate' }) + '：' + formatDisplayDate(record.expiredAt)}><Tag color='orange' size="small">{formatMessage({ id: 'users.status.temporary' })}</Tag></Tooltip>}
                                {record.expired && <Tag size="small">{formatMessage({ id: 'users.status.expired' })}</Tag>}
                            </Space></div>
                        </div>
                    </div>
                );
            },
        },
        {
            width: 120,
            title: formatMessage({ id: 'users.table.column.roleLocal' }),
            dataIndex: 'role',
            sorter: true,
            render: (field: string, record: User) => {

                let roleName = ''
                switch (record.role) {
                    case UserRole.FLYNET_ADMIN: roleName = formatMessage({ id: 'users.role.admin' }); break;
                    case UserRole.FLYNET_USER: roleName = formatMessage({ id: 'users.role.user' }); break;
                    case UserRole.SUPER_ADMIN: roleName = formatMessage({ id: 'users.role.superAdmin' }); break;
                    default: roleName = formatMessage({ id: 'common.unknown' }); break;
                }

                return roleName
            }
        },
        {
            title: formatMessage({ id: 'users.table.column.groupLocal' }),
            dataIndex: 'group',
            render: (_: any, record: User) => {
                return <Space style={{ flexWrap: 'wrap' }}>{record.userGroups.map((g, i) => <Tag key={i} size='large'>{g.alias} ({g.name})</Tag>)}</Space>
            }
        },
        {
            width: 200,
            title: formatMessage({ id: 'users.table.column.createdAtLocal' }),
            dataIndex: 'createdAt',
            sorter: true,
            render: (field: Timestamp, record: User) => {
                // 是否未激活用户
                const isOtherUser = record.avatarUrl == 'avatarUrl';
                if (isOtherUser) {
                    return <span>{formatMessage({ id: 'users.status.notJoined' })}</span>
                }
                return (
                    <div>
                        <DateFormat date={field}></DateFormat>
                    </div>
                );
            },
        },
        {
            width: 180,
            title: formatMessage({ id: 'users.table.column.lastSeenLocal' }),
            dataIndex: 'lastSeen',
            sorter: true,
            render: (field: Timestamp, record: User) => {
                // 是否未激活用户
                const isOtherUser = record.avatarUrl == 'avatarUrl';
                if (isOtherUser) {
                    return ""
                }
                return record.connected ? <span><Badge dot style={{ backgroundColor: 'var(--semi-color-success)' }} /> {formatMessage({ id: 'users.status.online' })}</span> : <><DateFormat date={field}></DateFormat></>
            }
        },
        {
            title: formatMessage({ id: 'users.table.column.statusLocal' }),
            dataIndex: 'disabled',
            key: 'disabled',
            width: 80,
            render: (fieldd: string, acl: User, index: number) => {
                return <>
                    {acl.disabled ? <Tag color='red'>{formatMessage({ id: 'users.status.disabled' })}</Tag> : <Tag color='green'>{formatMessage({ id: 'users.status.enabled' })}</Tag>}
                </>;
            }
        },
        {
            width: 100,
            title: '',
            dataIndex: 'operate',
            render: (field: string, record: User) => {
                let hasMFADevice = false;
                if (record.account && record.account.credentials) {
                    record.account.credentials.forEach(credential => {
                        if (credential.type == 'totp') {
                            hasMFADevice = true;
                        }
                    })
                }
                // 解除MFA设备


                // 是否是自己
                const isSelf = record.loginName == userProfile.identity?.traits?.email;

                let isOidc = false;
                if(record.account?.credentials && record.account?.credentials.length > 0){
                    record.account?.credentials.forEach((item)=>{
                        if(item.type === 'oidc'){
                            isOidc = true;
                        }
                    })
                }

                // 是否未激活用户
                const isOtherUser = record.avatarUrl == 'avatarUrl';
                if (isOtherUser) {
                    return <div className='table-last-col'><Dropdown
                        position='bottomRight'
                        render={
                            <Dropdown.Menu>
                                <Dropdown.Item onClick={() => { setEditRoleVisible(true); setCurUser(record) }}>{formatMessage({ id: 'users.actions.editRole' })}</Dropdown.Item>
                            </Dropdown.Menu>
                        }
                    >
                        <Button><IconMore className='align-v-center' /></Button>
                    </Dropdown></div>;
                }

                return <div className='table-last-col'><Dropdown
                    position='bottomRight'
                    render={
                        <Dropdown.Menu>
                            <Dropdown.Item onClick={() => navigate(`${BASE_PATH}/devices?keywords=${record.loginName}`)}>{formatMessage({ id: 'users.actions.viewDevices' })}</Dropdown.Item>
                            <Dropdown.Item onClick={() => {
                                navigate(`${BASE_PATH}/logs?keywords=${record.loginName}`)
                            }}>{formatMessage({ id: 'users.actions.viewLogs' })}</Dropdown.Item>
                            <Dropdown.Divider />

                            <Dropdown.Item onClick={() => { setEditVisible(true); setCurUser(record) }}>{formatMessage({ id: 'users.actions.editUser' })}</Dropdown.Item>



                            {flynet && flynet.accountManualCreate && <>
                                <Dropdown.Divider />
                                <Dropdown.Item type='danger' disabled={isSelf || isOidc} onClick={() => {
                                    if (isSelf) { return }

                                    Modal.confirm({
                                        title: formatMessage({ id: 'users.passwordReset.title' }),
                                        content: formatMessage({ id: 'users.passwordReset.description' }),
                                        onOk: () => {
                                            let password = generatePassword(16);
                                            flylayerClient.resetUserPassword({
                                                userId: record.id,
                                                password: password
                                            }).then(() => {
                                                Notification.success({
                                                    title: formatMessage({ id: 'users.passwordReset.success' }),
                                                    onClose: () => {
                                                    }
                                                });
                                                setPassword(password);
                                                setPasswordVisible(true);
                                            }).catch(() => {

                                                Notification.error({
                                                    title: formatMessage({ id: 'users.passwordReset.failed' })
                                                });
                                            })
                                        }
                                    })

                                }
                                }>{formatMessage({ id: 'users.actions.resetPassword' })}</Dropdown.Item>
                            </>
                            }
                            <Dropdown.Item onClick={() => {
                                setUnlockVisible(true);
                                setCurUser(record)
                            }}>{formatMessage({ id: 'users.actions.unlockAccount' })}</Dropdown.Item>
                            <Dropdown.Divider />
                            <Dropdown.Item
                                onClick={() => {
                                    setEditGroupVisible(true);
                                    setCurUser(record);
                                }}
                            >
                                {formatMessage({ id: 'users.actions.editUserGroup' })}
                            </Dropdown.Item>
                            {curGroup && curGroup.type == GroupType.GROUP_STATIC && <Dropdown.Item
                                onClick={() => {
                                    setCurUser(record);
                                    setRemoveUserFromGroupVisible(true);

                                }}
                            >{formatMessage({ id: 'users.actions.removeFromGroup' })}</Dropdown.Item>}
                            <Dropdown.Item onClick={() => { setEditRoleVisible(true); setCurUser(record) }}>{formatMessage({ id: 'users.action.editRole' })}</Dropdown.Item>



                            {
                                flynet?.mfaEnabled && <Dropdown.Item onClick={() => { setMfaVisible(true); setCurUser(record) }}>{formatMessage({ id: 'users.actions.mfaSettings' })}</Dropdown.Item>
                            }
                            <Dropdown.Divider />
                            {record.disabled ?
                                <Dropdown.Item type='danger' disabled={isSelf} onClick={() => {
                                    if (isSelf) { return }
                                    setCurUser(record)
                                    setSuspendVisible(true)
                                }
                                }>{formatMessage({ id: 'users.actions.enableUser' })}</Dropdown.Item>
                                : ''}
                            {!record.disabled ?
                                <Dropdown.Item type='danger' disabled={isSelf} onClick={() => {
                                    if (isSelf) { return }
                                    setCurUser(record)
                                    setSuspendVisible(true)
                                }
                                }>{formatMessage({ id: 'users.actions.disableUser' })}</Dropdown.Item>
                                : ''}
                            <Dropdown.Item type="danger" disabled={isSelf} onClick={() => { if (isSelf) { return }; setDelVisible(true); setCurUser(record) }}>{formatMessage({ id: 'users.actions.deleteUser' })}</Dropdown.Item>
                            <Divider />
                            <Dropdown.Item disabled={isSelf} type='danger' onClick={() => {
                                if (isSelf) {
                                    return;
                                }
                                setCurUser(record);
                                setExpiresAtVisible(true)
                            }}>
                                {formatMessage({ id: 'users.actions.setExpiry' })}
                            </Dropdown.Item>
                            {
                                hasMFADevice && <Dropdown.Item type='danger' onClick={() => {
                                    setRemoveMFADeviceVisible(true);
                                    setCurUser(record);
                                }} >{formatMessage({ id: 'users.actions.removeMFADevice' })}</Dropdown.Item>
                            }
                        </Dropdown.Menu>
                    }
                >
                    <Button><IconMore className='align-v-center' /></Button>
                </Dropdown></div>;
            },
        },
    ];

    const handleSort = (param: any) => {
        const sorter = param.sorter;
        const dataIndex = sorter.dataIndex;
        const sortOrder = sorter.sortOrder;

        let sortedAllDate = [...allData];

        if (sortOrder == 'ascend') {
            setSortOrder('ascend');

            if (dataIndex == 'name') {
                sortedAllDate.sort((a?: User, b?: User) => (a && b && a.displayName < b.displayName ? 1 : -1))
            }
            if (dataIndex == 'role') {
                sortedAllDate.sort((a?: User, b?: User) => (a && b && a.role < b.role ? 1 : -1));
            }
            if (dataIndex == 'createdAt') {
                sortedAllDate.sort((a?: User, b?: User) => {
                    if (!a || !b) return -1;
                    if (a.createdAt && b.createdAt) {
                        return 1;
                    }
                    if (a.createdAt && !b.createdAt) {
                        return -1;
                    }
                    if (!a.createdAt && b.createdAt) {
                        return 1;
                    }
                    if (b.createdAt && a.createdAt && a.createdAt.seconds > b.createdAt.seconds) {
                        return -1;
                    }
                    return -1;
                });
            }
            if (dataIndex == 'lastSeen') {
                sortedAllDate.sort((a?: User, b?: User) => {
                    if (!a || !b) return -1;
                    if (a.connected && b.connected) {
                        return 1;
                    }
                    if (a.connected && !b.connected) {
                        return 1;
                    }
                    if (!a.connected && b.connected) {
                        return -1;
                    }
                    if (b.lastSeen && a.lastSeen && a.lastSeen && b.lastSeen) {
                        return a.lastSeen.seconds > b.lastSeen.seconds ? 1 : -1;
                    }
                    if (a.lastSeen && !b.lastSeen) {
                        return 1;
                    }
                    if (!a.lastSeen && b.lastSeen) {
                        return -1;
                    }
                    return -1;
                });
            }
            setAllData(sortedAllDate);

            setData(doFilter(1, sortedAllDate, filterParam, curGroup))
        } else if (sortOrder == 'descend') {
            setSortOrder('descend');

            if (dataIndex == 'name') {
                sortedAllDate.sort((a?: User, b?: User) => (a && b && a.displayName > b.displayName ? 1 : -1))
            }
            if (dataIndex == 'role') {
                sortedAllDate.sort((a?: User, b?: User) => (a && b && a.role > b.role ? 1 : -1));
            }
            if (dataIndex == 'createdAt') {
                sortedAllDate.sort((a?: User, b?: User) => {
                    if (!a || !b) return 1;
                    if (a.createdAt && b.createdAt) {
                        return -1;
                    }
                    if (a.createdAt && !b.createdAt) {
                        return 1;
                    }
                    if (!a.createdAt && b.createdAt) {
                        return -1;
                    }
                    if (b.createdAt && a.createdAt && a.createdAt.seconds < b.createdAt.seconds) {
                        return 1;
                    }
                    return 1;
                });
            }
            if (dataIndex == 'lastSeen') {
                sortedAllDate.sort((a?: User, b?: User) => {
                    if (!a || !b) return -1;
                    if (a.connected && b.connected) {
                        return 1;
                    }
                    if (a.connected && !b.connected) {
                        return -1;
                    }
                    if (!a.connected && b.connected) {
                        return 1;
                    }
                    if (b.lastSeen && a.lastSeen && a.lastSeen && b.lastSeen) {
                        return a.lastSeen.seconds < b.lastSeen.seconds ? 1 : -1;
                    }
                    if (a.lastSeen && !b.lastSeen) {
                        return -1;
                    }
                    if (!a.lastSeen && b.lastSeen) {
                        return 1;
                    }
                    return 1;
                });
            }

            setAllData(sortedAllDate);
            setData(doFilter(1, sortedAllDate, filterParam, curGroup))
        } else {
            setSortOrder(undefined)
            setAllData(originData)
            setData(doFilter(1, originData, filterParam, curGroup))

        }
        setSortField(dataIndex)



    }
    // 过滤结果
    const doFilter = (page: number, src: Array<User>, filter: UserFilter, group?: UserGroup): Array<User> => {

        if (!src || src.length == 0) {
            setTotal(src.length)

            calOtherUsersCount(src);
            return src.slice(0, page * pageSize);
        }

        if (filter.keywords == '' && filter.role.length == 0 && filter.status == '' && !group && filter.temporary == '') {
            setTotal(src.length)

            calOtherUsersCount(src);
            return src.slice(0, page * pageSize);
        }
        const filteredList = src.filter(record => {
            let { keywords: query, role, status, temporary } = filter;
            query = query.trim()
            let passQuery = true, passRole = true, passStatus = true, passGroup = true, passTemporary = true;
            if (query) {

                if (caseInsensitiveIncludes(record.loginName, query) ||
                    caseInsensitiveIncludes(record.displayName, query)
                ) {
                    passQuery = true;
                } else {
                    passQuery = false;
                }
            }
            if (role.length > 0) {
                passRole = role.indexOf(record.role.toString()) >= 0;
            }

            if (status == 'enable') {
                passStatus = !record.disabled;
            } else if (status == 'disable') {
                passStatus = record.disabled;
            }

            if (temporary == 'true') {
                if (record.temporary) {
                    passTemporary = true;
                } else {
                    passTemporary = false;
                }
            } else if (temporary == 'false') {
                if (!record.temporary) {
                    passTemporary = true;
                } else {
                    passTemporary = false;
                }
            } else {
                passTemporary = true;
            }

            if (group) {
                let findGroup = false;
                group.users.forEach((val: User) => {
                    if (record.id == val.id) {
                        findGroup = true;
                    }
                })
                group.attrs?.otherUsers.forEach((loginName) => {
                    if (loginName == record.loginName) {
                        findGroup = true;
                    }
                });

                if (findGroup) {
                    passGroup = true;
                } else {
                    passGroup = false;
                }
            }

            return passQuery && passRole && passStatus && passGroup && passTemporary;
        })

        setTotal(filteredList.length)
        calOtherUsersCount(filteredList);

        return filteredList.slice(0, page * pageSize)

    }

    // 合并用户和用户组里的未激活用户
    const mergeUserAndGroup = (users: User[], groups: UserGroup[], mapRole: { [key: string]: UserRole }) => {
        if (!groups || groups.length == 0) {
            return users;
        }

        let allUsers: User[] = [];
        users.forEach(u => allUsers.push(u));

        let otherLoginNames: string[] = [];
        users.forEach(user => {
            otherLoginNames.push(user.loginName);
        })

        groups.forEach(group => {
            group.attrs?.otherUsers.forEach(loginName => {
                if (otherLoginNames.indexOf(loginName) < 0) {
                    let user = new User();
                    user.id = generateRandomID();
                    user.loginName = loginName;
                    user.displayName = loginName;
                    user.role = mapRole[loginName] || UserRole.FLYNET_USER;

                    user.avatarUrl = 'avatarUrl';
                    allUsers.push(user);
                    otherLoginNames.push(loginName);
                }
            })
        })


        return allUsers;
    }
    // 从用户表中提取用户组
    const fillGroupsWithUsers = (srcGroups: UserGroup[], users: User[]): UserGroup[] => {
        // 组列表
        let groups = srcGroups;
        users.forEach(user => {
            // 遍历用户的组
            user.userGroups.forEach(g => {
                
                groups.forEach(group => {
                    if (group.id == g.id) {
                        if (!group.users) {
                            group.users = [];
                        }
                        group.users.push(user);
                    }
                })
            })
        })


        return groups;

    }


    const query = async () => {
        const resGroups = await flylayerClient.listUserGroups({
            flynetId: flynetGeneral.id
        });
        

        const resMapRole = await flylayerClient.listUserRoles({
            flynetId: flynetGeneral.id
        });
        const mapRole = resMapRole.userRoles;

        setLoading(true);

        flylayerClient.listUsers({
            flynetId: flynetGeneral.id
        }).then(res => {
            const groups = fillGroupsWithUsers(resGroups?.groups || [], res.users);
            let curGroup: UserGroup | undefined;
            setGroups(groups);
            groups.forEach(g => {
                if (g.id + '' == initFilter.group) {
                    curGroup = g;
                }
            })
            setCurGroup(curGroup);
            const allUsers = mergeUserAndGroup(res.users, groups, mapRole);

            setAllData(allUsers);
            let copyedData: any = []
            allUsers.forEach(user => {
                copyedData.push({ ...user })
            })
            setOriginData(copyedData);
            setData(doFilter(page, allUsers, filterParam, curGroup));
        }).catch(err => {
            Notification.error({ content: formatMessage({ id: 'users.error.getUserListFailed' }) })
            console.error(err)
        }).finally(() => setLoading(false))
    }

    const addPage = () => {
        setData(doFilter(page + 1, allData, filterParam, curGroup));
        setPage(page + 1)
    }

    const calOtherUsersCount = (users: User[]) => {
        let count = 0;
        users.forEach(user => {
            if (user.avatarUrl == 'avatarUrl') {
                count++;
            }
        })
        setOtherUsersCount(count);
    }

    useEffect(() => {
        query()
    }, []);

    useEffect(() => {
        setPage(1)
        setReloadFlag(false)
        if (reloadFlag) {
            query()
        }
    }, [reloadFlag])
    useEffect(() => {
        setPage(1)
        const list = doFilter(1, allData, filterParam, curGroup);
        setData(list);
    }, [filterParam])

    const handleGroupChange = (group?: UserGroup) => {
        const list = doFilter(1, allData, filterParam, group);
        setCurGroup(group);
    };

    const [selectedUsers, setSelectedUsers] = useState<User[]>([]);

    const onRowChange = (selectedRowKeys: (string | number)[] | undefined, selectedRows: User[] | undefined) => {
        if (selectedRows) {
            setSelectedUsers(selectedRows);
            setSelectedRowKeys(selectedRowKeys || []);
        }
    };

    const handleRowClick = (record: User, index: number) => {
        let isRecordSelected = selectedUsers.find(user => user.id == record.id);
        if (isRecordSelected) {
            setSelectedUsers(selectedUsers.filter(user => user.id != record.id));
            setSelectedRowKeys(selectedRowKeys.filter(key => key != record.id + ''));
        } else {
            setSelectedUsers([...selectedUsers, record]);
            setSelectedRowKeys([...selectedRowKeys, record.id + '']);
        }
    }

    const [selectedRowKeys, setSelectedRowKeys] = useState<(string | number)[]>([]);



    const handleExport = () => {
        // interface User4ExportResult {
        //     登录名: string,
        //     显示名: string,
        //     角色名: string,
        //     加入时间: string,
        //     上次在线: string
        // }

        // flylayerClient.exportUsers({
        //     flynetId: flynetGeneral.id
        // }).then(res => {

        //     const user4Exports = res.users.map(user => {
        //         let u: User4ExportResult = {
        //             登录名: user.loginName,
        //             显示名: user.displayName,
        //             角色名: user.role,
        //             加入时间: formatDisplayDate(user.createdAt),
        //             上次在线: formatDisplayDate(user.lastSeen)
        //         }
        //         return u;
        //     });

        //     let csv = Papa.unparse(user4Exports);
        //     let blob = new Blob(["\ufeff" + csv], { type: 'text/csv,charset=UTF-8' });
        //     let a = document.createElement('a');
        //     a.href = URL.createObjectURL(blob);
        //     a.download = `用户数据.csv`;
        //     a.click();
        // }).catch(() => {
        //     Notification.error({ content: '导出用户数据失败, 请稍后重试' })

        // });

    }



    return {
        columns, loading, allData, data,
        onRowChange, selectedRowKeys, setSelectedRowKeys,
        handleRowClick,
        selectedUsers, setSelectedUsers,
        curUser, setCurUser, addVisible, setAddVisible,
        delVisible, setDelVisible,
        suspendVisible,
        setSuspendVisible,
        editRDPVisible, setEditRDPVisible,
        editRoleVisible, setEditRoleVisible,
        editVisible, setEditVisible,
        reloadFlag, setReloadFlag,
        filterParam, setFilterParam,
        page, pageSize, addPage, setPage, total,
        handleSort, addGroupVisible, setAddGroupVisible,
        flynet,
        password, setPassword,
        passwordVisible, setPasswordVisible,
        expires_at_visible, setExpiresAtVisible,
        groups,
        curGroup,
        setCurGroup,
        handleGroupChange,
        otherUsersCount,
        editGroupVisible,
        setEditGroupVisible,
        removeUserFromGroupVisible,
        setRemoveUserFromGroupVisible,
        mfaVisible,
        setMfaVisible,
        removeMFADeviceVisible,
        setRemoveMFADeviceVisible,
        handleExport,
        unlockVisible,
        setUnlockVisible
    }
}

export default useTable;