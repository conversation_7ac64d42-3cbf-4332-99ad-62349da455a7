import { useEffect, useState, useContext, useCallback } from 'react';
import { Space, Typography, Dropdown, Badge, Avatar, Button, Tag, Notification, Popover, Modal, RadioGroup, Radio, Tooltip, Divider, Select, CheckboxGroup, Checkbox } from '@douyinfe/semi-ui';
import { IconMore, IconArrowUpRight, IconPlusCircle, IconMinusCircle } from '@douyinfe/semi-icons';
import { Flynet } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/flynets_pb";
import { getFlynet } from '@/services/flynet';
import { User, UserRole, UserGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb';
import DOMPurify from 'dompurify';
import { flylayerClient } from '@/services/core';
import Papa from 'papaparse';
import qs from 'query-string';

import avatarDefault from '@/assets/avatar_default.jpg';
import { Timestamp } from "@bufbuild/protobuf";
import { useNavigate } from 'react-router-dom';
import styles from './index.module.scss';
import { BASE_PATH } from '@/constants/router';
import DateFormat from '@/components/date-format';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { UserProfileContext } from '@/hooks/useUserProfile';
import { GlobalConfigContext } from '@/hooks/useGlobalConfig';
const { Title, Paragraph, Text } = Typography;

import { FilterParam } from '@/components/search-filter-combo';

import Handlebars from 'handlebars';
import { parseUser } from '@/utils/user';
import { generatePassword, generateRandomID } from '@/utils/common';
import { formatDisplayDate } from '@/utils/format';
import { debounce } from 'lodash';
import { useLocale } from '@/locales';
export type UserFilter = {
    keywords: string,
    roles: string,
    status: 'enable' | 'disable' | '',
    groups: string,
    temporary: 'true' | 'false' | ''
}


const useTable = (initFilter: UserFilter) => {
    const { formatMessage } = useLocale();
    const flynetGeneral = useContext(FlynetGeneralContext);
    const userProfile = useContext(UserProfileContext);
    const navigate = useNavigate();
    const globalConfig = useContext(GlobalConfigContext);

    const [password, setPassword] = useState('');
    const [passwordVisible, setPasswordVisible] = useState(false);

    const templateUserListTitle = Handlebars.compile(globalConfig.template?.userListTitle);

    const [flynet, setFlynet] = useState<Flynet>();

    const [loading, setLoading] = useState(true);
    const [data, setData] = useState<Array<User>>([]);

    const [sortOrder, setSortOrder] = useState<'ASC' | 'DESC' | undefined>(undefined);
    const [sortField, setSortField] = useState<string>('');

    const [page, setPage] = useState(1);
    const [total, setTotal] = useState(0);

    const pageSize = 20;

    const [editVisible, setEditVisible] = useState(false);
    const [addVisible, setAddVisible] = useState(false);
    const [delVisible, setDelVisible] = useState(false);

    const [suspendVisible, setSuspendVisible] = useState(false);
    const [editRoleVisible, setEditRoleVisible] = useState(false);
    const [addGroupVisible, setAddGroupVisible] = useState(false);
    const [editRDPVisible, setEditRDPVisible] = useState(false);

    const [removeUserFromGroupVisible, setRemoveUserFromGroupVisible] = useState(false);
    const [editGroupVisible, setEditGroupVisible] = useState(false);
    const [mfaVisible, setMfaVisible] = useState(false);
    const [unlockVisible, setUnlockVisible] = useState(false);
    const [removeMFADeviceVisible, setRemoveMFADeviceVisible] = useState(false);

    const [groups, setGroups] = useState<UserGroup[]>([]);

    const [filterParams, setFilterParams] = useState<FilterParam[]>([]);

    const initFilterParams = (userGroups: UserGroup[]) => {
        setFilterParams([{
            name: 'keywords',
            placeholder: formatMessage({ id: 'users.index.filter.searchPlaceholder' }),
            label: formatMessage({ id: 'users.button.query' }),
            value: initFilter.keywords || '',
        },
        {
            name: 'status',
            placeholder: formatMessage({ id: 'users.index.filter.status' }),
            label: formatMessage({ id: 'users.index.filter.status' }),
            fixed: true,
            value: initFilter.status || '',
            filterComponent: ({ value, onChange }) => {
                return <Select insetLabel={formatMessage({ id: 'users.index.filter.status' })} value={value} onChange={(val) => onChange(val as string)}>
                    <Select.Option value=''>{formatMessage({ id: 'components.common.all' })}</Select.Option>
                    <Select.Option value='enable'>{formatMessage({ id: 'users.status.enabled' })}</Select.Option>
                    <Select.Option value='disable'>{formatMessage({ id: 'users.status.disabled' })}</Select.Option>
                </Select>

            },
            funGetDisplayValue: (val: string) => {
                if (val == 'enable') {
                    return formatMessage({ id: 'users.status.enabled' });
                } else if (val == 'disable') {
                    return formatMessage({ id: 'users.status.disabled' });
                }
                return '';
            }
        }, {
            name: 'groups',
            placeholder: formatMessage({ id: 'users.index.filter.userGroupPlaceholder' }),
            label: formatMessage({ id: 'users.index.filter.userGroup' }),
            value: initFilter.groups || '',
            filterComponent: ({ value, onChange }) => {
                return <Select
                    position='top'
                    value={value ? value.split(',') : []}
                    onChange={(val) => {
                        const value = val as string[];
                        onChange(value.join(','));
                    }}
                    style={{ width: 180 }}
                    placeholder={formatMessage({ id: 'users.filter.selectUserGroups' })}
                    optionList={[
                        ...userGroups.map(g => {
                            return { value: g.id + '', label: g.alias }
                        })
                    ]}
                    multiple
                    maxTagCount={1}
                >
                </Select>;
            },
            funGetDisplayValue: (value: string) => {
                let names: string[] = [];
                if (value) {
                    let val = value.split(',');
                    val.forEach(v => {
                        let group = userGroups.find(g => g.id + '' == v);
                        if (group) {
                            names.push(group.alias);
                        }
                    });
                }
                return names.join(',');
            }
        }, {
            name: 'temporary',
            placeholder: formatMessage({ id: 'users.index.filter.userTypePlaceholder' }),
            label: formatMessage({ id: 'users.index.filter.userType' }),
            value: initFilter.temporary || '',
            filterComponent: ({ value, onChange }) => {

                return <RadioGroup
                    value={value}
                    onChange={(val) => {
                        onChange(val.target.value);
                    }}
                    style={{ width: 180 }}
                    options={[
                        { value: 'true', label: formatMessage({ id: 'users.type.temporary' }) },
                        { value: 'false', label: formatMessage({ id: 'users.type.regular' }) }
                    ]}
                >
                </RadioGroup>
            },
            funGetDisplayValue: (val: string) => {
                if (val == 'true') {
                    return formatMessage({ id: 'users.type.temporary' });
                } else if (val == 'false') {
                    return formatMessage({ id: 'users.type.regular' });
                }
                return '';
            }
        }])

    }

    useEffect(() => {
        getFlynet(flynetGeneral.id).then(res => {
            setFlynet(res.flynet)
        })
    }, [])

    const [curUser, setCurUser] = useState<User>();

    const [reloadFlag, setReloadFlag] = useState(false);

    const [filter, setFilter] = useState<UserFilter>(initFilter);

    const [expiresAtVisible, setExpiresAtVisible] = useState(false);
    const doNavigate = (param: UserFilter) => {
        let query = '';
        if (param.temporary || param.keywords || param.status || param.roles || param.groups) {
            query = qs.stringify(param, {
                skipEmptyString: true
            });
        }
        if (query) {
            navigate(`${BASE_PATH}/users?${query}`)
        } else {
            navigate(`${BASE_PATH}/users`)
        }
    }

    const columns = [
        {
            width: 470,
            title: formatMessage({ id: 'users.table.user' }),
            dataIndex: 'name',
            sorter: true,
            render: (_: string, record: User) => {
                const parsedUser = parseUser(record);
                const val = templateUserListTitle({ user: parsedUser });

                const isOtherUser = record.avatarUrl == 'avatarUrl';

                return (
                    <div className={styles.name}><Avatar
                        src={record.avatarUrl && !isOtherUser ? record.avatarUrl : avatarDefault}
                        style={{ marginRight: 12 }}
                    ></Avatar>
                        <div>
                            <Title heading={6}>
                                {isOtherUser ? record.loginName : <a onClick={() => setTimeout(() => navigate(`${BASE_PATH}/users/${record.loginName}`), 10)}><span dangerouslySetInnerHTML={{
                                    __html: DOMPurify.sanitize(val, { USE_PROFILES: { html: true } })
                                }} /></a>}
                            </Title>
                            <Paragraph className='mb2'>
                                {record.loginName}
                            </Paragraph>
                            <div><Space>
                                {record.disabled ? <Tag size="small"> {formatMessage({ id: 'users.status.disabled' })} </Tag> : ''}
                                {record.temporary && <Tooltip content={formatMessage({ id: 'users.field.expiresAt' }) + '：' + formatDisplayDate(record.expiredAt)}><Tag color='orange' size="small"> {formatMessage({ id: 'users.type.temporary' })} </Tag></Tooltip>}
                                {record.expired && <Tag size="small"> {formatMessage({ id: 'users.status.expired' })} </Tag>}
                            </Space></div>
                        </div>
                    </div>
                );
            },
        },
        {
            width: 150,
            title: formatMessage({ id: 'users.table.role' }),
            dataIndex: 'role',

            render: (field: string, record: User) => {

                let roleName = ''
                switch (record.role) {
                    case UserRole.FLYNET_ADMIN: roleName = formatMessage({ id: 'users.role.admin' }); break;
                    case UserRole.FLYNET_USER: roleName = formatMessage({ id: 'users.role.user' }); break;
                    case UserRole.SUPER_ADMIN: roleName = formatMessage({ id: 'users.role.superAdmin' }); break;
                    default: roleName = formatMessage({ id: 'components.common.unknown' }); break;
                }

                return roleName
            }
        },
        {
            title: formatMessage({ id: 'users.table.userGroups' }),
            dataIndex: 'group',
            render: (_: any, record: User) => {
                return <Space style={{ flexWrap: 'wrap' }}>
                    {record.userGroups.map((g, i) =>
                        <Tag key={i}>
                            {filter.groups && filter.groups.indexOf(g.id + '') >= 0 &&
                                <Tooltip content={<>{formatMessage({ id: 'users.action.removeFromFilter' })}</>}>
                                    <IconMinusCircle className={styles.addFilterIcon} onClick={(e) => {
                                        e.stopPropagation();
                                        if (filterParams) {
                                            const newFilterParams = filterParams.map(f => {
                                                if (f.name == 'groups') {
                                                    let groups: string[] = f.value ? f.value.split(',') : [];
                                                    if (groups.indexOf(g.id + '') >= 0) {
                                                        groups = groups.filter(item => item != (g.id + ''));
                                                    }
                                                    return {
                                                        ...f,
                                                        value: groups.join(',')
                                                    }
                                                }
                                                return f;
                                            })
                                            setFilterParams(newFilterParams);
                                            let groups = filter.groups ? filter.groups.split(',') : [];
                                            groups = groups.filter(item => item != (g.id + ''));
                                            const newFilter = {
                                                ...filter,
                                                groups: groups.join(',')
                                            }
                                            setFilter(newFilter);
                                            doNavigate(newFilter);
                                            handleFilterChange(newFilter);
                                        }
                                    }} />
                                </Tooltip>}
                            {(!filter.groups || filter.groups == '' || filter.groups.indexOf(g.id + '') < 0) &&
                                <Tooltip content={<>{formatMessage({ id: 'users.action.addToFilter' })}</>}>
                                    <IconPlusCircle className={styles.addFilterIcon} onClick={(e) => {
                                        e.stopPropagation();
                                        if (filterParams) {
                                            const newFilterParams = filterParams.map(f => {
                                                if (f.name == 'groups') {
                                                    let groups = f.value ? f.value.split(',') : [];
                                                    if (groups.indexOf(g.id + '') < 0) {
                                                        groups.push(g.id + '');
                                                    }
                                                    return {
                                                        ...f,
                                                        value: groups.join(',')
                                                    }
                                                }
                                                return f;
                                            })
                                            setFilterParams(newFilterParams);
                                            let groups = filter.groups ? filter.groups.split(',') : [];
                                            if (groups.indexOf(g.id + '') < 0) {
                                                groups.push(g.id + '');
                                            }
                                            const newFilter = {
                                                ...filter,
                                                groups: groups.join(',')
                                            }
                                            setFilter(newFilter);
                                            doNavigate(newFilter);
                                            handleFilterChange(newFilter);
                                        }
                                    }} />
                                </Tooltip>}
                            <Tooltip content={<>{g.alias} ({g.name})</>}><Text style={{ maxWidth: 200, fontSize: '12px' }} size='small' ellipsis>{g.alias}</Text></Tooltip>

                        </Tag>)}</Space>
            }
        },
        {
            width: 200,
            title: formatMessage({ id: 'users.table.joinTime' }),
            dataIndex: 'created_At',
            sorter: true,
            render: (field: Timestamp, record: User) => {
                const isOtherUser = record.avatarUrl == 'avatarUrl';
                if (isOtherUser) {
                    return <span>{formatMessage({ id: 'users.status.notJoined' })}</span>
                }
                return (
                    <div>
                        <DateFormat date={record.createdAt}></DateFormat>
                    </div>
                );
            },
        },
        {
            width: 180,
            title: formatMessage({ id: 'users.table.lastSeen' }),
            dataIndex: 'lastSeen',
            render: (field: Timestamp, record: User) => {
                const isOtherUser = record.avatarUrl == 'avatarUrl';
                if (isOtherUser) {
                    return ""
                }
                return record.connected ? <span><Badge dot style={{ backgroundColor: 'var(--semi-color-success)' }} /> {formatMessage({ id: 'components.common.online' })}</span> : <><DateFormat date={field}></DateFormat></>
            }
        },
        {
            title: formatMessage({ id: 'users.table.status' }),
            dataIndex: 'disabled',
            key: 'disabled',
            width: 80,
            render: (fieldd: string, acl: User, index: number) => {
                return <>
                    {acl.disabled ? <Tag color='red'>{formatMessage({ id: 'users.status.disabled' })}</Tag> : <Tag color='green'>{formatMessage({ id: 'users.status.enabled' })}</Tag>}
                </>;
            }
        },
        {
            width: 100,
            title: '',
            dataIndex: 'operate',
            render: (field: string, record: User) => {
                let hasMFADevice = false;
                if (record.account && record.account.credentials) {
                    record.account.credentials.forEach(credential => {
                        if (credential.type == 'totp') {
                            hasMFADevice = true;
                        }
                    })
                }
                const isSelf = record.loginName == userProfile.identity?.traits?.email;


                let isOidc = false;
                if (record.account?.credentials && record.account?.credentials.length > 0) {
                    record.account?.credentials.forEach((item) => {
                        if (item.type === 'oidc') {
                            isOidc = true;
                        }
                    })
                }


                const isOtherUser = record.avatarUrl == 'avatarUrl';
                if (isOtherUser) {
                    return <div className='table-last-col'><Dropdown
                        position='bottomRight'
                        render={
                            <Dropdown.Menu>
                                <Dropdown.Item onClick={() => { setEditRoleVisible(true); setCurUser(record) }}>{formatMessage({ id: 'users.action.editRole' })}</Dropdown.Item>
                            </Dropdown.Menu>
                        }
                    >
                        <Button><IconMore className='align-v-center' /></Button>
                    </Dropdown></div>;
                }

                return <div className='table-last-col'><Dropdown
                    position='bottomRight'
                    render={
                        <Dropdown.Menu>
                            <Dropdown.Item onClick={() => navigate(`${BASE_PATH}/devices?keywords=${record.loginName}`)}>{formatMessage({ id: 'users.action.viewDevices' })}</Dropdown.Item>
                            <Dropdown.Item onClick={() => {
                                navigate(`${BASE_PATH}/logs?keywords=${record.loginName}`)
                            }}>{formatMessage({ id: 'users.action.viewLogs' })}</Dropdown.Item>
                            <Dropdown.Divider />

                            <Dropdown.Item onClick={() => { setEditVisible(true); setCurUser(record) }}>{formatMessage({ id: 'users.action.editUser' })}</Dropdown.Item>



                            {flynet && flynet.accountManualCreate && <>
                                <Dropdown.Divider />
                                <Dropdown.Item type='danger' disabled={isSelf || isOidc} onClick={() => {
                                    if (isSelf) { return }

                                    Modal.confirm({
                                        title: formatMessage({ id: 'users.action.resetPassword' }),
                                        content: formatMessage({ id: 'users.action.resetPasswordConfirm' }),
                                        onOk: () => {
                                            let password = generatePassword(16);
                                            flylayerClient.resetUserPassword({
                                                userId: record.id,
                                                password: password
                                            }).then(() => {
                                                Notification.success({
                                                    title: formatMessage({ id: 'users.action.resetPasswordSuccess' }),
                                                    onClose: () => {
                                                    }
                                                });
                                                setPassword(password);
                                                setPasswordVisible(true);
                                            }).catch(() => {

                                                Notification.success({
                                                    title: formatMessage({ id: 'users.action.resetPasswordFailed' })
                                                });
                                            })
                                        }
                                    })

                                }}
                                >{formatMessage({ id: 'users.action.resetPassword' })}</Dropdown.Item>
                            </>
                            }
                            <Dropdown.Item onClick={() => {
                                setUnlockVisible(true);
                                setCurUser(record)
                            }}>{formatMessage({ id: 'users.action.unlockAccount' })}</Dropdown.Item>
                            <Dropdown.Divider />
                            <Dropdown.Item
                                onClick={() => {
                                    setEditGroupVisible(true);
                                    setCurUser(record);
                                }}
                            >
                                {formatMessage({ id: 'users.action.editUserGroups' })}
                            </Dropdown.Item>
                            {!flynet?.rdpEnabled && <Popover zIndex={3001} content={<div className='p10'>远程桌面功能未开启，请在<a target='_blank' href={`${BASE_PATH}/settings/device`} className='link-external'>设置/设备/远程桌面<IconArrowUpRight /></a>开启</div>}>
                                <Dropdown.Item disabled >远程桌面设置</Dropdown.Item>
                            </Popover>}
                            {flynet?.rdpEnabled && <Dropdown.Item onClick={() => { setEditRDPVisible(true); setCurUser(record) }}>远程桌面设置</Dropdown.Item>}
                            <Dropdown.Divider />


                            <Dropdown.Divider />
                            {record.disabled ?
                                <Dropdown.Item type='danger' disabled={isSelf} onClick={() => {
                                    if (isSelf) { return }
                                    setCurUser(record)
                                    setSuspendVisible(true)
                                }}
                                >{formatMessage({ id: 'users.action.enableUser' })}</Dropdown.Item>
                                : ''}
                            {!record.disabled ?
                                <Dropdown.Item type='danger' disabled={isSelf} onClick={() => {
                                    if (isSelf) { return }
                                    setCurUser(record)
                                    setSuspendVisible(true)
                                }}
                                >{formatMessage({ id: 'users.action.disableUser' })}</Dropdown.Item>
                                : ''}
                            <Dropdown.Item type="danger" disabled={isSelf} onClick={() => { if (isSelf) { return }; setDelVisible(true); setCurUser(record) }}>{formatMessage({ id: 'users.action.deleteUser' })}</Dropdown.Item>
                            <Divider />
                            <Dropdown.Item disabled={isSelf} type='danger' onClick={() => {
                                if (isSelf) {
                                    return;
                                }
                                setCurUser(record);
                                setExpiresAtVisible(true)
                            }}>
                                {formatMessage({ id: 'users.action.setExpiration' })}
                            </Dropdown.Item>
                            {
                                hasMFADevice && <Dropdown.Item type='danger' onClick={() => {
                                    setRemoveMFADeviceVisible(true);
                                    setCurUser(record);
                                }} >{formatMessage({ id: 'users.action.removeMFADevice' })}</Dropdown.Item>
                            }
                        </Dropdown.Menu>
                    }
                >
                    <Button><IconMore className='align-v-center' /></Button>
                </Dropdown></div>;
            },
        },
    ];

    const handleSort = (param: any) => {
        const sorter = param.sorter;
        const dataIndex = sorter.dataIndex;

        let sortOrder = '';
        if (sorter.sortOrder == 'ascend') {
            sortOrder = 'ASC';
        } else if (sorter.sortOrder == 'descend') {
            sortOrder = 'DESC';
        }
        queryUser({
            page: 1,
            sortOrder: sortOrder,
            sortField: dataIndex,
        })

    }

    const query = async () => {

        const resMapRole = await flylayerClient.listUserRoles({
            flynetId: flynetGeneral.id
        });
        const mapRole = resMapRole.userRoles;

        const resUserGroups = await flylayerClient.listUserGroups({
            flynetId: flynetGeneral.id,
        })
        const groups = resUserGroups.groups;
        initFilterParams(groups);
        setGroups(groups);

        queryUser({
            userGroups: groups
        });
    }

    const queryUser = (params?: {
        filter?: UserFilter,
        sortOrder?: string,
        sortField?: string,
        page?: number,
        pageSize?: number,
        userGroups?: Array<UserGroup>
    }) => {
        let queryArray = [];

        let limit = pageSize;
        let curPage = page;

        let filterRoles = filter.roles || [];
        let filterGroups = filter.groups || [];
        let filterStatus = filter.status;
        let filterTemporary = filter.temporary;
        let filterKeywords = filter.keywords;
        let filterSortOrder = sortOrder;
        let filterSortField = sortField;

        if (params) {
            if (params.filter) {
                setFilter(params.filter);

                filterRoles = params.filter.roles;
                filterGroups = params.filter.groups;
                filterStatus = params.filter.status;
                filterTemporary = params.filter.temporary;
                filterKeywords = params.filter.keywords;
            }

            if (params.sortOrder && params.sortField) {
                setSortOrder(params.sortOrder as any);
                setSortField(params.sortField);

                filterSortOrder = params.sortOrder as any;
                filterSortField = params.sortField;
            } else {
                filterSortOrder = undefined;
                filterSortField = '';
                setSortOrder(undefined);
                setSortField('');
            }

            if (params.page) {
                curPage = params.page;
                setPage(params.page);
            }

            if (params.pageSize) {
                limit = params.pageSize;
            }
        }


        if (filterRoles && filterRoles.length > 0) {
            queryArray.push(`roles=${filterRoles}`);
        }

        if (filterGroups) {
            queryArray.push(`groups=${filterGroups}`);
        }

        if (filterStatus == 'enable') {
            queryArray.push(`disabled=false`);
        } else if (filterStatus == 'disable') {
            queryArray.push('disabled=true');
        }

        if (filterTemporary == 'true') {
            queryArray.push('temporary=true');
        } else if (filterTemporary == 'false') {
            queryArray.push('temporary=false');
        }

        if (filterKeywords != "" && filterKeywords.trim() != "") {
            queryArray.push(`keywords=${encodeURIComponent(filterKeywords)}`);
        }


        if (filterSortOrder && filterSortField) {
            setSortOrder(filterSortOrder as any);
            setSortField(filterSortField);

            let order_by = encodeURIComponent(`${filterSortField} ${filterSortOrder}`);
            queryArray.push(`order_by=${order_by}`);
        }

        const offset = (curPage - 1) * limit;

        queryArray.push(`limit=${limit}`);
        queryArray.push(`offset=${offset}`);

        setLoading(true);
        flylayerClient.listUsers({
            flynetId: flynetGeneral.id,
            query: queryArray.join('&')
        }).then(res => {
            if (curPage == 1) {
                setData(res.users);
            } else {
                if (data) {
                    setData(data.concat(res.users))
                } else {
                    setData(res.users)
                }
            }
            setTotal(Number(res.total))
        }).catch(err => {
            Notification.error({ content: formatMessage({ id: 'users.error.fetchUserListFailed' }) })
            console.error(err)
        }).finally(() => {
            setLoading(false);
        })

    }

    const addPage = () => {
        queryUser({
            page: page + 1,
            sortOrder: sortOrder,
            sortField: sortField,
        })
    }

    useEffect(() => {
        query()
    }, []);

    useEffect(() => {
        setReloadFlag(false)
        if (reloadFlag) {
            queryUser({
                page: 1,
                sortOrder: sortOrder,
                sortField: sortField,
            })
        }
    }, [reloadFlag])

    const debounceQuery = useCallback(debounce((filterParam) => queryUser(filterParam), 500), [])

    const handleFilterChange = (filter: UserFilter) => {
        debounceQuery({
            filter: filter,
            page: 1,
            sortOrder: sortOrder,
            sortField: sortField,
            pageSize: pageSize,
            userGroups: groups
        })
    }


    const [selectedUsers, setSelectedUsers] = useState<User[]>([]);

    const onRowChange = (selectedRowKeys: (string | number)[] | undefined, selectedRows: User[] | undefined) => {
        if (selectedRows) {
            setSelectedUsers(selectedRows);
            setSelectedRowKeys(selectedRowKeys || []);
        }
    };

    const handleRowClick = (record: User, index: number) => {
        let isRecordSelected = selectedUsers.find(user => user.id == record.id);
        if (isRecordSelected) {
            setSelectedUsers(selectedUsers.filter(user => user.id != record.id));
            setSelectedRowKeys(selectedRowKeys.filter(key => key != record.id + ''));
        } else {
            setSelectedUsers([...selectedUsers, record]);
            setSelectedRowKeys([...selectedRowKeys, record.id + '']);
        }
    }

    const [selectedRowKeys, setSelectedRowKeys] = useState<(string | number)[]>([]);



    const handleExport = () => {
        // Export functionality is handled by data-export module
    }



    return {
        columns, loading, data,
        onRowChange, selectedRowKeys, setSelectedRowKeys,
        handleRowClick,
        selectedUsers, setSelectedUsers,
        curUser, setCurUser, addVisible, setAddVisible,
        delVisible, setDelVisible,
        suspendVisible,
        setSuspendVisible,
        editRDPVisible, setEditRDPVisible,
        editRoleVisible, setEditRoleVisible,
        editVisible, setEditVisible,
        reloadFlag, setReloadFlag,
        filter, setFilter,
        page, pageSize, addPage, setPage, total,
        handleSort, addGroupVisible, setAddGroupVisible,
        flynet,
        password, setPassword,
        passwordVisible, setPasswordVisible,
        expires_at_visible: expiresAtVisible, setExpiresAtVisible,
        groups,
        // curGroup,
        // setCurGroup,
        // handleGroupChange,
        // otherUsersCount,
        editGroupVisible,
        setEditGroupVisible,
        removeUserFromGroupVisible,
        setRemoveUserFromGroupVisible,
        mfaVisible,
        setMfaVisible,
        removeMFADeviceVisible,
        setRemoveMFADeviceVisible,
        handleExport,
        unlockVisible,
        setUnlockVisible,
        filterParams,
        setFilterParams,
        handleFilterChange,
        doNavigate
    }
}

export default useTable;