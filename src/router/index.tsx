import { lazy, FC } from 'react'
import { RouteObject } from 'react-router'
import { useRoutes } from 'react-router-dom'
import { WrapperRouteComponent, WrapperRouteWithOutLayoutComponent, WrapperPageComponent } from './config'

import { VITE_LOCAL_PAGER_AND_FILTER, VITE_USE_LICENSE } from '@/utils/service';
import HomePage from '@/layouts/home';
import HomeLicensePage from '@/layouts/home/<USER>';

const LayoutPage = VITE_USE_LICENSE ? HomeLicensePage : HomePage;

import LayoutSimplePage from '@/layouts/home-simple';
import WelcomeLayoutPage from '@/layouts/welcome';
import ErrorPage from '@/layouts/error';

import Error from '@/components/error';
import { BASE_PATH } from '@/constants/router';

const DashboardPage = lazy(() => import('@/pages/dashboard'));
const DashboardDeviceInfo = lazy(() => import('@/pages/dashboard/device-info'));
const DashboardDeviceNetwork = lazy(() => import('@/pages/dashboard/device-network'));
const DashboardMonitorDevice = lazy(() => import('@/pages/dashboard/monitor-device'));
const DashboardMonitorInterface = lazy(() => import('@/pages/dashboard/monitor-interface'));

const OverviewPage = lazy(() => import('@/pages/overview'));
const NetworksPage = lazy(() => import('@/pages/networks'));
const DevicesPage = lazy(() => VITE_LOCAL_PAGER_AND_FILTER ? import('@/pages/devices/index-local') : import('@/pages/devices'));
const DeviceDetailPage = lazy(() => import('@/pages/devices/detail'));
const DeviceLogsPage = lazy(() => import('@/pages/devices/logs'));
const DeviceGroup = lazy(() => import('@/pages/devices/group'));
const DeviceImportPage = lazy(() => import('@/pages/devices/data-import'));
const DeviceImportDetailPage = lazy(() => import('@/pages/devices/data-import/detail'));
const DeviceExportPage = lazy(() => import('@/pages/devices/data-export'));

const UsersPage = lazy(() => VITE_LOCAL_PAGER_AND_FILTER ? import('@/pages/users/index-local') : import('@/pages/users'));
const UsersDetailPage = lazy(() => import('@/pages/users/detail'));
const UsersGroupPage = lazy(() => import('@/pages/users/group'));
const UsersGroupDetailPage = lazy(() => import('@/pages/users/group-detail'));
const UsersImportPage = lazy(() => import('@/pages/users/data-import'));
const UsersImportDetailPage = lazy(() => import('@/pages/users/data-import/detail'));
const UsersExportPage = lazy(() => import('@/pages/users/data-export'));
const UsersExportDetailPage = lazy(() => import('@/pages/users/data-export/detail'));

const LogsPage = lazy(() => import('@/pages/logs'));
const LogsPushConfigPage = lazy(() => import('@/pages/logs/push-config'));
const LogsPushConfigDetailPage = lazy(() => import('@/pages/logs/push-config/detail'));
// const DnsPage = lazy(() => import('@/pages/dns'));
const AccessControlsPage = lazy(() => import('@/pages/access-controls'));

const ServicesPage = lazy(() => VITE_LOCAL_PAGER_AND_FILTER ? import('@/pages/services/index-local') : import('@/pages/services'));
const ServicesDetailPage = lazy(() => import('@/pages/services/detail'));
const ServicesGroupPage = lazy(() => import('@/pages/services/group'));
const ServiceGroupDetailPage = lazy(() => import('@/pages/services/group-detail'));
const ServicesDevicesPage = lazy(() => import('@/pages/services/devices'));
const ServicesImportPage = lazy(() => import('@/pages/services/import'));
const ServiceApplicationPage = lazy(() => import('@/pages/services/application/index-wrap'));

const WelcomePage = lazy(() => import('@/pages/welcome'));
const TrialPage = lazy(() => import('@/pages/trial'));
const TrialPersonalPage = lazy(() => import('@/pages/trial/personal'));
const TrialTeamPage = lazy(() => import('@/pages/trial/team'));

// const PoliciesPage = lazy(() => VITE_LOCAL_PAGER_AND_FILTER ? import('@/pages/policies/index-local') : import('@/pages/policies'));
const PoliciesPage = lazy(() => import('@/pages/policies'));
const PoliciesGroupPage = lazy(() => import('@/pages/policies/group'));
const PoliciesWrapPage = lazy(() => import('@/pages/policies-wrap'));

const PoliciesImportPage = lazy(() => import('@/pages/policies/data-import'));
const PoliciesImportDetailPage = lazy(() => import('@/pages/policies/data-import/detail'));
const PoliciesExportPage = lazy(() => import('@/pages/policies/data-export'));
const PoliciesExportDetailPage = lazy(() => import('@/pages/policies/data-export/detail'));


// const PoliciesAdmissionPage = lazy(() => import('@/pages/admission'));

const EntranceBaselineCheckPage = lazy(() => import('@/pages/entrance/baseline-check'));
const EntranceAppCheckPage = lazy(() => import('@/pages/entrance/app-check'));
const EntranceProcessCheckPage = lazy(() => import('@/pages/entrance/process-check'));

const EntranceDetailPage = lazy(() => import('@/pages/entrance/detail'));
const EntranceBaselineCheckDetailPage = lazy(() => import('@/pages/entrance/baseline-check/detail'));
const EntranceAppCheckDetailPage = lazy(() => import('@/pages/entrance/app-check/detail'));
const EntranceProcessCheckDetailPage = lazy(() => import('@/pages/entrance/process-check/detail'));

const GuidePage = lazy(() => import('@/pages/guide'));
const GuideNetworking = lazy(() => import('@/pages/guide-networking'));
const GuideVpn = lazy(() => import('@/pages/guide-vpn'));
const GuideIntranetPenetration = lazy(() => import('@/pages/guide-intranet-penetration'));

const FlynetSelectorPage = lazy(() => import('@/pages/flynet-selector'));

const SettingsPage = lazy(() => import('@/pages/settings'));
const SettingsGeneralPage = lazy(() => import('@/pages/settings/general'));
const SettingsUserPage = lazy(() => import('@/pages/settings/user'));
const SettingsDevicePage = lazy(() => import('@/pages/settings/device'));
const SettingsKeysPage = lazy(() => import('@/pages/settings/keys'));
const SettingsFlynetPage = lazy(() => import('@/pages/settings/flynet'));
const SettingsClientPage = lazy(() => import('@/pages/settings/client'));
const SettingsSchemaPage = lazy(() => import('@/pages/settings/schema'));
const SettingsImportPage = lazy(() => import('@/pages/settings/datasource/import'));
const SettingsImportSangforPage = lazy(() => import('@/pages/settings/datasource/import/sangfor'));
const SettingsImportSangforUserPage = lazy(() => import('@/pages/settings/datasource/import/sangfor/user-import'));
const SettingsImportSangforServicePage = lazy(() => import('@/pages/settings/datasource/import/sangfor/service-import'));
const SettingsImportSangforAclPage = lazy(() => import('@/pages/settings/datasource/import/sangfor/acl-import'));
const SettingsUsersImportPage = lazy(() => import('@/pages/settings/datasource/import/users'));
const SettingsUsersImportOidcPage = lazy(() => import('@/pages/settings/datasource/import/users/oidc'));
const SettingsPoliciesImportPage = lazy(() => import('@/pages/settings/datasource/import/policies'));
const SettingsDatasourcePage = lazy(() => import('@/pages/settings/datasource'));
const SettingsApplicationPage = lazy(() => import('@/pages/settings/application'));
const SettingsLogsPage = lazy(() => import('@/pages/settings/logs'));
const SettingsServicePage = lazy(() => import('@/pages/settings/service'));
const SettingsRightsPage = lazy(() => import('@/pages/settings/rights'));
const SettingsDnsPage = lazy(() => import('@/pages/settings/dns'));
const SettingsApiPage = lazy(() => import('@/pages/settings/api'));
const SettingsRelayPage = lazy(() => import('@/pages/settings/relay'));
const SettingsMfaPage = lazy(() => import('@/pages/settings/mfa'));
const SettingsSensitivePage = lazy(() => import('@/pages/settings/sensitive'));
const SettingsConnectorPage = lazy(() => import('@/pages/settings/connector'));

const DownloadPage = lazy(() => import('@/pages/download'));
// const DocsPage = lazy(() => import('@/pages/docs'));
const StartPage = lazy(() => import('@/pages/start'));

const SrvPage = lazy(() => import('@/pages/srv'));
const SrvRoutingPoolPage = lazy(() => import('@/pages/srv/networking/pool'));
const SrvGroupPage = lazy(() => import('@/pages/srv/group'));
const SrvGroupDetailPage = lazy(() => import('@/pages/srv/group/group-detail'));
const SrvRoutingTable = lazy(() => import('@/pages/srv/networking/routing-table'));

const ResourcesPage = lazy(() => import('@/pages/resources'));

const RenderRouter: FC = () => {
    const routeList: RouteObject[] = [
        {
            path: BASE_PATH,
            element: <WrapperRouteComponent element={<LayoutPage />} title="" auth />,
            children: [
                {
                    element: <WrapperPageComponent element={<OverviewPage />} title="pages.overview" />,
                    index: true
                },
                {
                    path: 'overview',
                    element: <WrapperPageComponent element={<OverviewPage />} title="pages.overview" />
                },
                {
                    path: 'dashboard',
                    element: <WrapperPageComponent element={<DashboardPage />} title='pages.dashboard' />
                },
                {
                    path: 'dashboard/monitor-device',
                    element: <WrapperPageComponent element={<DashboardMonitorDevice />} title='pages.deviceMonitor' />
                },
                {
                    path: 'dashboard/monitor-interface',
                    element: <WrapperPageComponent element={<DashboardMonitorInterface />} title='pages.physicalInterface' />
                },
                {
                    path: 'dashboard/device-info',
                    element: <WrapperPageComponent element={<DashboardDeviceInfo />} title='pages.deviceManagement' />
                },
                {
                    path: 'dashboard/device-network',
                    element: <WrapperPageComponent element={<DashboardDeviceNetwork />} title='pages.networkTopology' />
                },
                {
                    path: 'networks',
                    element: <WrapperPageComponent element={<NetworksPage />} title="pages.networks" />
                },
                {
                    path: 'devices',
                    element: <WrapperPageComponent element={<DevicesPage />} title="pages.devices" />
                },
                {
                    path: 'devices/:name',
                    element: <WrapperPageComponent element={<DeviceDetailPage />} title="pages.deviceDetail" />
                },
                {
                    path: 'devices/:name/logs',
                    element: <WrapperPageComponent element={<DeviceLogsPage />} title="pages.deviceLogs" />
                },
                {
                    path: 'devices/:name/logs/network-flow',
                    element: <WrapperPageComponent element={<DeviceLogsPage />} title="pages.deviceNetworkLogs" />
                },
                {
                    path: 'devices/group',
                    element: <WrapperPageComponent element={<DeviceGroup />} title='pages.deviceGroup' />
                },
                {
                    path: 'devices/import',
                    element: <WrapperPageComponent element={<DeviceImportPage />} title="pages.deviceImport" />
                },
                {
                    path: 'devices/import/:id',
                    element: <WrapperPageComponent element={<DeviceImportDetailPage />} title="pages.deviceImportDetail" />
                },
                {
                    path: 'devices/export',
                    element: <WrapperPageComponent element={<DeviceExportPage />} title="pages.deviceExport" />
                },
                {
                    path: 'settings',
                    element: <WrapperPageComponent element={<SettingsPage />} title="pages.settings" />,
                    children: [
                        {
                            element: <SettingsDevicePage />,
                            index: true
                        },
                        {
                            path: 'general',
                            element: <SettingsGeneralPage />,
                        },
                        {
                            path: 'user',
                            element: <SettingsUserPage />
                        },
                        {
                            path: 'device',
                            element: <SettingsDevicePage />,
                            index: true
                        },
                        {
                            path: 'keys',
                            element: <SettingsKeysPage></SettingsKeysPage>
                        },
                        {
                            path: 'flynet',
                            element: <SettingsFlynetPage></SettingsFlynetPage>
                        },
                        {
                            path: 'client',
                            element: <SettingsClientPage></SettingsClientPage>
                        },
                        {
                            path: 'schema',
                            element: <SettingsSchemaPage></SettingsSchemaPage>
                        },
                        {
                            path: 'logs',
                            element: <SettingsLogsPage></SettingsLogsPage>
                        },
                        {
                            path: 'import',
                            element: <SettingsImportPage></SettingsImportPage>
                        },
                        {
                            path: 'import/sangfor',
                            element: <SettingsImportSangforPage></SettingsImportSangforPage>
                        },
                        {
                            path: 'import/sangfor/user',
                            element: <SettingsImportSangforUserPage></SettingsImportSangforUserPage>
                        },
                        {
                            path: 'import/sangfor/service',
                            element: <SettingsImportSangforServicePage></SettingsImportSangforServicePage>
                        },
                        {
                            path: 'import/sangfor/acl',
                            element: <SettingsImportSangforAclPage></SettingsImportSangforAclPage>
                        },
                        {
                            path: 'datasource',
                            element: <SettingsDatasourcePage></SettingsDatasourcePage>
                        },
                        {
                            path: 'application',
                            element: <SettingsApplicationPage></SettingsApplicationPage>
                        },
                        {
                            path: 'service',
                            element: <SettingsServicePage />
                        },
                        {
                            path: 'rights',
                            element: <SettingsRightsPage />
                        },
                        {
                            path: 'dns',
                            element: <SettingsDnsPage />
                        },
                        {
                            path: 'api',
                            element: <SettingsApiPage />
                        },
                        {
                            path: 'relay',
                            element: <SettingsRelayPage />
                        },
                        {
                            path: 'mfa',
                            element: <SettingsMfaPage />
                        },
                        {
                            path: 'sensitive',
                            element: <SettingsSensitivePage />
                        },
                        {
                            path: 'connector',
                            element: <SettingsConnectorPage />
                        }
                    ]
                },
                {
                    path: 'users',
                    element: <WrapperPageComponent element={<UsersPage />} title="pages.users" />
                },
                {
                    path: 'users/:name',
                    element: <WrapperPageComponent element={<UsersDetailPage />} title="pages.userDetail" />
                },
                {
                    path: 'users/group/',
                    element: <WrapperPageComponent element={<UsersGroupPage />} title="pages.userGroup" />
                },
                {
                    path: 'users/group/:id',
                    element: <WrapperPageComponent element={<UsersGroupDetailPage />} title="pages.userGroupDetail" />
                },
                {
                    path: 'users/import',
                    element: <WrapperPageComponent element={<UsersImportPage />} title="pages.userImport" />
                },
                {
                    path: 'users/import/:id',
                    element: <WrapperPageComponent element={<UsersImportDetailPage />} title="pages.userImportDetail" />
                },
                {
                    path: 'users/export',
                    element: <WrapperPageComponent element={<UsersExportPage />} title="pages.userExport" />
                },
                {
                    path: 'users/export/:id',
                    element: <WrapperPageComponent element={<UsersExportDetailPage />} title="pages.userExportDetail" />
                },
                {
                    path: 'settings/datasource/users/import',
                    element: <WrapperPageComponent element={<SettingsUsersImportPage />} title="pages.settingsUserImport" />
                },
                {
                    path: 'settings/datasource/users/import/sangfor',
                    element: <WrapperPageComponent element={<SettingsImportSangforUserPage></SettingsImportSangforUserPage>} title="pages.sangforUserImport" />
                },
                {
                    path: 'settings/datasource/users/import/oidc',
                    element: <WrapperPageComponent element={<SettingsUsersImportOidcPage />} title="pages.oidcImport" />
                },
                {
                    path: 'resources',
                    element: <WrapperPageComponent element={<ResourcesPage />} title="pages.resources" />
                },
                {
                    path: 'policies',
                    element: <WrapperPageComponent element={<PoliciesPage />} title="pages.policies" />
                },
                {
                    path: 'policies/acl',
                    element: <WrapperPageComponent element={<PoliciesWrapPage />} title="pages.accessPolicies" />
                },
                {
                    path: 'policies/group/',
                    element: <WrapperPageComponent element={<PoliciesGroupPage />} title="pages.policyGroup" />
                },
                {
                    path: 'policies/advanced',
                    element: <WrapperPageComponent element={<AccessControlsPage />} title="pages.accessControls" />
                },
                {
                    path: 'policies/import',
                    element: <WrapperPageComponent element={<PoliciesImportPage />} title="pages.policyImport" />
                },
                {
                    path: 'policies/import/:id',
                    element: <WrapperPageComponent element={<PoliciesImportDetailPage />} title="pages.policyImportDetail" />
                },
                {
                    path: 'policies/export',
                    element: <WrapperPageComponent element={<PoliciesExportPage />} title="pages.policyExport" />
                },
                {
                    path: 'policies/export/:id',
                    element: <WrapperPageComponent element={<PoliciesExportDetailPage />} title="pages.policyExportDetail" />
                },
                {
                    path: 'settings/datasource/policies/import/sangfor',
                    element: <WrapperPageComponent element={<SettingsImportSangforAclPage></SettingsImportSangforAclPage>} title="pages.sangforPolicyImport" />
                },
                {
                    path: 'settings/datasource/policies/import',
                    element: <WrapperPageComponent element={<SettingsPoliciesImportPage />} title="pages.policyImport" />
                },
                {
                    path: 'policies/admission',
                    element: <WrapperPageComponent element={<PoliciesWrapPage />} title="pages.networkAccessPolicy" />
                },
                {
                    path: 'policies/entrance',
                    element: <WrapperPageComponent element={<PoliciesWrapPage />} title="pages.deviceAccessPolicy" />
                },
                {
                    path: 'policies/entrance/detail/:id',
                    element: <WrapperPageComponent element={<EntranceDetailPage />} title="pages.baselineCheckDetail" />
                },
                {
                    path: 'policies/entrance/baseline-check',
                    element: <WrapperPageComponent element={<EntranceBaselineCheckPage />} title="pages.baselineCheck" />
                },
                {
                    path: 'policies/entrance/baseline-check/detail/:id',
                    element: <WrapperPageComponent element={<EntranceBaselineCheckDetailPage />} title="pages.baselineCheckDetail" />
                },
                {
                    path: 'policies/entrance/app-check',
                    element: <WrapperPageComponent element={<EntranceAppCheckPage />} title="pages.appCheck" />
                },
                {
                    path: 'policies/entrance/app-check/detail/:id',
                    element: <WrapperPageComponent element={<EntranceAppCheckDetailPage />} title="pages.appCheckDetail" />
                },
                {
                    path: 'policies/entrance/process-check',
                    element: <WrapperPageComponent element={<EntranceProcessCheckPage />} title="pages.processCheck" />
                },
                {
                    path: 'policies/entrance/process-check/detail/:id',
                    element: <WrapperPageComponent element={<EntranceProcessCheckDetailPage />} title="pages.processCheckDetail" />
                },
                {
                    path: 'services',
                    element: <WrapperPageComponent element={<ServicesPage />} title="pages.services" />
                },
                {
                    path: 'services/:id',
                    element: <WrapperPageComponent element={<ServicesDetailPage />} title="pages.serviceDetail" />
                },
                {
                    path: 'services/group/',
                    element: <WrapperPageComponent element={<ServicesGroupPage />} title="pages.serviceGroup" />
                },
                {
                    path: 'services/group/:id',
                    element: <WrapperPageComponent element={<ServiceGroupDetailPage />} title="pages.serviceGroupDetail" />
                },
                {
                    path: 'services/devices/',
                    element: <WrapperPageComponent element={<ServicesDevicesPage />} title="pages.autoDiscoveredServices" />
                },
                {
                    path: 'services/import/sangfor',
                    element: <WrapperPageComponent element={<SettingsImportSangforServicePage></SettingsImportSangforServicePage>} title="pages.sangforServiceImport" />
                },
                {
                    path: 'services/import',
                    element: <WrapperPageComponent element={<ServicesImportPage />} title="pages.serviceImport" />
                },
                {
                    path: 'services/application',
                    element: <WrapperPageComponent element={<ServiceApplicationPage />} title="pages.applicationSettings" />
                },
                {
                    path: 'srv',
                    element: <WrapperPageComponent element={<SrvPage />} title="pages.services" />
                },
                {
                    path: 'srv/app',
                    element: <WrapperPageComponent element={<SrvPage />} title="pages.applicationServices" />
                },
                {
                    path: 'srv/app/group',
                    element: <WrapperPageComponent element={<SrvGroupPage />} title="pages.applicationGroup" />
                },
                {
                    path: 'srv/app/group/:id',
                    element: <WrapperPageComponent element={<SrvGroupDetailPage />} title="pages.applicationGroupDetail" />
                },
                {
                    path: 'srv/networking',
                    element: <WrapperPageComponent element={<SrvPage />} title="pages.networkServices" />
                },
                {
                    path: 'srv/system',
                    element: <WrapperPageComponent element={<SrvPage />} title="pages.systemServices" />
                },
                {
                    path: 'srv/system/group',
                    element: <WrapperPageComponent element={<SrvGroupPage />} title="pages.systemServiceGroup" />
                },
                {
                    path: 'srv/system/group/:id',
                    element: <WrapperPageComponent element={<SrvGroupDetailPage />} title="pages.systemServiceGroupDetail" />
                },
                {
                    path: 'srv/networking/pool',
                    element: <WrapperPageComponent element={<SrvRoutingPoolPage />} title="pages.routingPool" />
                },
                {
                    path: 'srv/networking/group',
                    element: <WrapperPageComponent element={<SrvGroupPage />} title="pages.networkServiceGroup" />
                },
                {
                    path: 'srv/networking/group/:id',
                    element: <WrapperPageComponent element={<SrvGroupDetailPage />} title="pages.networkServiceGroupDetail" />
                },
                {
                    path: 'srv/networking/routing-table',
                    element: <WrapperPageComponent element={<SrvRoutingTable />} title="pages.routingTable" />
                },
                {
                    path: 'logs',
                    element: <WrapperPageComponent element={<LogsPage />} title="pages.logs" />
                },
                {
                    path: 'logs/audit',
                    element: <WrapperPageComponent element={<LogsPage />} title="pages.configLogs" />
                },
                {
                    path: 'logs/network-flow',
                    element: <WrapperPageComponent element={<LogsPage />} title="pages.networkLogs" />
                },
                {
                    path: 'logs/dns',
                    element: <WrapperPageComponent element={<LogsPage />} title="pages.dnsLogs" />
                },
                {
                    path: 'logs/services',
                    element: <WrapperPageComponent element={<LogsPage />} title="pages.serviceLogs" />
                },
                {
                    path: 'logs/push-config',
                    element: <WrapperPageComponent element={<LogsPushConfigPage />} title="pages.logPushConfig" />
                },
                {
                    path: 'logs/push-config/:id',
                    element: <WrapperPageComponent element={<LogsPushConfigDetailPage />} title="pages.pushConfigDetail" />
                },
                // {
                //     path: 'dns',
                //     element: <WrapperPageComponent element={<DnsPage />} title="pages.dnsResolution" />
                // }
            ]
        },
        {
            path: BASE_PATH + '/welcome',
            element: <WrapperRouteComponent element={<WelcomeLayoutPage />} title="" auth />,
            children: [
                {
                    element: <WrapperPageComponent element={<WelcomePage />} title="" />,
                    index: true
                },
            ]
        },
        {
            path: BASE_PATH + '/guide',
            element: <WrapperRouteComponent element={<WelcomeLayoutPage />} title="" auth />,
            children: [
                {
                    element: <WrapperPageComponent element={<GuidePage />} title="pages.userGuide" />,
                    index: true
                }, {
                    path: 'networking',
                    element: <WrapperPageComponent element={<GuideNetworking />} title="pages.deviceNetworkingGuide" />
                }, {
                    path: 'vpn',
                    element: <WrapperPageComponent element={<GuideVpn />} title="pages.vpnReplacementGuide" />
                }, {
                    path: 'intranet-penetration',
                    element: <WrapperPageComponent element={<GuideIntranetPenetration />} title="pages.intranetPenetrationGuide" />

                }]
        },
        {
            path: BASE_PATH + '/flynet-selector',
            element: <WrapperRouteComponent element={<WelcomeLayoutPage />} title="" auth />,
            children: [
                {
                    element: <WrapperPageComponent element={<FlynetSelectorPage />} title="pages.selectNetwork" />,
                    index: true
                }
            ]
        },
        {
            path: BASE_PATH + '/trial',
            element: <WrapperRouteComponent element={<WelcomeLayoutPage />} title="" />,
            children: [
                {
                    element: <WrapperPageComponent element={<TrialPage />} title="pages.trialDescription" />,
                    index: true
                },
                {
                    path: 'personal',
                    element: <WrapperPageComponent element={<TrialPersonalPage />} title="pages.personalTrial" />
                },
                {
                    path: 'team',
                    element: <WrapperPageComponent element={<TrialTeamPage />} title="pages.teamTrial" />
                }
            ]
        },
        {
            path: BASE_PATH + '/error/:type?',
            element: <WrapperRouteWithOutLayoutComponent element={<ErrorPage />} title="pages.error" />
        },
        {
            path: BASE_PATH + '/download/:platformName?',
            element: <WrapperRouteComponent element={<LayoutSimplePage />} auth title="pages.download" />,
            children: [{
                element: <WrapperPageComponent element={<DownloadPage />} title='pages.download' />,
                index: true
            }]
        },
        {
            path: BASE_PATH + '/start',
            element: <WrapperRouteComponent element={<LayoutSimplePage />} auth title="pages.applicationPanel" />,
            children: [{
                element: <WrapperPageComponent element={<StartPage />} title='pages.applicationPanel' />,
                index: true
            }]
        },
        // {
        //     path: BASE_PATH + '/docs',
        //     element: <WrapperRouteWithOutLayoutComponent element={<DocsPage />} title="pages.docs" />
        // },
        {
            path: '*',
            element: (
                <WrapperRouteWithOutLayoutComponent
                    element={<Error title="pages.notFound" description="pages.notFoundDescription" type="404" />}
                    title="404"
                />
            )
        }
    ];
    const element = useRoutes(routeList)
    return element
}

export default RenderRouter
