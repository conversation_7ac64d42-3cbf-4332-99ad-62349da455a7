import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react-swc'
// import SemiPlugin from "vite-plugin-semi-theme";

import {viteStaticCopy} from 'vite-plugin-static-copy';

import { resolve } from 'path'
// https://vitejs.dev/config/
export default defineConfig({
  base: '/mesh',

  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
    }
  },
  plugins: [
    react(),    
    viteStaticCopy({
      targets: [
        {
          src: 'node_modules/monaco-editor/min/vs',
          dest: 'public'
        }
      ]
    })
    // SemiPlugin({
    //   theme: "@semi-bot/semi-theme-feiyue-cloud"
    // }),
  ],
  server: {
    base: '/mesh',
    proxy: {
      '/api/': {
        "target": "https://dev.sandbox.feiyue.cloud/", // 服务端域名
        "changeOrigin": true, // 允许域名进行转换
        "headers": { "Cookie": "Hm_lvt_991451d4e71853fb3e6dbeb77dc47473=1735392173,1735614576; Hm_lvt_942e5619ea4c0fd13d0531688f8a0e1f=1749517691,1751638615; dev_sandbox_feiyue_cloud_session_continuity=MTc1MjA0NDY4NXxEdi1CQkFFQ180SUFBUkFCRUFBQUJQLUNBQUE9fCtWBHg9b67v4-Yp7uEuJFJglVnEpLg3aDe5dZl5zxSH; csrf_token_f9f405b5391baab9e94e5290179b20bbf404673ef36f6d656dfaa0e7513afa71=OCklZpS9NkxAANxRkZmheAACESsj10mtceTFRgn4Pnc=; dev_sandbox_feiyue_cloud_session=MTc1MjA0NDY4Nnx6XzAzbW10b29iSU03X1VqTDhOb0otZkZzNzJUd1ZEU29XYmgyd3lNUUxRWS14djRNUlRPU01NSnctNi1YSzFRcVJycHRnc2phWnFnM0VEWjhHaVYycVA0MHJGMGdRSC0zRlVfXzhKQjhXRUp6Tm5pN0JQWXR6ZTdIZjVwNC1EOEZFUldWNXlIcTJIRUxJOFgyNDd2a3ZsR1pxZUdTczlJajQtTl92bm5aeGRQSzdwTDUybHZ0cTR0OGNkVmRGZGgzUzFwekhvR3VVT3JFT1M0ZVNRb2pQdDM1MFVmMnc9PXww_13v6M1ABAOfwT8YzttkcxVJV0SlE4KcSTFEtBQj_A==; dev_sandbox_feiyue_cloud_session=MTc1MjA0NDY4Nnx2NUJZeDJJWGhsVjdRamxPUEVQVF9yRmJJRk9kME9MaUJqdUcyMnBpQWIyYUQyNTlGaXFSUWJ1SzM1d0NMNWZGbC1IVThvTmUzcndYWkpKNmVQb2JNN3hGM2w3NmdjQlhhRFc0bGlNdU5yaG9aVFZQekxuc1pGaGQ5RXlMd1BHNXo1bExlX2FjdmVNOFN6aGxoQ0FpRDFlR0lsRHVqYUpfR2FGU0tYTG9HYnJpQ0NCcXpCUjgtMFJWYUJnalNDSVZZR2FkNFUtS2ZFd1hDb1J4TmNaWkd3dUd3R1NlTlE9PXx10hDb0btaRrD6eB9GvEDLACXWt6bYBciRedFyV-CHXg==" },
      },
      // '/api/': {
      //   "target": "http://localhost:8080/", // 服务端域名
      //   changeOrigin: true,
      //   secure: false, // Set to true if your API uses HTTPS
      //   rewrite: (path) => path.replace(/^\/api\/mesh/, ''),
      //   configure: (proxy, options) => {
      //     proxy.on('proxyReq', (proxyReq, req, res) => {
      //       proxyReq.setHeader('Authorization', `Bearer RXwrieAYFJyY_DbLlTElUcaBLUZdaIotRlC`)
      //       proxyReq.setHeader('Accept', 'application/json')
      //     })
      //   }
      // },
      '/auth/sessions/whoami': {
        "target": "https://dev.sandbox.feiyue.cloud/", // 服务端域名
        "changeOrigin": true, // 允许域名进行转换
        "headers": { "Cookie": "Hm_lvt_991451d4e71853fb3e6dbeb77dc47473=1735392173,1735614576; Hm_lvt_942e5619ea4c0fd13d0531688f8a0e1f=1749517691,1751638615; dev_sandbox_feiyue_cloud_session_continuity=MTc1MjA0NDY4NXxEdi1CQkFFQ180SUFBUkFCRUFBQUJQLUNBQUE9fCtWBHg9b67v4-Yp7uEuJFJglVnEpLg3aDe5dZl5zxSH; csrf_token_f9f405b5391baab9e94e5290179b20bbf404673ef36f6d656dfaa0e7513afa71=OCklZpS9NkxAANxRkZmheAACESsj10mtceTFRgn4Pnc=; dev_sandbox_feiyue_cloud_session=MTc1MjA0NDY4Nnx6XzAzbW10b29iSU03X1VqTDhOb0otZkZzNzJUd1ZEU29XYmgyd3lNUUxRWS14djRNUlRPU01NSnctNi1YSzFRcVJycHRnc2phWnFnM0VEWjhHaVYycVA0MHJGMGdRSC0zRlVfXzhKQjhXRUp6Tm5pN0JQWXR6ZTdIZjVwNC1EOEZFUldWNXlIcTJIRUxJOFgyNDd2a3ZsR1pxZUdTczlJajQtTl92bm5aeGRQSzdwTDUybHZ0cTR0OGNkVmRGZGgzUzFwekhvR3VVT3JFT1M0ZVNRb2pQdDM1MFVmMnc9PXww_13v6M1ABAOfwT8YzttkcxVJV0SlE4KcSTFEtBQj_A==; dev_sandbox_feiyue_cloud_session=MTc1MjA0NDY4Nnx2NUJZeDJJWGhsVjdRamxPUEVQVF9yRmJJRk9kME9MaUJqdUcyMnBpQWIyYUQyNTlGaXFSUWJ1SzM1d0NMNWZGbC1IVThvTmUzcndYWkpKNmVQb2JNN3hGM2w3NmdjQlhhRFc0bGlNdU5yaG9aVFZQekxuc1pGaGQ5RXlMd1BHNXo1bExlX2FjdmVNOFN6aGxoQ0FpRDFlR0lsRHVqYUpfR2FGU0tYTG9HYnJpQ0NCcXpCUjgtMFJWYUJnalNDSVZZR2FkNFUtS2ZFd1hDb1J4TmNaWkd3dUd3R1NlTlE9PXx10hDb0btaRrD6eB9GvEDLACXWt6bYBciRedFyV-CHXg==" },
      },
      '/auth/self-service/logout/browser': {
        "target": "https://dev.sandbox.feiyue.cloud/", // 服务端域名
        "changeOrigin": true, // 允许域名进行转换
      },
      '/auth/self-service/logout': {
        "target": "https://dev.sandbox.feiyue.cloud/", // 服务端域名
        "changeOrigin": true, // 允许域名进行转换
      },
    }
  }
})