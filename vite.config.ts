import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react-swc'
// import SemiPlugin from "vite-plugin-semi-theme";

import {viteStaticCopy} from 'vite-plugin-static-copy';

import { resolve } from 'path'
// https://vitejs.dev/config/
export default defineConfig({
  base: '/mesh',

  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
    }
  },
  plugins: [
    react(),    
    viteStaticCopy({
      targets: [
        {
          src: 'node_modules/monaco-editor/min/vs',
          dest: 'public'
        }
      ]
    })
    // SemiPlugin({
    //   theme: "@semi-bot/semi-theme-feiyue-cloud"
    // }),
  ],
  server: {
    base: '/mesh',
    proxy: {
      // '/api/': {
      //   "target": "https://dev.sandbox.feiyue.cloud/", // 服务端域名
      //   "changeOrigin": true, // 允许域名进行转换
      //   "headers": { "Cookie": "Hm_lvt_991451d4e71853fb3e6dbeb77dc47473=1735392173,1735614576; Hm_lvt_942e5619ea4c0fd13d0531688f8a0e1f=1751638615,1753846873; dev_sandbox_feiyue_cloud_session_continuity=MTc1NDI3MzQ1OHxEdi1CQkFFQ180SUFBUkFCRUFBQUJQLUNBQUE9fJ2sK2ru6K3rvXWa-0eHA_mnKkSGF0bhtrUDZgVkszKs; csrf_token_f9f405b5391baab9e94e5290179b20bbf404673ef36f6d656dfaa0e7513afa71=HgvlmrcUuhoOLJoQhfjRQ8c2LMSxii2g59Yp/JnK6Mw=; dev_sandbox_feiyue_cloud_session=MTc1NDI3MzQ2MHx3UERvSnJIX2NuMVpXTW1CakhVdG81ZGFvaTh5VUFDZjZfNndFWGZaVjhpbl9TWFNISXlZTXViVTZ5eFZ1VHhrSnVzOU5LanprcTB4bF9HNC1ONHVPTUl1b2NCRElnY2pDdjNDaVJ5bXoyUlhWWnJkNVpYbFo0ZlpBZWpRbDZjalNQQ3BqbjEyR1hnT0ktaHBjMHVGeXY2SDM0NF9WUVlJYVAwWk1FelJFLTJ0anpxVXZCLXpzYkJWZWZ1Z3NVY1BEOGNMZm5fSzFBb1BMWGM9fHmAskUcLWF27WAzIoa_Vy_T4BXebmH8yDq8d0exYz_1; dev_sandbox_feiyue_cloud_session=MTc1NDI3MzQ2MHxiNFhUVWdjWkJqbVdPVDB6MGl0eS01X3J4SlQ1SE5VQU5WdWJsUVJ2YzBnTEU3Y29HQmZoTy11T3hKOGJad3E5cHNJZldVZTlhT2JRS1BfVWlmcVN2U3FiUzFkUGxYdDVXUG5iZ0k1NEJUTzlNV2hYZHFPUTR3bHdsOEFOd1JOYkNkRkNseThKVjdpZTZzNlZzdXAwaUxJX1FTdXphaUZ4R1NDM1hmaHExNTRhWkVDMjBqRnVvOER1RmJSVTdIWmJWWUN2eTVpNy0zRXhqMUE9fF1IFKW8mEiIAz-C1Qkc7nGBdhCAGhpicocfJA2zSm2h" },
      // },
      '/api/': {
        "target": "http://localhost:8080/", // 服务端域名
        changeOrigin: true,
        secure: false, // Set to true if your API uses HTTPS
        rewrite: (path) => path.replace(/^\/api\/mesh/, ''),
        configure: (proxy, options) => {
          proxy.on('proxyReq', (proxyReq, req, res) => {
            proxyReq.setHeader('Authorization', `Bearer RXwrieAYFJyY_DbLlTElUcaBLUZdaIotRlC`)
            proxyReq.setHeader('Accept', 'application/json')
          })
        }
      },
      '/auth/sessions/whoami': {
        "target": "https://dev.sandbox.feiyue.cloud/", // 服务端域名
        "changeOrigin": true, // 允许域名进行转换
        "headers": { "Cookie": "Hm_lvt_991451d4e71853fb3e6dbeb77dc47473=1735392173,1735614576; Hm_lvt_942e5619ea4c0fd13d0531688f8a0e1f=1751638615,1753846873; dev_sandbox_feiyue_cloud_session_continuity=MTc1NDI3MzQ1OHxEdi1CQkFFQ180SUFBUkFCRUFBQUJQLUNBQUE9fJ2sK2ru6K3rvXWa-0eHA_mnKkSGF0bhtrUDZgVkszKs; csrf_token_f9f405b5391baab9e94e5290179b20bbf404673ef36f6d656dfaa0e7513afa71=HgvlmrcUuhoOLJoQhfjRQ8c2LMSxii2g59Yp/JnK6Mw=; dev_sandbox_feiyue_cloud_session=MTc1NDI3MzQ2MHx3UERvSnJIX2NuMVpXTW1CakhVdG81ZGFvaTh5VUFDZjZfNndFWGZaVjhpbl9TWFNISXlZTXViVTZ5eFZ1VHhrSnVzOU5LanprcTB4bF9HNC1ONHVPTUl1b2NCRElnY2pDdjNDaVJ5bXoyUlhWWnJkNVpYbFo0ZlpBZWpRbDZjalNQQ3BqbjEyR1hnT0ktaHBjMHVGeXY2SDM0NF9WUVlJYVAwWk1FelJFLTJ0anpxVXZCLXpzYkJWZWZ1Z3NVY1BEOGNMZm5fSzFBb1BMWGM9fHmAskUcLWF27WAzIoa_Vy_T4BXebmH8yDq8d0exYz_1; dev_sandbox_feiyue_cloud_session=MTc1NDI3MzQ2MHxiNFhUVWdjWkJqbVdPVDB6MGl0eS01X3J4SlQ1SE5VQU5WdWJsUVJ2YzBnTEU3Y29HQmZoTy11T3hKOGJad3E5cHNJZldVZTlhT2JRS1BfVWlmcVN2U3FiUzFkUGxYdDVXUG5iZ0k1NEJUTzlNV2hYZHFPUTR3bHdsOEFOd1JOYkNkRkNseThKVjdpZTZzNlZzdXAwaUxJX1FTdXphaUZ4R1NDM1hmaHExNTRhWkVDMjBqRnVvOER1RmJSVTdIWmJWWUN2eTVpNy0zRXhqMUE9fF1IFKW8mEiIAz-C1Qkc7nGBdhCAGhpicocfJA2zSm2h" },
      },
      '/auth/self-service/logout/browser': {
        "target": "https://dev.sandbox.feiyue.cloud/", // 服务端域名
        "changeOrigin": true, // 允许域名进行转换
      },
      '/auth/self-service/logout': {
        "target": "https://dev.sandbox.feiyue.cloud/", // 服务端域名
        "changeOrigin": true, // 允许域名进行转换
      },
    }
  }
})